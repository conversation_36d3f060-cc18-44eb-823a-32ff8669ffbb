class MetaModel {
  final String? id;
  final String nome;
  final String nomeMeta;
  final bool flgMensal;
  final bool flgAtingirValor;
  final int qtdMeses;
  final DateTime? dtVencimentoMeta;
  final DateTime? dataVencimento;
  final DateTime? dataConclusao;
  final String iconeMeta;
  final String corMeta;
  final int valorMeta;
  final double valorObjetivo;
  final double valorAtual;
  final String status;
  final String categoria;
  final bool flgAtivo;
  final DateTime? dtaCadastro;

  MetaModel({
    this.id,
    required this.nome,
    required this.nomeMeta,
    this.flgMensal = false,
    this.flgAtingirValor = false,
    this.qtdMeses = 1,
    this.dtVencimentoMeta,
    this.dataVencimento,
    this.dataConclusao,
    this.iconeMeta = '',
    this.corMeta = '',
    this.valorMeta = 0,
    this.valorObjetivo = 0,
    this.valorAtual = 0,
    this.status = 'Em Andamento',
    this.categoria = '',
    this.flgAtivo = true,
    this.dtaCadastro,
  });

  factory MetaModel.fromJson(Map<String, dynamic> json) {
    return MetaModel(
      id: json['id'],
      nome: json['nome'] ?? '',
      nomeMeta: json['nomeMeta'] ?? '',
      flgMensal: json['flgMensal'] ?? false,
      flgAtingirValor: json['flgAtingirValor'] ?? false,
      qtdMeses: json['qtdMeses'] ?? 1,
      dtVencimentoMeta: json['dtVencimentoMeta'] != null 
          ? DateTime.parse(json['dtVencimentoMeta']) 
          : null,
      dataVencimento: json['dataVencimento'] != null 
          ? DateTime.parse(json['dataVencimento']) 
          : null,
      dataConclusao: json['dataConclusao'] != null 
          ? DateTime.parse(json['dataConclusao']) 
          : null,
      iconeMeta: json['iconeMeta'] ?? '',
      corMeta: json['corMeta'] ?? '',
      valorMeta: json['valorMeta'] ?? 0,
      valorObjetivo: (json['valorObjetivo'] as num?)?.toDouble() ?? 0,
      valorAtual: (json['valorAtual'] as num?)?.toDouble() ?? 0,
      status: json['status'] ?? 'Em Andamento',
      categoria: json['categoria'] ?? '',
      flgAtivo: json['flgAtivo'] ?? true,
      dtaCadastro: json['dtaCadastro'] != null 
          ? DateTime.parse(json['dtaCadastro']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nome': nome,
      'nomeMeta': nomeMeta,
      'flgMensal': flgMensal,
      'flgAtingirValor': flgAtingirValor,
      'qtdMeses': qtdMeses,
      'dtVencimentoMeta': dtVencimentoMeta?.toIso8601String(),
      'dataVencimento': dataVencimento?.toIso8601String(),
      'dataConclusao': dataConclusao?.toIso8601String(),
      'iconeMeta': iconeMeta,
      'corMeta': corMeta,
      'valorMeta': valorMeta,
      'valorObjetivo': valorObjetivo,
      'valorAtual': valorAtual,
      'status': status,
      'categoria': categoria,
      'flgAtivo': flgAtivo,
      'dtaCadastro': dtaCadastro?.toIso8601String(),
    };
  }

  // Métodos auxiliares para cálculos
  double get progresso {
    if (valorObjetivo <= 0) return 0;
    return (valorAtual / valorObjetivo).clamp(0.0, 1.0);
  }

  double get progressoPercentual => progresso * 100;

  double get valorRestante => (valorObjetivo - valorAtual).clamp(0.0, double.infinity);

  bool get isCompleta => valorAtual >= valorObjetivo;

  bool get isVencida {
    if (dataVencimento == null) return false;
    return DateTime.now().isAfter(dataVencimento!) && !isCompleta;
  }

  int get diasRestantes {
    if (dataVencimento == null) return 0;
    final diferenca = dataVencimento!.difference(DateTime.now()).inDays;
    return diferenca > 0 ? diferenca : 0;
  }

  String get statusFormatado {
    if (isCompleta) return 'Concluída';
    if (isVencida) return 'Vencida';
    return status;
  }

  // Conversão para o formato usado no goals_list_screen.dart
  Map<String, dynamic> toGoalFormat() {
    return {
      'id': id,
      'name': nomeMeta.isNotEmpty ? nomeMeta : nome,
      'description': nome,
      'targetAmount': valorObjetivo,
      'currentAmount': valorAtual,
      'targetDate': dataVencimento ?? dtVencimentoMeta,
      'icon': iconeMeta.isNotEmpty ? iconeMeta : '🎯',
      'color': _parseColor(corMeta),
      'isMonthly': flgMensal,
      'categories': [categoria],
      'status': statusFormatado,
      'progress': progresso,
      'daysRemaining': diasRestantes,
      'isCompleted': isCompleta,
      'isOverdue': isVencida,
    };
  }

  int _parseColor(String cor) {
    if (cor.isEmpty) return 0xFF4ECDC4; // Cor padrão
    
    try {
      // Remove # se presente
      String colorString = cor.replaceAll('#', '');
      
      // Adiciona FF para opacidade total se necessário
      if (colorString.length == 6) {
        colorString = 'FF$colorString';
      }
      
      return int.parse(colorString, radix: 16);
    } catch (e) {
      return 0xFF4ECDC4; // Cor padrão em caso de erro
    }
  }

  MetaModel copyWith({
    String? id,
    String? nome,
    String? nomeMeta,
    bool? flgMensal,
    bool? flgAtingirValor,
    int? qtdMeses,
    DateTime? dtVencimentoMeta,
    DateTime? dataVencimento,
    DateTime? dataConclusao,
    String? iconeMeta,
    String? corMeta,
    int? valorMeta,
    double? valorObjetivo,
    double? valorAtual,
    String? status,
    String? categoria,
    bool? flgAtivo,
    DateTime? dtaCadastro,
  }) {
    return MetaModel(
      id: id ?? this.id,
      nome: nome ?? this.nome,
      nomeMeta: nomeMeta ?? this.nomeMeta,
      flgMensal: flgMensal ?? this.flgMensal,
      flgAtingirValor: flgAtingirValor ?? this.flgAtingirValor,
      qtdMeses: qtdMeses ?? this.qtdMeses,
      dtVencimentoMeta: dtVencimentoMeta ?? this.dtVencimentoMeta,
      dataVencimento: dataVencimento ?? this.dataVencimento,
      dataConclusao: dataConclusao ?? this.dataConclusao,
      iconeMeta: iconeMeta ?? this.iconeMeta,
      corMeta: corMeta ?? this.corMeta,
      valorMeta: valorMeta ?? this.valorMeta,
      valorObjetivo: valorObjetivo ?? this.valorObjetivo,
      valorAtual: valorAtual ?? this.valorAtual,
      status: status ?? this.status,
      categoria: categoria ?? this.categoria,
      flgAtivo: flgAtivo ?? this.flgAtivo,
      dtaCadastro: dtaCadastro ?? this.dtaCadastro,
    );
  }
}
