﻿using RepositoryClient.Interfaces.Generic;
using Shared.Entities.Client;

namespace RepositoryClient.Interfaces
{
    /// <summary>
    /// Interface para repositório de logs de erro no contexto Client (multi-tenant)
    /// </summary>
    public interface ILogErroClientRepository : IGenericClientRepository<LogErroClient>
    {
        /// <summary>
        /// Busca logs de erro por usuário
        /// </summary>
        /// <param name="idUsuario">ID do usuário</param>
        /// <returns>Lista de logs de erro do usuário</returns>
        Task<IEnumerable<LogErroClient>> BuscarPorUsuarioAsync(string idUsuario);

        /// <summary>
        /// Busca logs de erro por período
        /// </summary>
        /// <param name="dataInicio">Data de início</param>
        /// <param name="dataFim">Data de fim</param>
        /// <returns>Lista de logs de erro no período</returns>
        Task<IEnumerable<LogErroClient>> BuscarPorPeriodoAsync(DateTime dataInicio, DateTime dataFim);

        /// <summary>
        /// Busca logs de erro por severidade
        /// </summary>
        /// <param name="severidade">Severidade do erro</param>
        /// <returns>Lista de logs de erro com a severidade especificada</returns>
        Task<IEnumerable<LogErroClient>> BuscarPorSeveridadeAsync(string severidade);
    }
}
