﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using RepositoryClient.Interfaces.Generic;
using ServiceClient.Interfaces.Generic;
using Shared.Helpers;
using System.Linq.Expressions;
using System.Security.Claims;


namespace ServiceClient.Repository.Generic
{
    public class GenericClientService<TViewModel, TEntidade>(
        IGenericClientRepository<TEntidade> repository,
        IHttpContextAccessor httpContextAccessor,
        IMapper mapper) :
        ConversorMap<TViewModel, TEntidade>(mapper),
        IGenericClientService<TViewModel, TEntidade>
        where TViewModel : class
        where TEntidade : class
    {
        #region CONSTRUTOR
        private readonly IGenericClientRepository<TEntidade> _repository = repository;
        protected readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;

        public string? IdUsuarioLogado => _httpContextAccessor.HttpContext?.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        public string? IdEmpresaUsuarioLogado => _httpContextAccessor.HttpContext?.User.FindFirst("EmpresaId")?.Value;
        #endregion

        #region CONSULTAS
        public async Task<TViewModel?> BuscarPorIdAsync(string? Id)
        {
            try
            {
                TEntidade? objRetornoEntidade = await _repository.BuscarPorIdAsync(Id);
                TViewModel? objRetornoViewModel = ConverteEntidadeParaViewModel(objRetornoEntidade);
                return objRetornoViewModel;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar por ID '{Id}': {ex.Message}", ex);
            }
        }

        public async Task<IEnumerable<TViewModel?>> BuscarTodosAsync()
        {
            try
            {
                var lstEntidade = await _repository.BuscarTodosAsync();
                return lstEntidade.Select(d => ConverteEntidadeParaViewModel(d)).ToList();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar todos os registros: {ex.Message}", ex);
            }
        }

        public async Task<IEnumerable<TViewModel?>> BuscarPorFiltroAsync(Expression<Func<TEntidade?, bool>> expression)
        {
            try
            {
                var lstEntidade = await _repository.BuscarPorFiltroAsync(expression);
                return lstEntidade.Select(d => ConverteEntidadeParaViewModel(d)).ToList();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar por filtro: {ex.Message}", ex);
            }
        }

        public async Task<IEnumerable<TViewModel?>> BuscarPorFiltroPaginadoAsync(Expression<Func<TEntidade?, bool>> expression, int page, int pageSize)
        {
            try
            {
                var lstEntidade = await _repository.BuscarPorFiltroPaginadoAsync(expression, page, pageSize);
                return lstEntidade.Select(d => ConverteEntidadeParaViewModel(d)).ToList();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar por filtro paginado (página {page}, tamanho {pageSize}): {ex.Message}", ex);
            }
        }

        public async Task<int> BuscarContagemTotalPorFiltroAsync(Expression<Func<TEntidade?, bool>> expression)
        {
            try
            {
                return await _repository.BuscarContagemTotalPorFiltroAsync(expression);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar contagem total por filtro: {ex.Message}", ex);
            }
        }

        public async Task<int> BuscarContagemTotalAsync()
        {
            try
            {
                return await _repository.BuscarContagemTotalAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar contagem total: {ex.Message}", ex);
            }
        }

        public async Task<TViewModel?> BuscarPrimeiroPorFiltroAsync(Expression<Func<TEntidade?, bool>> expression)
        {
            try
            {
                var objRetornoEntidade = await _repository.BuscarPrimeiroPorFiltroAsync(expression);
                TViewModel? objRetornoViewModel = ConverteEntidadeParaViewModel(objRetornoEntidade);
                return objRetornoViewModel;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar por filtro: {ex.Message}", ex);
            }
        }
        #endregion

        #region OPERAÇÕES
        public async Task<TViewModel?> AdicionarAsync(TViewModel objViewModel)
        {
            try
            {
                if (objViewModel == null)
                {
                    throw new ArgumentNullException(nameof(objViewModel), "O objeto ViewModel não pode ser nulo.");
                }

                TEntidade? objEntidade = ConverteViewModelParaEntidade(objViewModel) ?? throw new ApplicationException("Erro ao converter ViewModel para Entidade");
                TEntidade? objRetornoEntidade = await _repository.AdicionarAsync(objEntidade);
                TViewModel? objRetornoViewModel = ConverteEntidadeParaViewModel(objRetornoEntidade);
                return objRetornoViewModel;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao adicionar registro: {ex.Message}", ex);
            }
        }

        public async Task<TViewModel?> EditarAsync(TViewModel objViewModel)
        {
            try
            {
                TEntidade? objEntidade = ConverteViewModelParaEntidade(objViewModel);
                return ConverteEntidadeParaViewModel(await _repository.EditarAsync(objEntidade));
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao editar registro: {ex.Message}", ex);
            }
        }

        public async Task ExcluirAsync(TViewModel objViewModel)
        {
            try
            {
                TEntidade? objEntidade = ConverteViewModelParaEntidade(objViewModel);
                await _repository.ExcluirAsync(objEntidade);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao excluir registro: {ex.Message}", ex);
            }
        }

        public async Task<List<TViewModel>?> AdicionarArrayAsync(List<TViewModel> arrayObjViewModel)
        {
            try
            {
                if (arrayObjViewModel == null || arrayObjViewModel.Count == 0)
                    throw new ArgumentNullException(nameof(arrayObjViewModel), "Array não pode ser nulo ou vazio");

                var idProperty = typeof(TEntidade).GetProperty("Id") ?? throw new InvalidOperationException("A entidade não possui uma propriedade 'Id'");

                List<TEntidade> objEntidade = arrayObjViewModel.Select(x => ConverteViewModelParaEntidade(x)).ToList();

                var comId = objEntidade.Where(e =>
                {
                    var id = idProperty.GetValue(e);
                    return id != null && !string.IsNullOrEmpty(id.ToString());
                }).ToList();

                var semId = objEntidade.Where(e =>
                {
                    var id = idProperty.GetValue(e);
                    return id == null || string.IsNullOrEmpty(id.ToString());
                }).ToList();

                var resultados = new List<TEntidade>();

                if (comId.Count != 0)
                {
                    var atualizados = await _repository.EditarArrayAsync(comId);
                    resultados.AddRange(atualizados);
                }

                if (semId.Count != 0)
                {
                    var adicionados = await _repository.AdicionarArrayAsync(semId);
                    resultados.AddRange(adicionados);
                }

                List<TViewModel> objRetornoViewModel = resultados.Select(x => ConverteEntidadeParaViewModel(x)).ToList();
                return objRetornoViewModel;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao adicionar registro: {ex.Message}", ex);
            }
        }

        #endregion

        #region AUXILIARES
        #endregion
    }
}
