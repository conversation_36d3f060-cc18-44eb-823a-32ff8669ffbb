﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.5" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.0.2" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.0.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\RepositoryAdmin\RepositoryAdmin.csproj" />
    <ProjectReference Include="..\RepositoryClient\RepositoryClient.csproj" />
    <ProjectReference Include="..\Shared\Shared.csproj" />
  </ItemGroup>

</Project>
