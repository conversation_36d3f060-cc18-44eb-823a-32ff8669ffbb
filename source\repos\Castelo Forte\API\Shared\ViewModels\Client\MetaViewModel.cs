﻿using Shared.Enums;
using Shared.ViewModels.Base;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Shared.ViewModels.Client
{
    /// <summary>
    /// ViewModel para meta financeira
    /// </summary>
    public class MetaViewModel : BaseViewModel
    {
        /// <summary>
        /// Nome da meta
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Nome { get; set; } = "";

        /// <summary>
        /// Descrição detalhada da meta
        /// </summary>
        [StringLength(500)]
        public string Descricao { get; set; } = "";

        /// <summary>
        /// Data de abertura da meta
        /// </summary>
        public DateTime DataAbertura { get; set; } = DateTime.Now;

        /// <summary>
        /// Data-alvo para conclusão
        /// </summary>
        [Required]
        public DateTime DataConclusao { get; set; }

        /// <summary>
        /// Data real de conclusão
        /// </summary>
        public DateTime? DataConclusaoReal { get; set; }

        /// <summary>
        /// Valor-alvo da meta
        /// </summary>
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "O valor alvo deve ser maior que zero")]
        public decimal ValorAlvo { get; set; } = 0;

        /// <summary>
        /// Progresso atual
        /// </summary>
        public decimal ProgressoAtual { get; set; } = 0;

        /// <summary>
        /// Status da meta
        /// </summary>
        public MetaStatus Status { get; set; } = MetaStatus.Ativa;

        /// <summary>
        /// Descrição do status
        /// </summary>
        public string StatusDescricao => Status.GetDescription();

        /// <summary>
        /// Ícone da meta
        /// </summary>
        public string IconeMeta { get; set; } = "";

        /// <summary>
        /// Cor da meta
        /// </summary>
        public string CorMeta { get; set; } = "#4CAF50";

        /// <summary>
        /// IDs das categorias associadas
        /// </summary>
        public List<string> CategoriasAssociadas { get; set; } = new List<string>();

        /// <summary>
        /// Indica se é meta mensal
        /// </summary>
        public bool IsMetaMensal { get; set; } = false;

        /// <summary>
        /// Percentual de progresso (0-100)
        /// </summary>
        public decimal PercentualProgresso => ValorAlvo > 0 ? Math.Min((ProgressoAtual / ValorAlvo) * 100, 100) : 0;

        /// <summary>
        /// Valor restante para atingir a meta
        /// </summary>
        public decimal ValorRestante => Math.Max(ValorAlvo - ProgressoAtual, 0);

        /// <summary>
        /// Verifica se a meta foi atingida
        /// </summary>
        public bool IsAtingida => ProgressoAtual >= ValorAlvo;

        /// <summary>
        /// Verifica se a meta está vencida
        /// </summary>
        public bool IsVencida => DateTime.Now > DataConclusao && !IsAtingida;

        /// <summary>
        /// Dias restantes para a meta
        /// </summary>
        public int DiasRestantes => Math.Max((DataConclusao - DateTime.Now).Days, 0);

        // Propriedades de compatibilidade com versão anterior (DEPRECATED)
        [Obsolete("Use Nome")]
        public string NomeMeta
        {
            get => Nome;
            set => Nome = value;
        }

        [Obsolete("Use IsMetaMensal")]
        public bool FlgMensal
        {
            get => IsMetaMensal;
            set => IsMetaMensal = value;
        }

        [Obsolete("Use ValorAlvo")]
        public decimal ValorObjetivo
        {
            get => ValorAlvo;
            set => ValorAlvo = value;
        }

        [Obsolete("Use ProgressoAtual")]
        public decimal ValorAtual
        {
            get => ProgressoAtual;
            set => ProgressoAtual = value;
        }
        public DateTime? DtaAlteracao { get; set; }
    }
}
