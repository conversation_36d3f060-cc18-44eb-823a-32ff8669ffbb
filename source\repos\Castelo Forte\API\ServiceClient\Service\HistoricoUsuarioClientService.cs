using AutoMapper;
using Microsoft.AspNetCore.Http;
using RepositoryClient.Interfaces;
using ServiceClient.Interfaces;
using ServiceClient.Repository.Generic;
using Shared.Entities.Client;
using Shared.ViewModels.Client;
using System.Security.Claims;

namespace ServiceClient.Service
{
    /// <summary>
    /// Serviço para histórico de usuário no contexto Client (multi-tenant)
    /// </summary>
    public class HistoricoUsuarioClientService : GenericClientService<HistoricoUsuarioClientViewModel, HistoricoUsuarioClient>, IHistoricoUsuarioClientService
    {
        private readonly IHistoricoUsuarioClientRepository _historicoRepository;

        public HistoricoUsuarioClientService(
            IHistoricoUsuarioClientRepository historicoRepository,
            IHttpContextAccessor httpContextAccessor,
            IMapper mapper) : base(historicoRepository, httpContextAccessor, mapper)
        {
            _historicoRepository = historicoRepository ?? throw new ArgumentNullException(nameof(historicoRepository));
        }

        /// <summary>
        /// Registra uma ação no histórico do usuário
        /// </summary>
        /// <param name="metodo">Método que executou a ação</param>
        /// <param name="acao">Descrição da ação realizada</param>
        /// <param name="dadoAntigo">Dados antes da alteração</param>
        /// <param name="dadoNovo">Dados após a alteração</param>
        /// <param name="tipoOperacao">Tipo da operação (CREATE, READ, UPDATE, DELETE, etc.)</param>
        /// <param name="controller">Controller onde a ação foi executada</param>
        /// <returns>Task</returns>
        public async Task RegistrarAcao(string metodo, string acao, string dadoAntigo, string dadoNovo,
            string tipoOperacao = "", string controller = "")
        {
            try
            {
                var historico = new HistoricoUsuarioClient
                {
                    DtaCadastro = DateTime.UtcNow,
                    Metodo = metodo,
                    Acao = acao,
                    DadoAntigo = dadoAntigo,
                    DadoNovo = dadoNovo,
                    IdUsuario = IdUsuarioLogado,
                    Controller = controller,
                    TipoOperacao = DeterminarTipoOperacao(metodo, tipoOperacao),
                    Status = "SUCCESS",
                    FlgAtivo = true
                };

                await _historicoRepository.AdicionarAsync(historico);
            }
            catch (Exception)
            {
                // Em caso de erro ao registrar o histórico, não queremos quebrar o fluxo principal
                Console.WriteLine($"Erro ao registrar histórico de ação: {metodo} - {acao}");
            }
        }

        /// <summary>
        /// Registra uma ação no histórico do usuário com informações de contexto HTTP
        /// </summary>
        /// <param name="metodo">Método que executou a ação</param>
        /// <param name="acao">Descrição da ação realizada</param>
        /// <param name="dadoAntigo">Dados antes da alteração</param>
        /// <param name="dadoNovo">Dados após a alteração</param>
        /// <param name="tipoOperacao">Tipo da operação</param>
        /// <param name="controller">Controller onde a ação foi executada</param>
        /// <param name="ipCliente">IP do cliente</param>
        /// <param name="userAgent">User Agent do cliente</param>
        /// <param name="duracaoMs">Duração da operação em milissegundos</param>
        /// <param name="status">Status da operação</param>
        /// <param name="informacoesAdicionais">Informações adicionais</param>
        /// <returns>Task</returns>
        public async Task RegistrarAcaoComContexto(string metodo, string acao, string dadoAntigo, string dadoNovo,
            string tipoOperacao = "", string controller = "", string? ipCliente = null,
            string? userAgent = null, long? duracaoMs = null, string status = "SUCCESS",
            string? informacoesAdicionais = null)
        {
            try
            {
                // Obtém informações do contexto HTTP se não foram fornecidas
                if (string.IsNullOrEmpty(ipCliente) && _httpContextAccessor.HttpContext != null)
                {
                    ipCliente = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress?.ToString();
                }

                if (string.IsNullOrEmpty(userAgent) && _httpContextAccessor.HttpContext != null)
                {
                    userAgent = _httpContextAccessor.HttpContext.Request.Headers["User-Agent"].FirstOrDefault();
                }

                var historico = new HistoricoUsuarioClient
                {
                    DtaCadastro = DateTime.UtcNow,
                    Metodo = metodo,
                    Acao = acao,
                    DadoAntigo = dadoAntigo,
                    DadoNovo = dadoNovo,
                    IdUsuario = IdUsuarioLogado,
                    Controller = controller,
                    TipoOperacao = DeterminarTipoOperacao(metodo, tipoOperacao),
                    IpCliente = ipCliente,
                    UserAgent = userAgent,
                    DuracaoMs = duracaoMs,
                    Status = status,
                    InformacoesAdicionais = informacoesAdicionais,
                    FlgAtivo = true
                };

                await _historicoRepository.AdicionarAsync(historico);
            }
            catch (Exception)
            {
                // Em caso de erro ao registrar o histórico, não queremos quebrar o fluxo principal
                Console.WriteLine($"Erro ao registrar histórico de ação com contexto: {metodo} - {acao}");
            }
        }

        /// <summary>
        /// Busca histórico por usuário
        /// </summary>
        /// <param name="idUsuario">ID do usuário</param>
        /// <returns>Lista de histórico do usuário</returns>
        public async Task<IEnumerable<HistoricoUsuarioClientViewModel>> BuscarPorUsuarioAsync(string idUsuario)
        {
            var historicos = await _historicoRepository.BuscarPorUsuarioAsync(idUsuario);
            return historicos.Select(ConverteEntidadeParaViewModel);
        }

        /// <summary>
        /// Busca histórico por período
        /// </summary>
        /// <param name="dataInicio">Data de início</param>
        /// <param name="dataFim">Data de fim</param>
        /// <returns>Lista de histórico no período</returns>
        public async Task<IEnumerable<HistoricoUsuarioClientViewModel>> BuscarPorPeriodoAsync(DateTime dataInicio, DateTime dataFim)
        {
            var historicos = await _historicoRepository.BuscarPorPeriodoAsync(dataInicio, dataFim);
            return historicos.Select(ConverteEntidadeParaViewModel);
        }

        /// <summary>
        /// Busca histórico por tipo de operação
        /// </summary>
        /// <param name="tipoOperacao">Tipo da operação</param>
        /// <returns>Lista de histórico do tipo especificado</returns>
        public async Task<IEnumerable<HistoricoUsuarioClientViewModel>> BuscarPorTipoOperacaoAsync(string tipoOperacao)
        {
            var historicos = await _historicoRepository.BuscarPorTipoOperacaoAsync(tipoOperacao);
            return historicos.Select(ConverteEntidadeParaViewModel);
        }

        /// <summary>
        /// Busca histórico por controller
        /// </summary>
        /// <param name="controller">Nome do controller</param>
        /// <returns>Lista de histórico do controller</returns>
        public async Task<IEnumerable<HistoricoUsuarioClientViewModel>> BuscarPorControllerAsync(string controller)
        {
            var historicos = await _historicoRepository.BuscarPorControllerAsync(controller);
            return historicos.Select(ConverteEntidadeParaViewModel);
        }

        /// <summary>
        /// Determina o tipo de operação baseado no nome do método
        /// </summary>
        /// <param name="metodo">Nome do método</param>
        /// <param name="tipoOperacaoFornecido">Tipo de operação fornecido explicitamente</param>
        /// <returns>Tipo da operação</returns>
        private static string DeterminarTipoOperacao(string metodo, string tipoOperacaoFornecido)
        {
            if (!string.IsNullOrEmpty(tipoOperacaoFornecido))
                return tipoOperacaoFornecido;

            var metodoLower = metodo.ToLower();

            return metodoLower switch
            {
                var m when m.Contains("cadastr") || m.Contains("criar") || m.Contains("adicionar") || m.Contains("post") => "CREATE",
                var m when m.Contains("buscar") || m.Contains("obter") || m.Contains("listar") || m.Contains("get") => "READ",
                var m when m.Contains("editar") || m.Contains("atualizar") || m.Contains("alterar") || m.Contains("put") || m.Contains("patch") => "UPDATE",
                var m when m.Contains("excluir") || m.Contains("deletar") || m.Contains("remover") || m.Contains("delete") => "DELETE",
                var m when m.Contains("login") || m.Contains("autenticar") => "LOGIN",
                var m when m.Contains("logout") || m.Contains("sair") => "LOGOUT",
                var m when m.Contains("validar") || m.Contains("verificar") => "VALIDATE",
                _ => "OTHER"
            };
        }
    }
}
