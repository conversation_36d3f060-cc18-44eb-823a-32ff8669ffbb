using System.ComponentModel.DataAnnotations;

namespace Shared.ViewModels.Client
{
    /// <summary>
    /// ViewModel para categoria de lançamento na dashboard do cliente
    /// </summary>
    public class DashboardCategoriaViewModel
    {
        /// <summary>
        /// Identificador único da categoria
        /// </summary>
        [Required]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Nome da categoria
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Nome { get; set; } = string.Empty;

        /// <summary>
        /// Nome do ícone da categoria
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Icone { get; set; } = string.Empty;

        /// <summary>
        /// Código hexadecimal da cor da categoria (ex: "#FF6B6B")
        /// </summary>
        [Required]
        [StringLength(7)]
        public string Cor { get; set; } = string.Empty;

        /// <summary>
        /// Tipo da categoria ("RECEITA" ou "DESPESA")
        /// </summary>
        [Required]
        [StringLength(10)]
        public string Tipo { get; set; } = string.Empty;
    }
}
