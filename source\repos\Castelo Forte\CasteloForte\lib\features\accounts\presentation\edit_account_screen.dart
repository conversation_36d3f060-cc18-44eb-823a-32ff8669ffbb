import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/utils/navigation_helper.dart';
import '../data/accounts_service.dart';
import '../data/models/account_model.dart';
import '../data/constants/account_constants.dart';
// import 'widgets/icon_selector.dart'; // REMOVIDO - não utilizado
// import 'widgets/color_selector.dart'; // REMOVIDO - não utilizado

class EditAccountScreen extends StatefulWidget {
  final Map<String, dynamic> account;

  const EditAccountScreen({super.key, required this.account});

  @override
  State<EditAccountScreen> createState() => _EditAccountScreenState();
}

class _EditAccountScreenState extends State<EditAccountScreen> {
  final _formKey = GlobalKey<FormState>();
  final _bankNameController = TextEditingController();
  final _nicknameController = TextEditingController();
  final _balanceController = TextEditingController();

  // Novos controladores
  final _agenciaController = TextEditingController();
  final _numeroContaController = TextEditingController();
  final _numeroBancoController = TextEditingController();

  String _selectedAccountType = 'Conta Corrente';
  bool _isActive = true;

  // Novos campos
  String? _selectedIcon = 'account_balance';
  String? _selectedColor = 'blue';
  bool _isContaPj = false;

  List<String> _accountTypes = [];

  @override
  void initState() {
    super.initState();
    _loadAccountTypes();
    _loadAccountData();
  }

  void _loadAccountTypes() {
    _accountTypes = AccountsService.getAccountTypes();
  }

  void _loadAccountData() {
    _bankNameController.text = widget.account['nomeBanco'] ?? '';
    _nicknameController.text = widget.account['apelido'] ?? '';
    _balanceController.text = (widget.account['saldo'] ?? 0.0).toString();

    // Garante que o tipo da conta está na lista disponível
    final accountType = widget.account['tipoConta'] ?? 'Conta Corrente';
    if (_accountTypes.contains(accountType)) {
      _selectedAccountType = accountType;
    } else {
      // Se o tipo não estiver na lista, usa o primeiro disponível
      _selectedAccountType = _accountTypes.isNotEmpty
          ? _accountTypes.first
          : 'Conta Corrente';
    }

    _isActive = widget.account['ativa'] ?? true;

    // Carregar novos campos
    _agenciaController.text = widget.account['agencia'] ?? '';
    _numeroContaController.text = widget.account['numeroConta'] ?? '';
    _numeroBancoController.text = widget.account['numeroBanco'] ?? '';
    _selectedIcon = widget.account['icone'] ?? 'account_balance';
    _selectedColor = widget.account['cor'] ?? 'blue';
    _isContaPj = widget.account['contaPj'] ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => NavigationHelper.safeGoBack(context),
        ),
        title: const Text(
          'Editar Conta',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header informativo
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: const Color(0xFF16213E),
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: Colors.orange.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(Icons.edit, color: Colors.orange, size: 24),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'Edite as informações da sua conta',
                        style: TextStyle(color: Colors.white70),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 30),

              // Nome do banco
              _buildTextField(
                controller: _bankNameController,
                label: 'Nome do Banco/Instituição',
                hint: 'Ex: Nubank, Itaú, Bradesco...',
                icon: Icons.business,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Nome do banco é obrigatório';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),

              // Tipo de conta
              _buildDropdownField(),
              const SizedBox(height: 20),

              // Apelido
              _buildTextField(
                controller: _nicknameController,
                label: 'Apelido',
                hint: 'Ex: Conta Principal, Conta Salário...',
                icon: Icons.label,
              ),
              const SizedBox(height: 20),

              // Saldo atual
              _buildTextField(
                controller: _balanceController,
                label: 'Saldo Atual',
                hint: '0,00',
                icon: Icons.attach_money,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9.,]')),
                ],
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    final amount = double.tryParse(value.replaceAll(',', '.'));
                    if (amount == null) {
                      return 'Valor inválido';
                    }
                  }
                  return null;
                },
              ),
              const SizedBox(height: 30),

              // Seletor de ícone - OCULTO
              // IconSelector(
              //   selectedIcon: _selectedIcon,
              //   onIconSelected: (icon) {
              //     setState(() {
              //       _selectedIcon = icon;
              //     });
              //   },
              // ),
              // const SizedBox(height: 20),

              // Seletor de cor - OCULTO
              // ColorSelector(
              //   selectedColor: _selectedColor,
              //   onColorSelected: (color) {
              //     setState(() {
              //       _selectedColor = color;
              //     });
              //   },
              // ),
              const SizedBox(height: 20),

              // Agência
              _buildTextField(
                controller: _agenciaController,
                label: 'Agência',
                hint: 'Ex: 1234',
                icon: Icons.business,
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              ),
              const SizedBox(height: 20),

              // Número da conta
              _buildTextField(
                controller: _numeroContaController,
                label: 'Número da Conta',
                hint: 'Ex: 12345-6',
                icon: Icons.account_balance,
              ),
              const SizedBox(height: 20),

              // Número do banco
              _buildTextField(
                controller: _numeroBancoController,
                label: 'Número do Banco',
                hint: 'Ex: 001',
                icon: Icons.account_balance_wallet,
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              ),
              const SizedBox(height: 20),

              // Checkbox Conta PJ
              _buildContaPjCheckbox(),
              const SizedBox(height: 20),

              // Status da conta
              _buildStatusToggle(),
              const SizedBox(height: 30),

              // Preview da conta
              _buildAccountPreview(),
              const SizedBox(height: 30),

              // Botões de ação
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: Colors.white54),
                        padding: const EdgeInsets.symmetric(vertical: 15),
                      ),
                      child: const Text(
                        'Cancelar',
                        style: TextStyle(color: Colors.white54),
                      ),
                    ),
                  ),
                  const SizedBox(width: 15),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _saveAccount,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        padding: const EdgeInsets.symmetric(vertical: 15),
                      ),
                      child: const Text(
                        'Salvar Alterações',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          validator: validator,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(color: Colors.white54),
            prefixIcon: Icon(icon, color: Colors.white54),
            filled: true,
            fillColor: const Color(0xFF16213E),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.orange),
            ),
          ),
          onChanged: (value) => setState(() {}),
        ),
      ],
    );
  }

  Widget _buildContaPjCheckbox() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF16213E),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.business_center, color: Colors.orange, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Conta Pessoa Jurídica',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Text(
                  'Marque se esta é uma conta empresarial',
                  style: TextStyle(color: Colors.white70, fontSize: 14),
                ),
              ],
            ),
          ),
          Switch(
            value: _isContaPj,
            onChanged: (value) {
              setState(() {
                _isContaPj = value;
              });
            },
            activeColor: Colors.orange,
            activeTrackColor: Colors.orange.withValues(alpha: 0.3),
            inactiveThumbColor: Colors.white54,
            inactiveTrackColor: Colors.white24,
          ),
        ],
      ),
    );
  }

  Widget _buildDropdownField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Tipo de Conta',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _selectedAccountType,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            prefixIcon: const Icon(Icons.category, color: Colors.white54),
            filled: true,
            fillColor: const Color(0xFF16213E),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.orange),
            ),
          ),
          dropdownColor: const Color(0xFF16213E),
          items: _accountTypes.map((String type) {
            return DropdownMenuItem<String>(
              value: type,
              child: Text(type, style: const TextStyle(color: Colors.white)),
            );
          }).toList(),
          onChanged: (String? newValue) {
            if (newValue != null) {
              setState(() {
                _selectedAccountType = newValue;
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildStatusToggle() {
    return Row(
      children: [
        Switch(
          value: _isActive,
          onChanged: (value) => setState(() => _isActive = value),
          activeColor: Colors.green,
          inactiveThumbColor: Colors.red,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _isActive ? 'Conta Ativa' : 'Conta Inativa',
                style: TextStyle(
                  color: _isActive ? Colors.green : Colors.red,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                _isActive
                    ? 'A conta aparecerá no dashboard'
                    : 'A conta ficará oculta no dashboard',
                style: const TextStyle(color: Colors.white70, fontSize: 12),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAccountPreview() {
    final bankName = _bankNameController.text.isEmpty
        ? 'Nome do Banco'
        : _bankNameController.text;
    final nickname = _nicknameController.text;
    final balance =
        double.tryParse(_balanceController.text.replaceAll(',', '.')) ?? 0.0;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color:
            AccountConstants.getColorByName(
              _selectedColor,
            )?.withValues(alpha: 0.1) ??
            const Color(0xFF16213E),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color:
              AccountConstants.getColorByName(
                _selectedColor,
              )?.withValues(alpha: 0.5) ??
              Colors.orange.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AccountConstants.getColorByName(_selectedColor),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      AccountConstants.getIconByName(_selectedIcon),
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        nickname.isNotEmpty ? nickname : bankName,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        _selectedAccountType,
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: _isActive ? Colors.green : Colors.red,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _isActive ? 'Ativa' : 'Inativa',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          const Text(
            'Saldo Atual',
            style: TextStyle(color: Colors.white70, fontSize: 14),
          ),
          Text(
            'R\$ ${balance.toStringAsFixed(2)}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 28,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  void _saveAccount() {
    if (_formKey.currentState!.validate()) {
      final accountForm = AccountFormModel(
        id: widget.account['id'],
        nome: _bankNameController.text,
        tipoConta: _selectedAccountType,
        apelidoConta: _nicknameController.text,
        saldo:
            double.tryParse(_balanceController.text.replaceAll(',', '.')) ??
            0.0,
        // Novos campos
        icone: _selectedIcon,
        cor: _selectedColor,
        agencia: _agenciaController.text.isNotEmpty
            ? _agenciaController.text
            : null,
        numeroConta: _numeroContaController.text.isNotEmpty
            ? _numeroContaController.text
            : null,
        numeroBanco: _numeroBancoController.text.isNotEmpty
            ? _numeroBancoController.text
            : null,
        contaPj: _isContaPj,
      );

      // Usar o serviço para atualizar a conta
      AccountsService.updateAccount(accountForm).then((success) {
        if (mounted) {
          if (success) {
            Navigator.pop(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Conta atualizada com sucesso!'),
                backgroundColor: Colors.green,
              ),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Erro ao atualizar conta'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      });
    }
  }
}
