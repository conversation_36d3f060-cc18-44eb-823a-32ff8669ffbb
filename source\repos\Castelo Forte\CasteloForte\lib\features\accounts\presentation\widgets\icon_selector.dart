import 'package:flutter/material.dart';
import '../../data/constants/account_constants.dart';

/// Widget para seleção de ícones de conta
class IconSelector extends StatelessWidget {
  final String? selectedIcon;
  final Function(String) onIconSelected;
  final String label;

  const IconSelector({
    super.key,
    required this.selectedIcon,
    required this.onIconSelected,
    this.label = 'Ícon<PERSON> da Conta',
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF16213E),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(
              color: const Color(0xFF4ECDC4).withValues(alpha: 0.3),
            ),
          ),
          child: Column(
            children: [
              // Ícone selecionado atual
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF4ECDC4).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Icon(
                      AccountConstants.getIconByName(selectedIcon),
                      color: const Color(0xFF4ECDC4),
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      selectedIcon ?? 'account_balance',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                    const Spacer(),
                    const Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.white70,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              // Grid de ícones
              GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 6,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemCount: AccountConstants.iconNames.length,
                itemBuilder: (context, index) {
                  final iconName = AccountConstants.iconNames[index];
                  final icon = AccountConstants.availableIcons[iconName]!;
                  final isSelected = selectedIcon == iconName;

                  return GestureDetector(
                    onTap: () => onIconSelected(iconName),
                    child: Container(
                      decoration: BoxDecoration(
                        color: isSelected
                            ? const Color(0xFF4ECDC4)
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isSelected
                              ? const Color(0xFF4ECDC4)
                              : Colors.white24,
                        ),
                      ),
                      child: Icon(
                        icon,
                        color: isSelected ? Colors.white : Colors.white70,
                        size: 20,
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}
