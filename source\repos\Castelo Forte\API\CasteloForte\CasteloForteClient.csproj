<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.HealthChecks.MongoDb" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.14" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.5" />
    <PackageReference Include="MongoDB.EntityFrameworkCore" Version="8.2.3" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Controllers\BaseComplemento\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\RepositoryAdmin\RepositoryAdmin.csproj" />
    <ProjectReference Include="..\RepositoryClient\RepositoryClient.csproj" />
    <ProjectReference Include="..\ServiceAdmin\ServiceAdmin.csproj" />
    <ProjectReference Include="..\ServiceClient\ServiceClient.csproj" />
    <ProjectReference Include="..\Shared\Shared.csproj" />
  </ItemGroup>

</Project>
