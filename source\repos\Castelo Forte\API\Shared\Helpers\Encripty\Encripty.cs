﻿using Microsoft.Extensions.Configuration;
using System.Security.Cryptography;
using System.Text;

namespace Shared.Helpers.Encripty
{
    public class EncryptionService : IEncryptionService
    {
        private string _encryptionKey;

        public EncryptionService(IConfiguration configuration)
        {
            _encryptionKey = configuration["Criptografia:Chave"] ?? "X9TechSecretKey2024!@#$%^&*()12";

            if (_encryptionKey.Length < 32)
            {
                _encryptionKey = _encryptionKey.PadRight(32, '0');
            }
            else if (_encryptionKey.Length > 32)
            {
                _encryptionKey = _encryptionKey.Substring(0, 32);
            }
        }

        public string Encrypt(string plainText)
        {
            try
            {
                if (string.IsNullOrEmpty(plainText))
                    return string.Empty;

                using var aes = Aes.Create();
                aes.Key = Encoding.UTF8.GetBytes(_encryptionKey);
                aes.GenerateIV();

                using var encryptor = aes.CreateEncryptor();
                using var ms = new MemoryStream();
                using var cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write);
                using var writer = new StreamWriter(cs);

                writer.Write(plainText);
                writer.Close();

                var encrypted = ms.ToArray();
                var result = new byte[aes.IV.Length + encrypted.Length];
                Array.Copy(aes.IV, 0, result, 0, aes.IV.Length);
                Array.Copy(encrypted, 0, result, aes.IV.Length, encrypted.Length);

                return Convert.ToBase64String(result);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao criptografar: {ex.Message}", ex);
            }
        }

        public string Decrypt(string cipherText)
        {
            try
            {
                if (string.IsNullOrEmpty(cipherText))
                    return string.Empty;

                // Validação básica do formato Base64
                if (!IsValidBase64String(cipherText))
                {
                    throw new FormatException("Texto criptografado não está em formato Base64 válido");
                }

                var fullCipher = Convert.FromBase64String(cipherText);

                // Validação do tamanho mínimo (IV + pelo menos 1 byte de dados)
                if (fullCipher.Length < 17)
                {
                    throw new ArgumentException("Dados criptografados muito pequenos para serem válidos");
                }

                using var aes = Aes.Create();
                aes.Key = Encoding.UTF8.GetBytes(_encryptionKey);

                var iv = new byte[16];
                var cipher = new byte[fullCipher.Length - iv.Length];

                Array.Copy(fullCipher, 0, iv, 0, iv.Length);
                Array.Copy(fullCipher, iv.Length, cipher, 0, cipher.Length);

                aes.IV = iv;

                using var decryptor = aes.CreateDecryptor();
                using var ms = new MemoryStream(cipher);
                using var cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read);
                using var reader = new StreamReader(cs);

                var result = reader.ReadToEnd();

                // Validação adicional do resultado
                if (string.IsNullOrEmpty(result))
                {
                    throw new InvalidOperationException("Resultado da descriptografia está vazio");
                }

                return result;
            }
            catch (FormatException ex)
            {
                throw new FormatException($"Erro de formato na descriptografia: {ex.Message}", ex);
            }
            catch (System.Security.Cryptography.CryptographicException ex)
            {
                throw new System.Security.Cryptography.CryptographicException($"Erro de criptografia: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao descriptografar: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Valida se uma string está em formato Base64 válido
        /// </summary>
        /// <param name="base64String">String para validar</param>
        /// <returns>True se for Base64 válido</returns>
        private static bool IsValidBase64String(string base64String)
        {
            if (string.IsNullOrEmpty(base64String))
                return false;

            // Remove espaços em branco
            base64String = base64String.Trim();

            // Verifica se o comprimento é múltiplo de 4
            if (base64String.Length % 4 != 0)
                return false;

            // Verifica se contém apenas caracteres Base64 válidos
            try
            {
                Convert.FromBase64String(base64String);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Tenta descriptografar com tratamento de erro robusto
        /// </summary>
        /// <param name="cipherText">Texto criptografado</param>
        /// <param name="defaultValue">Valor padrão em caso de erro</param>
        /// <returns>Texto descriptografado ou valor padrão</returns>
        public string TryDecrypt(string cipherText, string defaultValue = "")
        {
            try
            {
                return Decrypt(cipherText);
            }
            catch (FormatException)
            {
                // Dados não estão em formato Base64 - podem ser dados legados não criptografados
                return cipherText.StartsWith("mongodb") ? cipherText : defaultValue;
            }
            catch (System.Security.Cryptography.CryptographicException)
            {
                // Erro de criptografia - dados corrompidos
                return defaultValue;
            }
            catch
            {
                // Outros erros
                return defaultValue;
            }
        }
    }
}
