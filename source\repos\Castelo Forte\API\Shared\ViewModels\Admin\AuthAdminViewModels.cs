using System.ComponentModel.DataAnnotations;

namespace Shared.ViewModels.Admin
{
    /// <summary>
    /// ViewModel para requisição de login de administrador
    /// </summary>
    public class LoginAdminRequestViewModel
    {
        /// <summary>
        /// CPF do administrador (formato: 000.000.000-00)
        /// </summary>
        [Required(ErrorMessage = "CPF é obrigatório")]
        [StringLength(14, MinimumLength = 11, ErrorMessage = "CPF deve ter entre 11 e 14 caracteres")]
        public string CPF { get; set; } = string.Empty;

        /// <summary>
        /// Email do administrador (alternativo ao CPF)
        /// </summary>
        [EmailAddress(ErrorMessage = "Email deve ter um formato válido")]
        public string? Email { get; set; }

        /// <summary>
        /// Senha do administrador
        /// </summary>
        [Required(ErrorMessage = "Senha é obrigatória")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "Senha deve ter entre 6 e 100 caracteres")]
        public string Senha { get; set; } = string.Empty;
    }

    /// <summary>
    /// ViewModel para resposta de login de administrador bem-sucedido
    /// </summary>
    public class LoginAdminResponseViewModel
    {
        /// <summary>
        /// Token JWT para autenticação de administrador
        /// </summary>
        public string Token { get; set; } = string.Empty;

        /// <summary>
        /// Tipo do token (sempre "Bearer")
        /// </summary>
        public string TokenType { get; set; } = "Bearer";

        /// <summary>
        /// Data e hora de expiração do token
        /// </summary>
        public DateTime ExpiresAt { get; set; }

        /// <summary>
        /// Dados do administrador autenticado
        /// </summary>
        public UsuarioAdminAutenticadoViewModel Usuario { get; set; } = new();

        /// <summary>
        /// Mensagem de boas-vindas
        /// </summary>
        public string Mensagem { get; set; } = string.Empty;

        /// <summary>
        /// Timestamp da resposta
        /// </summary>
        public DateTimeOffset Timestamp { get; set; } = DateTimeOffset.UtcNow;
    }

    /// <summary>
    /// ViewModel para dados do administrador autenticado
    /// </summary>
    public class UsuarioAdminAutenticadoViewModel
    {
        /// <summary>
        /// ID único do administrador
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Nome completo do administrador
        /// </summary>
        public string Nome { get; set; } = string.Empty;

        /// <summary>
        /// Email do administrador
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// CPF do administrador (mascarado para segurança)
        /// </summary>
        public string CpfMascarado { get; set; } = string.Empty;

        /// <summary>
        /// Data do último acesso
        /// </summary>
        public DateTime? DtaUltimoAcesso { get; set; }

        /// <summary>
        /// Flag indicando se é administrador (sempre true para este contexto)
        /// </summary>
        public bool FlgAdministrador { get; set; } = true;

        /// <summary>
        /// Data de cadastro do administrador
        /// </summary>
        public DateTime? DtaCadastro { get; set; }

        /// <summary>
        /// Status ativo do administrador
        /// </summary>
        public bool FlgAtivo { get; set; } = true;
    }

    /// <summary>
    /// ViewModel para validação de token de administrador
    /// </summary>
    public class ValidateAdminTokenRequestViewModel
    {
        /// <summary>
        /// Token JWT a ser validado
        /// </summary>
        [Required(ErrorMessage = "Token é obrigatório")]
        public string Token { get; set; } = string.Empty;
    }

    /// <summary>
    /// ViewModel para resposta de validação de token de administrador
    /// </summary>
    public class ValidateAdminTokenResponseViewModel
    {
        /// <summary>
        /// Indica se o token é válido
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Indica se o usuário é administrador
        /// </summary>
        public bool IsAdmin { get; set; }

        /// <summary>
        /// Data de expiração do token
        /// </summary>
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// Dados do administrador (se token válido)
        /// </summary>
        public UsuarioAdminAutenticadoViewModel? Usuario { get; set; }

        /// <summary>
        /// Mensagem de erro (se token inválido)
        /// </summary>
        public string? MensagemErro { get; set; }

        /// <summary>
        /// Timestamp da validação
        /// </summary>
        public DateTimeOffset Timestamp { get; set; } = DateTimeOffset.UtcNow;
    }
}
