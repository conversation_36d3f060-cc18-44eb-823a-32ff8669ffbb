using System.ComponentModel.DataAnnotations;

namespace Shared.ViewModels.Client
{
    /// <summary>
    /// ViewModel consolidado para conta bancária/cartão na dashboard do cliente
    /// </summary>
    public class DashboardContaViewModel
    {
        /// <summary>
        /// Identificador único da conta/cartão
        /// </summary>
        [Required]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Nome do banco/operadora (ex: "Nubank", "Itaú", "VISA")
        /// </summary>
        [Required]
        [StringLength(100)]
        public string NomeBanco { get; set; } = string.Empty;

        /// <summary>
        /// Tipo da conta (ex: "Conta Corrente", "Conta Poupança", "Cartão de Crédito")
        /// </summary>
        [Required]
        [StringLength(50)]
        public string TipoConta { get; set; } = string.Empty;

        /// <summary>
        /// Saldo atual da conta ou limite disponível do cartão
        /// </summary>
        [Required]
        public decimal Saldo { get; set; }

        /// <summary>
        /// Indica se a conta/cartão está ativo
        /// </summary>
        [Required]
        public bool Ativa { get; set; } = true;

        /// <summary>
        /// Apelido personalizado da conta/cartão
        /// </summary>
        public string? Apelido { get; set; }

        /// <summary>
        /// Últimos 4 dígitos do cartão (apenas para cartões de crédito)
        /// </summary>
        public string? UltimosDigitos { get; set; }

        /// <summary>
        /// Bandeira do cartão (VISA, MASTER, etc.) - apenas para cartões
        /// </summary>
        public string? Bandeira { get; set; }

        /// <summary>
        /// Nome de exibição (apelido ou nome do banco)
        /// </summary>
        public string NomeExibicao => !string.IsNullOrEmpty(Apelido) ? Apelido : NomeBanco;

        /// <summary>
        /// Verifica se é um cartão de crédito
        /// </summary>
        public bool IsCartaoCredito => TipoConta.ToLower().Contains("cartão") || TipoConta.ToLower().Contains("cartao");

        /// <summary>
        /// Número mascarado do cartão para exibição segura
        /// </summary>
        public string? NumeroMascarado => IsCartaoCredito && !string.IsNullOrEmpty(UltimosDigitos)
            ? $"**** **** **** {UltimosDigitos}"
            : null;
    }
}
