﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shared.Enums
{
    public enum FormaPagamento
    {
        Despesa = 1,
        Receita = 2,
        Transfêrencia = 3
    };

    public enum OrigemErro
    {
        [Description("API Admin")]
        ADMIN,

        [Description("API Client")]
        CLIENT,

        [Description("Worker")]
        WORKER,
    }

    /// <summary>
    /// Tipos de categoria
    /// </summary>
    public enum TipoCategoria
    {
        [Description("Receita")]
        Receita = 1,

        [Description("Despesa")]
        Despesa = 2,

        [Description("Meta")]
        Meta = 3
    }

    /// <summary>
    /// Períodos para limites de categoria
    /// </summary>
    public enum PeriodoLimite
    {
        [Description("Diário")]
        Diario = 1,

        [Description("Semanal")]
        Semanal = 2,

        [Description("Mensal")]
        Mensal = 3,

        [Description("Anual")]
        Anual = 4
    }
}
