using Microsoft.AspNetCore.Http;
using MongoDB.Driver;
using RepositoryAdmin.Interfaces;
using RepositoryClient.Configuration.Interfaces;
using RepositoryClient.Interfaces;
using RepositoryClient.Repositories.Generic;
using Shared.Entities.Client;
using Shared.Enums;

namespace RepositoryClient.Repositories
{
    /// <summary>
    /// Repositório para operações com categorias
    /// </summary>
    public class CategoriaRepository : GenericClientRepository<Categoria>, ICategoriaRepository
    {
        public CategoriaRepository(
            IContextoMultiTenantService contextoMultiTenant,
            IHttpContextAccessor httpContextAccessor,
            IUsuarioRepository usuarioRepository)
            : base(contextoMultiTenant, httpContextAccessor, usuarioRepository, "Categoria")
        {
        }

        /// <summary>
        /// Busca categorias por tipo
        /// </summary>
        public async Task<IEnumerable<Categoria>> BuscarPorTipoAsync(TipoCategoria tipo)
        {
            try
            {
                var filter = Builders<Categoria>.Filter.And(
                    GetTenantFilter(),
                    Builders<Categoria>.Filter.Eq(c => c.Tipo, tipo)
                );

                return await _collection.Find(filter)
                    .SortBy(c => c.Ordem)
                    .ThenBy(c => c.Nome)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar categorias por tipo: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca categorias ativas
        /// </summary>
        public async Task<IEnumerable<Categoria>> BuscarAtivasAsync()
        {
            try
            {
                var filter = Builders<Categoria>.Filter.And(
                    GetTenantFilter(),
                    Builders<Categoria>.Filter.Eq(c => c.Ativa, true)
                );

                return await _collection.Find(filter)
                    .SortBy(c => c.Ordem)
                    .ThenBy(c => c.Nome)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar categorias ativas: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca categorias por nome (busca parcial)
        /// </summary>
        public async Task<IEnumerable<Categoria>> BuscarPorNomeAsync(string nome)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(nome))
                    return await BuscarTodosAsync();

                var filter = Builders<Categoria>.Filter.And(
                    GetTenantFilter(),
                    Builders<Categoria>.Filter.Regex(c => c.Nome, new MongoDB.Bson.BsonRegularExpression(nome, "i"))
                );

                return await _collection.Find(filter)
                    .SortBy(c => c.Nome)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar categorias por nome: {ex.Message}", ex);
            }
        }



        /// <summary>
        /// Verifica se existe uma categoria com o nome especificado
        /// </summary>
        public async Task<bool> ExisteCategoriaPorNomeAsync(string nome, string? idExcluir = null)
        {
            try
            {
                var filterBuilder = Builders<Categoria>.Filter;
                var filter = filterBuilder.And(
                    GetTenantFilter(),
                    filterBuilder.Eq(c => c.Nome, nome)
                );

                if (!string.IsNullOrEmpty(idExcluir))
                {
                    filter = filterBuilder.And(filter, filterBuilder.Ne(c => c.Id, idExcluir));
                }

                var count = await _collection.CountDocumentsAsync(filter);
                return count > 0;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao verificar existência de categoria: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Inativa uma categoria (soft delete)
        /// </summary>
        public async Task<bool> InativarAsync(string id)
        {
            try
            {
                var filter = Builders<Categoria>.Filter.And(
                    GetTenantFilter(),
                    Builders<Categoria>.Filter.Eq(c => c.Id, id)
                );

                var update = Builders<Categoria>.Update
                    .Set(c => c.Ativa, false)
                    .Set(c => c.DataAlteracao, DateTime.Now);

                var result = await _collection.UpdateOneAsync(filter, update);
                return result.ModifiedCount > 0;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao inativar categoria: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Reativa uma categoria
        /// </summary>
        public async Task<bool> ReativarAsync(string id)
        {
            try
            {
                var filter = Builders<Categoria>.Filter.And(
                    GetTenantFilter(),
                    Builders<Categoria>.Filter.Eq(c => c.Id, id)
                );

                var update = Builders<Categoria>.Update
                    .Set(c => c.Ativa, true)
                    .Set(c => c.DataAlteracao, DateTime.Now);

                var result = await _collection.UpdateOneAsync(filter, update);
                return result.ModifiedCount > 0;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao reativar categoria: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca categorias com paginação
        /// </summary>
        public async Task<(IEnumerable<Categoria> Categorias, int Total)> BuscarPaginadoAsync(
            int pagina = 1, 
            int tamanhoPagina = 10, 
            TipoCategoria? tipo = null, 
            bool? ativas = null)
        {
            try
            {
                var filterBuilder = Builders<Categoria>.Filter;
                var filter = GetTenantFilter();

                if (tipo.HasValue)
                {
                    filter = filterBuilder.And(filter, filterBuilder.Eq(c => c.Tipo, tipo.Value));
                }

                if (ativas.HasValue)
                {
                    filter = filterBuilder.And(filter, filterBuilder.Eq(c => c.Ativa, ativas.Value));
                }

                var total = await _collection.CountDocumentsAsync(filter);

                var categorias = await _collection.Find(filter)
                    .SortBy(c => c.Ordem)
                    .ThenBy(c => c.Nome)
                    .Skip((pagina - 1) * tamanhoPagina)
                    .Limit(tamanhoPagina)
                    .ToListAsync();

                return (categorias, (int)total);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar categorias paginadas: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Conta o número de transações associadas a uma categoria
        /// </summary>
        public async Task<int> ContarTransacoesAsync(string idCategoria)
        {
            try
            {
                // Aqui você implementaria a contagem de transações
                // Por enquanto, retorna 0 como placeholder
                // TODO: Implementar quando a entidade Transacao estiver disponível
                return 0;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao contar transações da categoria: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Conta o número de metas associadas a uma categoria
        /// </summary>
        public async Task<int> ContarMetasAsync(string idCategoria)
        {
            try
            {
                var metaCategoriaCollection = await ObterCollectionAsync<MetaCategoria>("MetaCategoria");
                var filter = Builders<MetaCategoria>.Filter.Eq(mc => mc.IdCategoria, idCategoria);

                var count = await metaCategoriaCollection.CountDocumentsAsync(filter);
                return (int)count;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao contar metas da categoria: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Obtém o filtro de tenant para consultas
        /// </summary>
        protected FilterDefinition<Categoria> GetTenantFilter()
        {
            // Por enquanto, retorna filtro vazio pois as entidades não têm IdTenant
            // O multi-tenancy é gerenciado através do contexto de banco de dados
            return Builders<Categoria>.Filter.Empty;
        }

        /// <summary>
        /// Obtém o ID do tenant atual
        /// </summary>
        protected string? GetCurrentTenantId()
        {
            return _contextoMultiTenant.BuscarTenantAtualId();
        }
    }
}
