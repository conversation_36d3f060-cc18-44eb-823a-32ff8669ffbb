﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shared.Utils
{
    public static class Calculos
    {
        public static int CalcularIdade(DateTime AnoNascimento)
        {
            DateTime hoje = DateTime.Today;
            int idade = hoje.Year - AnoNascimento.Year;

            if (AnoNascimento.Date > hoje.AddYears(-idade))
                idade--;

            return idade;
        }
    }
}
