import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../core/utils/constants.dart';
import '../core/theme/app_theme.dart';
import '../features/splash/presentation/splash_screen.dart';
import '../features/auth/presentation/login_screen.dart';
import '../features/auth/presentation/register_screen.dart';
import '../features/dashboard/presentation/dashboard_screen.dart';
import '../features/settings/presentation/api_settings_screen.dart';
import '../features/profile/presentation/profile_screen.dart';
import '../features/transactions/presentation/transactions_screen.dart';
import '../features/reports/presentation/reports_screen.dart';
import '../features/goals/presentation/goals_list_screen.dart';
import '../features/debug/presentation/debug_screen.dart';
import '../features/categories/presentation/categories_list_screen.dart';
import '../features/accounts/presentation/accounts_list_screen.dart';
import '../features/cards/presentation/cards_list_screen.dart';
import '../features/accounts/presentation/add_account_screen.dart';
import '../features/accounts/presentation/account_form_wrapper.dart';

/// Configuração centralizada de rotas da aplicação
class AppRouter {
  static final GoRouter _router = GoRouter(
    initialLocation: AppConstants.splashRoute,
    routes: [
      // Rota da Splash Screen
      GoRoute(
        path: AppConstants.splashRoute,
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),

      // Rota de Login
      GoRoute(
        path: AppConstants.loginRoute,
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),

      // Rota de Cadastro
      GoRoute(
        path: AppConstants.registerRoute,
        name: 'register',
        builder: (context, state) => const RegisterScreen(),
      ),

      // Rota de Re-entrada de Senha
      GoRoute(
        path: AppConstants.passwordReentryRoute,
        name: 'passwordReentry',
        builder: (context, state) {
          // Esta rota normalmente não será usada diretamente
          // A navegação será feita via MaterialPageRoute
          return const _PlaceholderScreen(title: 'Re-autenticação');
        },
      ),

      // Rota do Dashboard
      GoRoute(
        path: AppConstants.dashboardRoute,
        name: 'dashboard',
        builder: (context, state) => const DashboardScreen(),
      ),

      // Rota de Perfil
      GoRoute(
        path: AppConstants.profileRoute,
        name: 'profile',
        builder: (context, state) => const ProfileScreen(),
      ),

      // Rota de Edição de Perfil
      GoRoute(
        path: AppConstants.editProfileRoute,
        name: 'editProfile',
        builder: (context, state) =>
            const _PlaceholderScreen(title: 'Editar Perfil'),
      ),

      // Rota de Configurações
      GoRoute(
        path: AppConstants.settingsRoute,
        name: 'settings',
        builder: (context, state) =>
            const _PlaceholderScreen(title: 'Configurações'),
      ),

      // Rota de Segurança
      GoRoute(
        path: AppConstants.securityRoute,
        name: 'security',
        builder: (context, state) =>
            const _PlaceholderScreen(title: 'Segurança'),
      ),

      // Rota de Lançamentos
      GoRoute(
        path: AppConstants.transactionsRoute,
        name: 'transactions',
        builder: (context, state) => const TransactionsScreen(),
      ),

      // Rota de Relatórios
      GoRoute(
        path: AppConstants.reportsRoute,
        name: 'reports',
        builder: (context, state) => const ReportsScreen(),
      ),

      // Rota de Metas
      GoRoute(
        path: AppConstants.goalsRoute,
        name: 'goals',
        builder: (context, state) => const GoalsListScreen(),
      ),

      // Rota de Categorias
      GoRoute(
        path: AppConstants.categoriesRoute,
        name: 'categories',
        builder: (context, state) => const CategoriesListScreen(),
      ),

      // Rota de Contas
      GoRoute(
        path: AppConstants.accountsRoute,
        name: 'accounts',
        builder: (context, state) => const AccountsListScreen(),
      ),

      // Rota de Cartões
      GoRoute(
        path: AppConstants.cardsRoute,
        name: 'cards',
        builder: (context, state) => const CardsListScreen(),
      ),

      // Rota de Formulário de Conta
      GoRoute(
        path: AppConstants.accountFormRoute,
        name: 'accountForm',
        builder: (context, state) {
          final accountId = state.uri.queryParameters['id'];
          if (accountId != null) {
            // Para edição, vamos usar uma tela que carrega a conta primeiro
            return AccountFormWrapper(accountId: accountId);
          }
          return const AddAccountScreen();
        },
      ),

      // Rota de Ajuda
      GoRoute(
        path: AppConstants.helpRoute,
        name: 'help',
        builder: (context, state) =>
            const _PlaceholderScreen(title: 'Ajuda & Suporte'),
      ),

      // Rota de Notificações
      GoRoute(
        path: AppConstants.notificationsRoute,
        name: 'notifications',
        builder: (context, state) =>
            const _PlaceholderScreen(title: 'Notificações'),
      ),

      // Rota de Configurações da API
      GoRoute(
        path: AppConstants.apiSettingsRoute,
        name: 'apiSettings',
        builder: (context, state) => const ApiSettingsScreen(),
      ),

      // Rota de Debug (apenas para desenvolvimento)
      GoRoute(
        path: AppConstants.debugRoute,
        name: 'debug',
        builder: (context, state) => const DebugScreen(),
      ),
    ],
    errorBuilder: (context, state) =>
        _ErrorScreen(error: state.error.toString()),
  );

  /// Getter para acessar o router
  static GoRouter get router => _router;
}

/// Tela de placeholder para rotas ainda não implementadas
class _PlaceholderScreen extends StatelessWidget {
  final String title;

  const _PlaceholderScreen({required this.title});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        backgroundColor: AppTheme.navyBlueColor,
        foregroundColor: AppTheme.snowWhiteColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppConstants.dashboardRoute),
        ),
      ),
      backgroundColor: AppTheme.navyBlueColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.construction, size: 80, color: AppTheme.goldColor),
            const SizedBox(height: 16),
            const Text(
              'Tela em construção',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Esta funcionalidade estará disponível em breve.',
              style: TextStyle(fontSize: 16, color: Colors.white70),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

/// Tela de erro para rotas não encontradas
class _ErrorScreen extends StatelessWidget {
  final String error;

  const _ErrorScreen({required this.error});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.navyBlueColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 80, color: AppTheme.errorColor),
            const SizedBox(height: 16),
            const Text(
              'Página não encontrada',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'O caminho solicitado não existe.',
              style: TextStyle(fontSize: 16, color: Colors.white70),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go(AppConstants.dashboardRoute),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.goldColor,
                foregroundColor: AppTheme.navyBlueColor,
              ),
              child: const Text('Voltar para o início'),
            ),
          ],
        ),
      ),
    );
  }
}
