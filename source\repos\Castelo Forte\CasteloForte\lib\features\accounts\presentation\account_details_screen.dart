import 'package:flutter/material.dart';
import 'edit_account_screen.dart';
import '../../transactions/presentation/add_transaction_screen.dart';
import '../../transactions/presentation/transfer_screen.dart';
import '../../transactions/presentation/statement_screen.dart';
import 'account_reports_screen.dart';
import '../../transactions/presentation/transactions_screen.dart';
import '../../../core/utils/navigation_helper.dart';

class AccountDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> account;

  const AccountDetailsScreen({super.key, required this.account});

  @override
  State<AccountDetailsScreen> createState() => _AccountDetailsScreenState();
}

class _AccountDetailsScreenState extends State<AccountDetailsScreen> {
  late Map<String, dynamic> _account;

  @override
  void initState() {
    super.initState();
    _account = Map<String, dynamic>.from(widget.account);
  }

  @override
  Widget build(BuildContext context) {
    final accountName = _account['nomeBanco'] ?? 'Conta';
    final accountType = _account['tipoConta'] ?? 'Conta Corrente';
    final balance = (_account['saldo'] ?? 0.0) as double;
    final isActive = _account['ativa'] ?? true;
    final nickname = _account['apelido'] ?? '';

    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => NavigationHelper.safeGoBack(context),
        ),
        title: Text(
          nickname.isNotEmpty ? nickname : accountName,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit, color: Colors.white),
            onPressed: () => _editAccount(),
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            color: const Color(0xFF16213E),
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, color: Colors.white70),
                    SizedBox(width: 12),
                    Text('Editar', style: TextStyle(color: Colors.white)),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'duplicate',
                child: Row(
                  children: [
                    Icon(Icons.copy, color: Colors.white70),
                    SizedBox(width: 12),
                    Text('Duplicar', style: TextStyle(color: Colors.white)),
                  ],
                ),
              ),
              PopupMenuItem(
                value: isActive ? 'deactivate' : 'activate',
                child: Row(
                  children: [
                    Icon(
                      isActive ? Icons.visibility_off : Icons.visibility,
                      color: Colors.white70,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      isActive ? 'Desativar' : 'Ativar',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 12),
                    Text('Excluir', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Card principal da conta
            _buildAccountCard(accountName, accountType, balance, isActive),
            const SizedBox(height: 30),

            // Informações detalhadas
            _buildDetailSection(),
            const SizedBox(height: 20),

            // Transações recentes
            _buildRecentTransactions(),
            const SizedBox(height: 20),

            // Estatísticas
            _buildStatistics(),
            const SizedBox(height: 20),

            // Ações rápidas
            _buildQuickActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountCard(
    String name,
    String type,
    double balance,
    bool isActive,
  ) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF4CAF50),
            const Color(0xFF4CAF50).withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    type,
                    style: const TextStyle(color: Colors.white70, fontSize: 14),
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: isActive ? Colors.green : Colors.red,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  isActive ? 'Ativa' : 'Inativa',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Text(
            'Saldo Atual',
            style: const TextStyle(color: Colors.white70, fontSize: 14),
          ),
          Text(
            'R\$ ${balance.toStringAsFixed(2)}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 32,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF16213E),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.white10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Informações da Conta',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          _buildDetailRow(
            'Nome do Banco',
            _account['nomeBanco'] ?? 'N/A',
            Icons.account_balance,
          ),
          _buildDetailRow(
            'Tipo de Conta',
            _account['tipoConta'] ?? 'N/A',
            Icons.category,
          ),
          _buildDetailRow(
            'Apelido',
            _account['apelido'] ?? 'Não definido',
            Icons.label,
          ),
          _buildDetailRow(
            'Data de Criação',
            _formatDate(_account['dataCriacao']),
            Icons.calendar_today,
          ),
          _buildDetailRow(
            'Última Atualização',
            _formatDate(_account['dataUltimaAtualizacao']),
            Icons.update,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: Colors.white54, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(color: Colors.white54, fontSize: 12),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentTransactions() {
    // Mock data - em produção viria da API
    final transactions = [
      {
        'title': 'Supermercado',
        'amount': -85.50,
        'date': DateTime.now().subtract(const Duration(days: 1)),
      },
      {
        'title': 'Salário',
        'amount': 3500.00,
        'date': DateTime.now().subtract(const Duration(days: 3)),
      },
      {
        'title': 'Farmácia',
        'amount': -45.20,
        'date': DateTime.now().subtract(const Duration(days: 5)),
      },
    ];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF16213E),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.white10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Transações Recentes',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () => _viewAllTransactions(),
                child: const Text(
                  'Ver todas',
                  style: TextStyle(color: Color(0xFF4CAF50)),
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          ...transactions.map(
            (transaction) => _buildTransactionItem(transaction),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionItem(Map<String, dynamic> transaction) {
    final isExpense = transaction['amount'] < 0;
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isExpense
                  ? Colors.red.withValues(alpha: 0.2)
                  : Colors.green.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              isExpense ? Icons.arrow_downward : Icons.arrow_upward,
              color: isExpense ? Colors.red : Colors.green,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction['title'],
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  _formatDate(transaction['date']),
                  style: const TextStyle(color: Colors.white54, fontSize: 12),
                ),
              ],
            ),
          ),
          Text(
            '${isExpense ? '' : '+'}R\$ ${transaction['amount'].abs().toStringAsFixed(2)}',
            style: TextStyle(
              color: isExpense ? Colors.red : Colors.green,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatistics() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF16213E),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.white10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Estatísticas do Mês',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Receitas',
                  'R\$ 3.500,00',
                  Colors.green,
                  Icons.trending_up,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Despesas',
                  'R\$ 2.150,00',
                  Colors.red,
                  Icons.trending_down,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Transações',
                  '24',
                  Colors.blue,
                  Icons.swap_horiz,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Média/Dia',
                  'R\$ 71,67',
                  Colors.orange,
                  Icons.analytics,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(color: Colors.white70, fontSize: 12),
          ),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF16213E),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.white10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Ações Rápidas',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 15),
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  'Nova Transação',
                  Icons.add,
                  () => _addTransaction(),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildActionButton(
                  'Transferir',
                  Icons.swap_horiz,
                  () => _transfer(),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  'Extrato',
                  Icons.receipt_long,
                  () => _viewStatement(),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildActionButton(
                  'Relatórios',
                  Icons.analytics,
                  () => _viewReports(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(String label, IconData icon, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white10,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(icon, color: Colors.white70, size: 24),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(color: Colors.white70, fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(dynamic date) {
    if (date == null) return 'N/A';
    if (date is DateTime) {
      return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
    }
    return date.toString();
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'edit':
        _editAccount();
        break;
      case 'duplicate':
        _duplicateAccount();
        break;
      case 'activate':
      case 'deactivate':
        _toggleAccountStatus();
        break;
      case 'delete':
        _deleteAccount();
        break;
    }
  }

  void _editAccount() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditAccountScreen(account: _account),
      ),
    );

    if (result != null) {
      setState(() {
        _account = result;
      });
    }
  }

  void _duplicateAccount() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213E),
        title: const Text(
          'Duplicar Conta',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          'Deseja criar uma cópia desta conta? Você poderá editar as informações depois.',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancelar',
              style: TextStyle(color: Colors.white70),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog

              // Criar cópia da conta
              final duplicatedAccount = {
                ..._account,
                'id': DateTime.now().millisecondsSinceEpoch.toString(),
                'nomeBanco': '${_account['nomeBanco']} - Cópia',
                'apelido': '${_account['apelido']} - Cópia',
                'dataCriacao': DateTime.now(),
                'dataUltimaAtualizacao': DateTime.now(),
              };

              // Navegar para edição da conta duplicada
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) =>
                      EditAccountScreen(account: duplicatedAccount),
                ),
              );
            },
            child: const Text(
              'Duplicar',
              style: TextStyle(color: Color(0xFF4CAF50)),
            ),
          ),
        ],
      ),
    );
  }

  void _toggleAccountStatus() {
    setState(() {
      _account['ativa'] = !(_account['ativa'] ?? true);
    });

    final isActive = _account['ativa'];
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Conta ${isActive ? 'ativada' : 'desativada'} com sucesso!',
        ),
        backgroundColor: isActive ? Colors.green : Colors.orange,
      ),
    );
  }

  void _deleteAccount() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213E),
        title: const Text(
          'Excluir Conta',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          'Tem certeza que deseja excluir esta conta? Esta ação não pode ser desfeita e todas as transações associadas serão perdidas.',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancelar',
              style: TextStyle(color: Colors.white70),
            ),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context); // Close dialog

              // Simular exclusão no backend
              await _simulateAccountDeletion();

              if (context.mounted) {
                Navigator.pop(context); // Return to previous screen
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Conta excluída com sucesso!'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            child: const Text('Excluir', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _addTransaction() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            AddTransactionScreen(preSelectedAccountId: _account['id']),
      ),
    );
  }

  void _transfer() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            TransferScreen(preSelectedAccountId: _account['id']),
      ),
    );
  }

  void _viewStatement() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StatementScreen(
          accountId: _account['id'],
          accountName: _account['nomeBanco'],
        ),
      ),
    );
  }

  void _viewReports() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AccountReportsScreen(
          accountId: _account['id'],
          accountName: _account['nomeBanco'],
        ),
      ),
    );
  }

  void _viewAllTransactions() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const TransactionsScreen()),
    );
  }

  Future<void> _simulateAccountDeletion() async {
    // Simular delay de rede
    await Future.delayed(const Duration(milliseconds: 500));

    // Aqui seria feita a chamada real para o backend
    // Exemplo: await AccountService.deleteAccount(_account['id']);

    // Por enquanto, apenas simulamos o sucesso
    // Log seria feito aqui em produção
  }
}
