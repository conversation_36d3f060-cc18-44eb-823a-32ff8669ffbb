{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"ServiceAdmin/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.3.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.IdentityModel.Tokens": "8.0.2", "RepositoryAdmin": "1.0.0", "RepositoryClient": "1.0.0", "Shared": "1.0.0", "System.IdentityModel.Tokens.Jwt": "8.0.2"}, "runtime": {"ServiceAdmin.dll": {}}}, "AutoMapper/14.0.0": {"dependencies": {"Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/AutoMapper.dll": {"assemblyVersion": "14.0.0.0", "fileVersion": "14.0.0.0"}}}, "DnsClient/1.6.1": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0"}, "runtime": {"lib/net5.0/DnsClient.dll": {"assemblyVersion": "1.6.1.0", "fileVersion": "1.6.1.0"}}}, "FFMpegCore/5.2.0": {"dependencies": {"Instances": "3.0.1", "System.Text.Json": "9.0.2"}, "runtime": {"lib/netstandard2.0/FFMpegCore.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.0.0"}}}, "Instances/3.0.1": {"runtime": {"lib/netstandard2.0/Instances.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNetCore.Http/2.3.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.3.0", "Microsoft.AspNetCore.WebUtilities": "2.3.0", "Microsoft.Extensions.ObjectPool": "8.0.11", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Net.Http.Headers": "2.3.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {"assemblyVersion": "*******", "fileVersion": "2.3.0.25014"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.3.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.3.0", "System.Text.Encodings.Web": "9.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.3.0.25014"}}}, "Microsoft.AspNetCore.Http.Features/2.3.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"assemblyVersion": "*******", "fileVersion": "2.3.0.25014"}}}, "Microsoft.AspNetCore.WebUtilities/2.3.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.3.0", "System.Text.Encodings.Web": "9.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {"assemblyVersion": "*******", "fileVersion": "2.3.0.25014"}}}, "Microsoft.EntityFrameworkCore/8.0.12": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "8.0.12", "Microsoft.EntityFrameworkCore.Analyzers": "8.0.12", "Microsoft.Extensions.Caching.Memory": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "8.0.12.0", "fileVersion": "8.0.1224.60305"}}}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.12": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "8.0.12.0", "fileVersion": "8.0.1224.60305"}}}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.12": {}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Caching.Memory/8.0.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.4": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Logging/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "System.Diagnostics.DiagnosticSource": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.ObjectPool/8.0.11": {"runtime": {"lib/net8.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.1124.52116"}}}, "Microsoft.Extensions.Options/8.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.224.6711"}}}, "Microsoft.Extensions.Primitives/9.0.4": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.IdentityModel.Abstractions/8.0.2": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.2.50822"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.0.2": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.2.50822"}}}, "Microsoft.IdentityModel.Logging/8.0.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.2.50822"}}}, "Microsoft.IdentityModel.Tokens/8.0.2": {"dependencies": {"Microsoft.IdentityModel.Logging": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.2.50822"}}}, "Microsoft.Net.Http.Headers/2.3.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.4", "System.Buffers": "4.6.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"assemblyVersion": "*******", "fileVersion": "2.3.0.25014"}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "MongoDB.Bson/3.2.1": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/net6.0/MongoDB.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MongoDB.Driver/3.1.0": {"dependencies": {"DnsClient": "1.6.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "MongoDB.Bson": "3.2.1", "SharpCompress": "0.30.1", "Snappier": "1.0.0", "System.Buffers": "4.6.0", "ZstdSharp.Port": "0.7.3"}, "runtime": {"lib/net6.0/MongoDB.Driver.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MongoDB.EntityFrameworkCore/8.2.3": {"dependencies": {"Microsoft.EntityFrameworkCore": "8.0.12", "MongoDB.Driver": "3.1.0"}, "runtime": {"lib/net8.0/MongoDB.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "RestSharp/106.11.7": {"runtime": {"lib/netstandard2.0/RestSharp.dll": {"assemblyVersion": "106.11.7.0", "fileVersion": "106.11.7.0"}}}, "SharpCompress/0.30.1": {"runtime": {"lib/net5.0/SharpCompress.dll": {"assemblyVersion": "0.30.1.0", "fileVersion": "0.30.1.0"}}}, "Snappier/1.0.0": {"runtime": {"lib/net5.0/Snappier.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Buffers/4.6.0": {}, "System.Diagnostics.DiagnosticSource/9.0.4": {"runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.IdentityModel.Tokens.Jwt/8.0.2": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.0.2", "Microsoft.IdentityModel.Tokens": "8.0.2"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.2.50822"}}}, "System.IO.Pipelines/9.0.2": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}}, "System.Memory/4.5.5": {}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encodings.Web/9.0.2": {"runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}}, "System.Text.Json/9.0.2": {"dependencies": {"System.IO.Pipelines": "9.0.2", "System.Text.Encodings.Web": "9.0.2"}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.225.6610"}}}, "ZstdSharp.Port/0.7.3": {"runtime": {"lib/net7.0/ZstdSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "RepositoryAdmin/1.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.5", "MongoDB.Bson": "3.2.1", "MongoDB.Driver": "3.1.0", "MongoDB.EntityFrameworkCore": "8.2.3", "Shared": "1.0.0"}, "runtime": {"RepositoryAdmin.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "RepositoryClient/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Http": "2.3.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "MongoDB.EntityFrameworkCore": "8.2.3", "RepositoryAdmin": "1.0.0", "Shared": "1.0.0"}, "runtime": {"RepositoryClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Shared/1.0.0": {"dependencies": {"AutoMapper": "14.0.0", "FFMpegCore": "5.2.0", "Microsoft.AspNetCore.Http.Features": "2.3.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "MongoDB.Bson": "3.2.1", "MongoDB.Driver": "3.1.0", "Newtonsoft.Json": "13.0.3", "RestSharp": "106.11.7"}, "runtime": {"Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"ServiceAdmin/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AutoMapper/14.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OC+1neAPM4oCCqQj3g2GJ2shziNNhOkxmNB9cVS8jtx4JbgmRzLcUOxB9Tsz6cVPHugdkHgCaCrTjjSI0Z5sCQ==", "path": "automapper/14.0.0", "hashPath": "automapper.14.0.0.nupkg.sha512"}, "DnsClient/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-4H/f2uYJOZ+YObZjpY9ABrKZI+JNw3uizp6oMzTXwDw6F+2qIPhpRl/1t68O/6e98+vqNiYGu+lswmwdYUy3gg==", "path": "dnsclient/1.6.1", "hashPath": "dnsclient.1.6.1.nupkg.sha512"}, "FFMpegCore/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-VpTM8mNo41Upj2zrlroEroQp7TGaXa+jDlGbekvy7Vcvz7bQTesUhf0LWvvwgakcCo9j9OlhP1bzkaZpEyn8WA==", "path": "ffmpegcore/5.2.0", "hashPath": "ffmpegcore.5.2.0.nupkg.sha512"}, "Instances/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-HSMGZF4g/VcmVyoWH2fRkgZMAW50FcsKvDttwH1jVS2oIz7V9xw9e/b2KhbCO4RSJKJEgTuPKQb6sQvqcDOl2A==", "path": "instances/3.0.1", "hashPath": "instances.3.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I9azEG2tZ4DDHAFgv+N38e6Yhttvf+QjE2j2UYyCACE7Swm5/0uoihCMWZ87oOZYeqiEFSxbsfpT71OYHe2tpw==", "path": "microsoft.aspnetcore.http/2.3.0", "hashPath": "microsoft.aspnetcore.http.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-39r9PPrjA6s0blyFv5qarckjNkaHRA5B+3b53ybuGGNTXEj1/DStQJ4NWjFL6QTRQpL9zt7nDyKxZdJOlcnq+Q==", "path": "microsoft.aspnetcore.http.abstractions/2.3.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-f10WUgcsKqrkmnz6gt8HeZ7kyKjYN30PO7cSic1lPtH7paPtnQqXPOveul/SIPI43PhRD4trttg4ywnrEmmJpA==", "path": "microsoft.aspnetcore.http.features/2.3.0", "hashPath": "microsoft.aspnetcore.http.features.2.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-trbXdWzoAEUVd0PE2yTopkz4kjZaAIA7xUWekd5uBw+7xE8Do/YOVTeb9d9koPTlbtZT539aESJjSLSqD8eYrQ==", "path": "microsoft.aspnetcore.webutilities/2.3.0", "hashPath": "microsoft.aspnetcore.webutilities.2.3.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/8.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-oxZydJ7/1NdG2Tf6qHeTkcm2j2Nc+uHQy5mxrv2FmVFXt7MfkTMyCokcMeElrr7nAUAidrOdiZPVSpNMKkLbkQ==", "path": "microsoft.entityframeworkcore/8.0.12", "hashPath": "microsoft.entityframeworkcore.8.0.12.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-A19COkZVUI3bNUdtWAgLvxzOXyW/gwJ1enmkHi0rgslPIAv9tFU0yEq4KNfmLoHf8kikYvC2DKYZo8vJvWzklA==", "path": "microsoft.entityframeworkcore.abstractions/8.0.12", "hashPath": "microsoft.entityframeworkcore.abstractions.8.0.12.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-irXnlkKHepepje6+ICaKpe/G4QnWi1G9Em9CmfqAEKb7OUTYaDQ2ieGps2Tl7/rblj5CKB0tDNI421f64x/Tqw==", "path": "microsoft.entityframeworkcore.analyzers/8.0.12", "hashPath": "microsoft.entityframeworkcore.analyzers.8.0.12.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "path": "microsoft.extensions.caching.abstractions/8.0.0", "hashPath": "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-HFDnhYLccngrzyGgHkjEDU5FMLn4MpOsr5ElgsBMC4yx6lJh4jeWO7fHS8+TXPq+dgxCmUa/Trl8svObmwW4QA==", "path": "microsoft.extensions.caching.memory/8.0.1", "hashPath": "microsoft.extensions.caching.memory.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-0LN/DiIKvBrkqp7gkF3qhGIeZk6/B63PthAHjQsxymJfIBcz0kbf4/p/t4lMgggVxZ+flRi5xvTwlpPOoZk8fg==", "path": "microsoft.extensions.configuration.abstractions/9.0.4", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-N1Mn0T/tUBPoLL+Fzsp+VCEtneUhhxc1//Dx3BeuQ8AX+XrMlYCfnp2zgpEXnTCB7053CLdiqVWPZ7mEX6MPjg==", "path": "microsoft.extensions.dependencyinjection/9.0.5", "hashPath": "microsoft.extensions.dependencyinjection.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-cjnRtsEAzU73aN6W7vkWy8Phj5t3Xm78HSqgrbh/O4Q9SK/yN73wZVa21QQY6amSLQRQ/M8N+koGnY6PuvKQsw==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.5", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4x+pzsQEbqxhNf1QYRr5TDkLP9UsLT3A6MdRKDDEgrW7h1ljiEPgTNhKYUhNCCAaVpQECVQ+onA91PTPnIp6Lw==", "path": "microsoft.extensions.logging/8.0.1", "hashPath": "microsoft.extensions.logging.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-0MXlimU4Dud6t+iNi5NEz3dO2w1HXdhoOLaYFuLPCjAsvlPQGwOT6V2KZRMLEhCAm/stSZt1AUv0XmDdkjvtbw==", "path": "microsoft.extensions.logging.abstractions/9.0.4", "hashPath": "microsoft.extensions.logging.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/8.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-6ApKcHNJigXBfZa6XlDQ8feJpq7SG1ogZXg6M4FiNzgd6irs3LUAzo0Pfn4F2ZI9liGnH1XIBR/OtSbZmJAV5w==", "path": "microsoft.extensions.objectpool/8.0.11", "hashPath": "microsoft.extensions.objectpool.8.0.11.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "path": "microsoft.extensions.options/8.0.2", "hashPath": "microsoft.extensions.options.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-SPFyMjyku1nqTFFJ928JAMd0QnRe4xjE7KeKnZMWXf3xk+6e0WiOZAluYtLdbJUXtsl2cCRSi8cBquJ408k8RA==", "path": "microsoft.extensions.primitives/9.0.4", "hashPath": "microsoft.extensions.primitives.9.0.4.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-m73Bun0l0jL8rceWZ9TMD4hwQCjDIaRT1s5RMN7TBDpXu8Ea8KcRndo45btW9gG0i/USmHLCmOBIITvTA4Y6PA==", "path": "microsoft.identitymodel.abstractions/8.0.2", "hashPath": "microsoft.identitymodel.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-6CVWMfXrQPMUaqlsMfG8OjtyTIKvtgiQCFOJ2YhSZo1UDaAWweVN7jGSrz59Ez0Y8lh260WE5V2b0Oe9NlVlyw==", "path": "microsoft.identitymodel.jsonwebtokens/8.0.2", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.0.2.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-iKUyFKCQgc8rcEqyIJGLOIqqxemG7bgraqS9n5J6RPoZZH7dwxmJd3aFYmxXuAnfznJuaE1DQX5U46Cqvb+BOg==", "path": "microsoft.identitymodel.logging/8.0.2", "hashPath": "microsoft.identitymodel.logging.8.0.2.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-X58KyDBpGJZcCfmSgbkxJLLxd04eMFVaJlMEbRCyWL1X44n6kMxRyK6UTS1zgi5DHikeyiZj8bi7+p0kfPepLg==", "path": "microsoft.identitymodel.tokens/8.0.2", "hashPath": "microsoft.identitymodel.tokens.8.0.2.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/M0wVg6tJUOHutWD3BMOUVZAioJVXe0tCpFiovzv0T9T12TBf4MnaHP0efO8TCr1a6O9RZgQeZ9Gdark8L9XdA==", "path": "microsoft.net.http.headers/2.3.0", "hashPath": "microsoft.net.http.headers.2.3.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "MongoDB.Bson/3.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-45w3BJU3sVejUaOVLAxQiEdWkNH+iyxMs3lDh4l2lUFbOb/s4uB++UoQY/3TCQrupQyx1/y7yzC5I76o4h7fFw==", "path": "mongodb.bson/3.2.1", "hashPath": "mongodb.bson.3.2.1.nupkg.sha512"}, "MongoDB.Driver/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-+O7lKaIl7VUHptE0hqTd7UY1G5KDp/o8S4upG7YL4uChMNKD/U6tz9i17nMGHaD/L2AiPLgaJcaDe2XACsegGA==", "path": "mongodb.driver/3.1.0", "hashPath": "mongodb.driver.3.1.0.nupkg.sha512"}, "MongoDB.EntityFrameworkCore/8.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-a5enzsXVV2Ssr3otKYd9T7gkOTsnnD7aMkXNIatF28alVJZpF32pQE2g+vUq4SF0Awlx3pk34RT+ZFastryRkQ==", "path": "mongodb.entityframeworkcore/8.2.3", "hashPath": "mongodb.entityframeworkcore.8.2.3.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "RestSharp/106.11.7": {"type": "package", "serviceable": true, "sha512": "sha512-NzndH096CTulvq+ihr7u1mBJ29oukdi9A8bypQJk+kaHxbRzTTrtgsEEQAb04ptTUZmQa7cgo7uF5z7NuEPX1Q==", "path": "restsharp/106.11.7", "hashPath": "restsharp.106.11.7.nupkg.sha512"}, "SharpCompress/0.30.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqD4TpfyYGa7QTPzaGlMVbcecKnXy4YmYLDWrU+JIj7IuRNl7DH2END+Ll7ekWIY8o3dAMWLFDE1xdhfIWD1nw==", "path": "sharpcompress/0.30.1", "hashPath": "sharpcompress.0.30.1.nupkg.sha512"}, "Snappier/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rFtK2KEI9hIe8gtx3a0YDXdHOpedIf9wYCEYtBEmtlyiWVX3XlCNV03JrmmAi/Cdfn7dxK+k0sjjcLv4fpHnqA==", "path": "snappier/1.0.0", "hashPath": "snappier.1.0.0.nupkg.sha512"}, "System.Buffers/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-lN6tZi7Q46zFzAbRYXTIvfXcyvQQgxnY7Xm6C6xQ9784dEL1amjM6S6Iw4ZpsvesAKnRVsM4scrDQaDqSClkjA==", "path": "system.buffers/4.6.0", "hashPath": "system.buffers.4.6.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-Be0emq8bRmcK4eeJIFUt9+vYPf7kzuQrFs8Ef1CdGvXpq/uSve22PTSkRF09bF/J7wmYJ2DHf2v7GaT3vMXnwQ==", "path": "system.diagnostics.diagnosticsource/9.0.4", "hashPath": "system.diagnostics.diagnosticsource.9.0.4.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-jbfANr2qEmrfEtK3L7tOnkCW5/y2YiF6ISSRhRBgIZL+W2ZbEVHFNTNV8QOKeNU6gedQnhpdU2IvB0YB3nNMjw==", "path": "system.identitymodel.tokens.jwt/8.0.2", "hashPath": "system.identitymodel.tokens.jwt.8.0.2.nupkg.sha512"}, "System.IO.Pipelines/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-UIBaK7c/A3FyQxmX/747xw4rCUkm1BhNiVU617U5jweNJssNjLJkPUGhBsrlDG0BpKWCYKsncD+Kqpy4KmvZZQ==", "path": "system.io.pipelines/9.0.2", "hashPath": "system.io.pipelines.9.0.2.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA==", "path": "system.runtime.compilerservices.unsafe/5.0.0", "hashPath": "system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-/kCGdrXg0PXrvkHYyHubXJHcmCAvJrxTZ7g4XS6UCxY1JW79aMjtUW6UYNECHJmiyFZsZ/vUuWOM4CtNpiNt8Q==", "path": "system.text.encodings.web/9.0.2", "hashPath": "system.text.encodings.web.9.0.2.nupkg.sha512"}, "System.Text.Json/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-4TY2Yokh5Xp8XHFhsY9y84yokS7B0rhkaZCXuRiKppIiKwPVH4lVSFD9EEFzRpXdBM5ZeZXD43tc2vB6njEwwQ==", "path": "system.text.json/9.0.2", "hashPath": "system.text.json.9.0.2.nupkg.sha512"}, "ZstdSharp.Port/0.7.3": {"type": "package", "serviceable": true, "sha512": "sha512-U9Ix4l4cl58Kzz1rJzj5hoVTjmbx1qGMwzAcbv1j/d3NzrFaESIurQyg+ow4mivCgkE3S413y+U9k4WdnEIkRA==", "path": "zstdsharp.port/0.7.3", "hashPath": "zstdsharp.port.0.7.3.nupkg.sha512"}, "RepositoryAdmin/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "RepositoryClient/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Shared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}