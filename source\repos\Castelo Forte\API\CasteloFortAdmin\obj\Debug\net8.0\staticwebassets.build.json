{"Version": 1, "Hash": "/MMbirExKZ3oEkus3qFy0OEVrnY0LUp3ki7Pn3LBS8E=", "Source": "CasteloForteAdmin", "BasePath": "_content/CasteloForteAdmin", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [{"Identity": "C:\\Users\\<USER>\\source\\repos\\Castelo Forte\\API\\CasteloForte\\CasteloForteClient.csproj", "Version": 2, "Source": "CasteloForteClient", "GetPublishAssetsTargets": "ComputeReferencedStaticWebAssetsPublishManifest;GetCurrentProjectPublishStaticWebAssetItems", "AdditionalPublishProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalPublishPropertiesToRemove": "WebPublishProfileFile;TargetFramework", "GetBuildAssetsTargets": "GetCurrentProjectBuildStaticWebAssetItems", "AdditionalBuildProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalBuildPropertiesToRemove": "WebPublishProfileFile;TargetFramework"}], "DiscoveryPatterns": [], "Assets": [], "Endpoints": []}