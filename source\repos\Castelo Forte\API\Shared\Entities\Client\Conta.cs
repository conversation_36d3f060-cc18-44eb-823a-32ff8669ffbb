﻿using MongoDB.Bson.Serialization.Attributes;
using Shared.Entities.Base;
using Shared.Enums;
using System;
using System.ComponentModel.DataAnnotations;

namespace Shared.Entities.Client
{
    /// <summary>
    /// Entidade consolidada que representa tanto contas bancárias quanto cartões de crédito
    /// </summary>
    public class Conta : BaseEntidade
    {
        /// <summary>
        /// Nome da instituição financeira (banco, operadora do cartão)
        /// </summary>
        public string Nome { get; set; } = "";

        /// <summary>
        /// Apelido personalizado para a conta/cartão
        /// </summary>
        public string ApelidoConta { get; set; } = "";

        /// <summary>
        /// Tipo da conta (Poupança, Corrente, Cartão de Crédito)
        /// </summary>
        public AccountType TipoConta { get; set; } = AccountType.ContaCorrente;

        /// <summary>
        /// Saldo atual da conta (para cartões, representa o limite disponível)
        /// </summary>
        public decimal Saldo { get; set; } = 0;

        /// <summary>
        /// Indica se a conta/cartão está ativo
        /// </summary>
        public bool Ativa { get; set; } = true;

        /// <summary>
        /// Ícone da conta para exibição na interface
        /// </summary>
        public string? Icone { get; set; }

        /// <summary>
        /// Cor da conta para exibição na interface
        /// </summary>
        public string? Cor { get; set; }

        /// <summary>
        /// Número da agência
        /// </summary>
        public string? Agencia { get; set; }

        /// <summary>
        /// Número da conta
        /// </summary>
        public string? NumeroConta { get; set; }

        /// <summary>
        /// Número do banco
        /// </summary>
        public string? NumeroBanco { get; set; }

        /// <summary>
        /// Indica se é uma conta pessoa jurídica
        /// </summary>
        public bool ContaPj { get; set; } = false;



        // Propriedades específicas para cartões de crédito
        /// <summary>
        /// Número do cartão (apenas para cartões de crédito)
        /// </summary>
        public string? NumeroCartao { get; set; }

        /// <summary>
        /// Últimos 4 dígitos do cartão (para exibição segura)
        /// </summary>
        public string? UltimosDigitos { get; set; }

        /// <summary>
        /// Data de validade do cartão
        /// </summary>
        [DataType(DataType.Date)]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime? DataValidade { get; set; }

        /// <summary>
        /// CVV do cartão
        /// </summary>
        [MaxLength(4)]
        public string? CVV { get; set; }

        /// <summary>
        /// Dia do fechamento da fatura (1-31)
        /// </summary>
        public int? DiaFechamento { get; set; }

        /// <summary>
        /// Dia do vencimento da fatura (1-31)
        /// </summary>
        public int? DiaVencimento { get; set; }

        /// <summary>
        /// Bandeira do cartão (VISA, MASTER, etc.)
        /// </summary>
        public string? Bandeira { get; set; }

        /// <summary>
        /// Limite total do cartão de crédito
        /// </summary>
        public decimal? LimiteTotal { get; set; }

        /// <summary>
        /// Limite utilizado do cartão
        /// </summary>
        public decimal? LimiteUtilizado { get; set; }

        /// <summary>
        /// Verifica se é um cartão de crédito
        /// </summary>
        public bool IsCartaoCredito => TipoConta == AccountType.CartaoCredito;

        /// <summary>
        /// Verifica se é uma conta bancária
        /// </summary>
        public bool IsContaBancaria => TipoConta.IsContaBancaria();

        /// <summary>
        /// Retorna o limite disponível para cartões de crédito
        /// </summary>
        public decimal LimiteDisponivel => IsCartaoCredito ? (LimiteTotal ?? 0) - (LimiteUtilizado ?? 0) : 0;

        /// <summary>
        /// Retorna o nome de exibição (apelido ou nome da instituição)
        /// </summary>
        public string NomeExibicao => !string.IsNullOrEmpty(ApelidoConta) ? ApelidoConta : Nome;

        /// <summary>
        /// Retorna o número mascarado do cartão para exibição
        /// </summary>
        public string? NumeroMascarado => IsCartaoCredito && !string.IsNullOrEmpty(UltimosDigitos)
            ? $"**** **** **** {UltimosDigitos}"
            : null;
    }
}
