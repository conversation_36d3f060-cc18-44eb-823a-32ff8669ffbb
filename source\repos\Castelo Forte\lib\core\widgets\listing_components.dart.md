import 'package:flutter/material.dart';

/// Componentes reutilizáveis para telas de listagem
/// Baseado no design system da AccountsListScreen
class ListingComponents {
  
  /// Barra de pesquisa com botão de ação
  static Widget buildSearchBarWithAction({
    required TextEditingController controller,
    required VoidCallback onSearch,
    required String hintText,
    IconData searchIcon = Icons.search,
    IconData actionIcon = Icons.refresh,
    String actionTooltip = 'Carregar resultados',
  }) {
    return Container(
      margin: const EdgeInsets.all(20),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: const Color(0xFF16213E),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: const Color(0xFF4ECDC4).withValues(alpha: 0.3),
                ),
              ),
              child: TextField(
                controller: controller,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  hintText: hintText,
                  hintStyle: const TextStyle(color: Colors.white70),
                  prefixIcon: Icon(searchIcon, color: Colors.white70),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 15,
                  ),
                ),
                onSubmitted: (_) => onSearch(),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Container(
            decoration: BoxDecoration(
              color: const Color(0xFF4ECDC4),
              borderRadius: BorderRadius.circular(15),
            ),
            child: IconButton(
              onPressed: onSearch,
              icon: Icon(actionIcon, color: Colors.white),
              tooltip: actionTooltip,
            ),
          ),
        ],
      ),
    );
  }

  /// Filtros horizontais com chips
  static Widget buildHorizontalFilterChips<T>({
    required List<T> options,
    required T selectedOption,
    required Function(T) onOptionSelected,
    required String Function(T) getLabel,
    required Color Function(T) getColor,
    required IconData Function(T) getIcon,
  }) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: options.map((option) {
          final isSelected = option == selectedOption;
          final color = getColor(option);
          final icon = getIcon(option);
          final label = getLabel(option);
          
          return Container(
            margin: EdgeInsets.only(
              left: option == options.first ? 20 : 8,
              right: option == options.last ? 20 : 0,
            ),
            child: FilterChip(
              selected: isSelected,
              label: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    icon,
                    size: 16,
                    color: isSelected ? Colors.white : color,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    label,
                    style: TextStyle(
                      color: isSelected ? Colors.white : color,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              onSelected: (_) => onOptionSelected(option),
              backgroundColor: Colors.transparent,
              selectedColor: color,
              side: BorderSide(color: color),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// Estado de loading
  static Widget buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4ECDC4)),
      ),
    );
  }

  /// Estado de erro
  static Widget buildErrorState({
    required String title,
    required String message,
    required VoidCallback onRetry,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 80, color: Colors.red),
          const SizedBox(height: 20),
          Text(
            title,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.5),
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),
          ElevatedButton.icon(
            onPressed: onRetry,
            icon: const Icon(Icons.refresh, color: Colors.white),
            label: const Text(
              'Tentar Novamente',
              style: TextStyle(color: Colors.white),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4ECDC4),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  /// Estado vazio
  static Widget buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
    VoidCallback? onAction,
    String? actionLabel,
    IconData? actionIcon,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 80,
            color: Colors.white.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 20),
          Text(
            title,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.5),
              fontSize: 14,
            ),
          ),
          if (onAction != null && actionLabel != null) ...[
            const SizedBox(height: 30),
            ElevatedButton.icon(
              onPressed: onAction,
              icon: Icon(actionIcon ?? Icons.add, color: Colors.white),
              label: Text(
                actionLabel,
                style: const TextStyle(color: Colors.white),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4ECDC4),
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Card de lista padrão
  static Widget buildListCard({
    required Widget leading,
    required Widget content,
    required Widget trailing,
    required VoidCallback onTap,
    bool isActive = true,
    Color? accentColor,
  }) {
    final cardOpacity = isActive ? 1.0 : 0.6;
    final cardColor = isActive
        ? const Color(0xFF16213E)
        : const Color(0xFF16213E).withValues(alpha: 0.7);
    final borderColor = accentColor ?? const Color(0xFF4ECDC4);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Opacity(
        opacity: cardOpacity,
        child: Card(
          color: cardColor,
          elevation: isActive ? 2 : 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
            side: BorderSide(
              color: borderColor.withValues(alpha: isActive ? 0.3 : 0.2),
              width: 1,
            ),
          ),
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(15),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  leading,
                  const SizedBox(width: 16),
                  Expanded(child: content),
                  trailing,
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Avatar circular com ícone
  static Widget buildCircularAvatar({
    required IconData icon,
    required Color color,
    double size = 50,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(size / 2),
      ),
      child: Icon(icon, color: color, size: size * 0.48),
    );
  }
}
