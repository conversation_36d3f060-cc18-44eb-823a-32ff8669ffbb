using CasteloForteAdmin.Attributes;
using CasteloForteAdmin.Controllers.ControllerBaseComplemento;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceAdmin.Interfaces;
using Shared.ViewModels.Admin;
using System.Text.Json;

namespace CasteloForteAdmin.Controllers
{
    /// <summary>
    /// Controller dedicado ao gerenciamento de logs de erro do sistema administrativo
    /// </summary>
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class LogErroAdminController : ControllerBaseComplemento<LogErroAdminController>
    {
        private readonly string _controller = "LogErroAdminController";
        private readonly ILogErroAdminService _logErroAdminService;

        public LogErroAdminController(
            ILogErroAdminService logErroAdminService,
            IHistoricoUsuarioService historicoUsuarioService,
            ILogger<LogErroAdminController> logger
            ) : base(logErroAdminService, historicoUsuarioService, logger)
        {
            _logErroAdminService = logErroAdminService ?? throw new ArgumentNullException(nameof(logErroAdminService));
        }

        #region Consultas Básicas de Logs de Erro

        /// <summary>
        /// Busca logs de erro com filtros avançados e paginação
        /// </summary>
        /// <param name="filtro">Filtros para busca</param>
        /// <returns>Resultado paginado de logs de erro</returns>
        [HttpPost("filtrados")]
        [AdminAuthorize]
        public async Task<IActionResult> BuscarLogsFiltrados([FromBody] FiltroLogErroViewModel filtro)
        {
            string variaveis = JsonSerializer.Serialize(filtro);
            try
            {
                string metodo = _controller + " BuscarLogsFiltrados";
                await RegistraAcao(metodo, "Busca de logs de erro com filtros avançados", "", variaveis);

                if (filtro == null)
                    return BadRequest(new { error = "Filtros são obrigatórios" });

                LogInfo($"Iniciando busca filtrada de logs de erro - Página: {filtro.Pagina}", nameof(BuscarLogsFiltrados), _controller);

                var resultado = await _logErroAdminService.BuscarLogsFiltradosAsync(filtro);

                LogInfo($"Busca filtrada concluída. {resultado.TotalRegistros} logs encontrados", nameof(BuscarLogsFiltrados), _controller);
                return Ok(new
                {
                    success = true,
                    message = "Logs de erro filtrados recuperados com sucesso",
                    data = resultado
                });
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controller, variaveis);
            }
        }

        /// <summary>
        /// Busca logs de erro por controller específico
        /// </summary>
        /// <param name="controller">Nome do controller</param>
        /// <returns>Lista de logs de erro do controller</returns>
        [HttpGet("controller/{controller}")]
        public async Task<IActionResult> BuscarPorController(string controller)
        {
            string variaveis = JsonSerializer.Serialize(new { controller });
            try
            {
                string metodo = _controller + " BuscarPorController";
                await RegistraAcao(metodo, "Busca de logs de erro por controller", "", variaveis);

                if (string.IsNullOrWhiteSpace(controller))
                    return BadRequest(new { error = "Nome do controller é obrigatório" });

                LogInfo($"Iniciando busca de logs de erro para controller: {controller}", nameof(BuscarPorController), _controller);

                var logs = await _logErroAdminService.BuscarPorControllerAsync(controller);

                LogInfo($"Busca concluída. {logs.Count()} logs encontrados para o controller", nameof(BuscarPorController), _controller);
                return Ok(new
                {
                    success = true,
                    message = "Logs de erro do controller recuperados com sucesso",
                    data = logs,
                    total = logs.Count()
                });
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controller, variaveis);
            }
        }

        /// <summary>
        /// Busca logs de erro por usuário específico
        /// </summary>
        /// <param name="idUsuario">ID do usuário</param>
        /// <returns>Lista de logs de erro do usuário</returns>
        [HttpGet("usuario/{idUsuario}")]
        public async Task<IActionResult> BuscarPorUsuario(string idUsuario)
        {
            string variaveis = JsonSerializer.Serialize(new { idUsuario });
            try
            {
                string metodo = _controller + " BuscarPorUsuario";
                await RegistraAcao(metodo, "Busca de logs de erro por usuário", "", variaveis);

                if (string.IsNullOrWhiteSpace(idUsuario))
                    return BadRequest(new { error = "ID do usuário é obrigatório" });

                LogInfo($"Iniciando busca de logs de erro para usuário: {idUsuario}", nameof(BuscarPorUsuario), _controller);

                var logs = await _logErroAdminService.BuscarPorUsuarioAsync(idUsuario);

                LogInfo($"Busca concluída. {logs.Count()} logs encontrados para o usuário", nameof(BuscarPorUsuario), _controller);
                return Ok(new
                {
                    success = true,
                    message = "Logs de erro do usuário recuperados com sucesso",
                    data = logs,
                    total = logs.Count()
                });
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controller, variaveis);
            }
        }

        #endregion

        #region Estatísticas e Relatórios

        /// <summary>
        /// Obtém estatísticas detalhadas dos logs de erro
        /// </summary>
        /// <returns>Estatísticas dos logs de erro</returns>
        [HttpGet("estatisticas")]
        [AdminAuthorize]
        public async Task<IActionResult> ObterEstatisticas()
        {
            string variaveis = "";
            try
            {
                string metodo = _controller + " ObterEstatisticas";
                await RegistraAcao(metodo, "Obtenção de estatísticas dos logs de erro", "", variaveis);

                LogInfo("Iniciando obtenção de estatísticas dos logs de erro", nameof(ObterEstatisticas), _controller);

                var estatisticas = await _logErroAdminService.ObterEstatisticasLogsAsync();

                LogInfo("Estatísticas dos logs obtidas com sucesso", nameof(ObterEstatisticas), _controller);
                return Ok(new
                {
                    success = true,
                    message = "Estatísticas dos logs de erro obtidas com sucesso",
                    data = estatisticas
                });
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controller, variaveis);
            }
        }

        #endregion

        #region Consultas Simples (Para compatibilidade)

        /// <summary>
        /// Busca todos os logs de erro (sem filtros)
        /// NOTA: Use BuscarLogsFiltrados para melhor performance
        /// </summary>
        /// <returns>Lista de todos os logs de erro</returns>
        [HttpGet("todos")]
        public async Task<IActionResult> BuscarTodos()
        {
            string variaveis = "";
            try
            {
                string metodo = _controller + " BuscarTodos";
                await RegistraAcao(metodo, "Busca de todos os logs de erro", "", variaveis);

                LogInfo("Iniciando busca de todos os logs de erro", nameof(BuscarTodos), _controller);

                var logs = await _logErroAdminService.BuscarTodosAsync();

                LogInfo($"Busca concluída. {logs.Count()} logs encontrados", nameof(BuscarTodos), _controller);
                return Ok(new
                {
                    success = true,
                    message = "Logs de erro recuperados com sucesso",
                    data = logs,
                    total = logs.Count(),
                    warning = "Para melhor performance, use o endpoint /filtrados"
                });
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controller, variaveis);
            }
        }

        /// <summary>
        /// Busca logs de erro por período
        /// NOTA: Use BuscarLogsFiltrados para melhor flexibilidade
        /// </summary>
        /// <param name="dataInicio">Data de início</param>
        /// <param name="dataFim">Data de fim</param>
        /// <returns>Lista de logs de erro no período</returns>
        [HttpGet("periodo")]
        public async Task<IActionResult> BuscarPorPeriodo([FromQuery] DateTime dataInicio, [FromQuery] DateTime dataFim)
        {
            string variaveis = JsonSerializer.Serialize(new { dataInicio, dataFim });
            try
            {
                string metodo = _controller + " BuscarPorPeriodo";
                await RegistraAcao(metodo, "Busca de logs de erro por período", "", variaveis);

                LogInfo($"Iniciando busca de logs de erro por período: {dataInicio:yyyy-MM-dd} a {dataFim:yyyy-MM-dd}", nameof(BuscarPorPeriodo), _controller);

                var logs = await _logErroAdminService.BuscarPorPeriodoAsync(dataInicio, dataFim);

                LogInfo($"Busca concluída. {logs.Count()} logs encontrados no período", nameof(BuscarPorPeriodo), _controller);
                return Ok(new
                {
                    success = true,
                    message = "Logs de erro do período recuperados com sucesso",
                    data = logs,
                    total = logs.Count(),
                    periodo = new { dataInicio, dataFim },
                    warning = "Para melhor flexibilidade, use o endpoint /filtrados"
                });
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controller, variaveis);
            }
        }

        #endregion
    }
}
