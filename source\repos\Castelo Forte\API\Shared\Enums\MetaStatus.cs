using System.ComponentModel;

namespace Shared.Enums
{
    /// <summary>
    /// Status possíveis para uma meta financeira
    /// </summary>
    public enum MetaStatus
    {
        [Description("Ativa")]
        Ativa = 1,

        [Description("Concluída")]
        Concluida = 2,

        [Description("Cancelada")]
        Cancelada = 3,

        [Description("Pausada")]
        Pausada = 4
    }

    /// <summary>
    /// Extensões para o enum MetaStatus
    /// </summary>
    public static class MetaStatusExtensions
    {
        /// <summary>
        /// Retorna a descrição do status da meta
        /// </summary>
        public static string GetDescription(this MetaStatus status)
        {
            var field = status.GetType().GetField(status.ToString());
            var attribute = (DescriptionAttribute?)Attribute.GetCustomAttribute(field!, typeof(DescriptionAttribute));
            return attribute?.Description ?? status.ToString();
        }

        /// <summary>
        /// Verifica se a meta está ativa
        /// </summary>
        public static bool IsAtiva(this MetaStatus status)
        {
            return status == MetaStatus.Ativa;
        }

        /// <summary>
        /// Verifica se a meta foi concluída
        /// </summary>
        public static bool IsConcluida(this MetaStatus status)
        {
            return status == MetaStatus.Concluida;
        }

        /// <summary>
        /// Verifica se a meta pode receber atualizações de progresso
        /// </summary>
        public static bool PodeReceberProgresso(this MetaStatus status)
        {
            return status == MetaStatus.Ativa || status == MetaStatus.Pausada;
        }

        /// <summary>
        /// Converte string para MetaStatus
        /// </summary>
        public static MetaStatus FromString(string status)
        {
            return status.ToLower() switch
            {
                "ativa" or "em andamento" or "em_andamento" => MetaStatus.Ativa,
                "concluída" or "concluida" or "finalizada" => MetaStatus.Concluida,
                "cancelada" or "cancelado" => MetaStatus.Cancelada,
                "pausada" or "pausado" => MetaStatus.Pausada,
                _ => MetaStatus.Ativa // Default
            };
        }
    }
}
