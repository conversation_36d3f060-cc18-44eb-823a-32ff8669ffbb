using CasteloForteAdmin.Controllers.ControllerBaseComplemento;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceAdmin.Interfaces;
using System.Text.Json;

namespace CasteloForteAdmin.Controllers
{
    /// <summary>
    /// Controller temporário para migrações e atualizações de dados
    /// ATENÇÃO: Este controller deve ser removido após as migrações necessárias
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [AllowAnonymous] // Temporário para permitir execução da migração
    public class MigrationController : ControllerBaseComplemento<MigrationController>
    {
        private readonly string _controller = "MigrationController";
        private readonly IUsuarioService _usuarioService;

        public MigrationController(
            IUsuarioService usuarioService,
            ILogErroAdminService logErroAdminService,
            IHistoricoUsuarioService historicoUsuarioService,
            ILogger<MigrationController> logger
            ) : base(logErroAdminService, historicoUsuarioService, logger)
        {
            _usuarioService = usuarioService ?? throw new ArgumentNullException(nameof(usuarioService));
        }

        /// <summary>
        /// Migração para adicionar a flag FlgAdministrador aos usuários existentes
        /// ATENÇÃO: Execute apenas uma vez e depois remova este endpoint
        /// </summary>
        /// <param name="adminEmails">Lista de emails dos usuários que devem ser marcados como administradores</param>
        /// <returns>Resultado da migração</returns>
        [HttpPost("add-admin-flag")]
        public async Task<IActionResult> AddAdminFlagToUsers([FromBody] List<string>? adminEmails = null)
        {
            string variaveis = JsonSerializer.Serialize(new { adminEmails = adminEmails ?? new List<string>() });

            try
            {
                string metodo = _controller + " AddAdminFlagToUsers";
                await RegistraAcao(metodo, "Migração para adicionar flag de administrador", "", variaveis);

                LogInfo("Iniciando migração para adicionar flag de administrador", nameof(AddAdminFlagToUsers), _controller);

                // Busca todos os usuários
                var usuarios = await _usuarioService.BuscarTodosAsync();
                var usuariosAtualizados = 0;
                var usuariosComErro = 0;
                var resultados = new List<object>();

                // Lista padrão de emails de administradores (caso não seja fornecida)
                var emailsAdmin = adminEmails ?? new List<string>
                {
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>"
                };

                foreach (var usuario in usuarios)
                {
                    try
                    {
                        // Determina se o usuário deve ser administrador
                        bool deveSerAdmin = emailsAdmin.Contains(usuario.Email, StringComparer.OrdinalIgnoreCase) ||
                                          usuario.Nome.Contains("Admin", StringComparison.OrdinalIgnoreCase) ||
                                          usuario.Nome.Contains("Administrador", StringComparison.OrdinalIgnoreCase);

                        // Atualiza apenas se necessário
                        if (usuario.FlgAdministrador != deveSerAdmin)
                        {
                            usuario.FlgAdministrador = deveSerAdmin;
                            await _usuarioService.EditarAsync(usuario);
                            usuariosAtualizados++;

                            resultados.Add(new
                            {
                                Id = usuario.Id,
                                Nome = usuario.Nome,
                                Email = usuario.Email,
                                FlgAdministrador = deveSerAdmin,
                                Status = "Atualizado"
                            });

                            LogInfo($"Usuário atualizado: {usuario.Nome} - Admin: {deveSerAdmin}", 
                                nameof(AddAdminFlagToUsers), _controller);
                        }
                        else
                        {
                            resultados.Add(new
                            {
                                Id = usuario.Id,
                                Nome = usuario.Nome,
                                Email = usuario.Email,
                                FlgAdministrador = usuario.FlgAdministrador,
                                Status = "Já configurado"
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        usuariosComErro++;
                        resultados.Add(new
                        {
                            Id = usuario.Id,
                            Nome = usuario.Nome,
                            Email = usuario.Email,
                            Status = "Erro",
                            Erro = ex.Message
                        });

                        LogInfo($"Erro ao atualizar usuário {usuario.Nome}: {ex.Message}",
                            nameof(AddAdminFlagToUsers), _controller);
                    }
                }

                var resumo = new
                {
                    TotalUsuarios = usuarios.Count(),
                    UsuariosAtualizados = usuariosAtualizados,
                    UsuariosComErro = usuariosComErro,
                    UsuariosJaConfigurados = usuarios.Count() - usuariosAtualizados - usuariosComErro,
                    EmailsAdminConsiderados = emailsAdmin,
                    Detalhes = resultados
                };

                LogInfo($"Migração concluída - Total: {usuarios.Count()}, Atualizados: {usuariosAtualizados}, Erros: {usuariosComErro}", 
                    nameof(AddAdminFlagToUsers), _controller);

                return Ok(new
                {
                    success = true,
                    message = "Migração de flag de administrador concluída",
                    data = resumo,
                    timestamp = DateTimeOffset.UtcNow
                });
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controller, variaveis);
            }
        }

        /// <summary>
        /// Endpoint para verificar o status atual dos usuários em relação à flag de administrador
        /// </summary>
        /// <returns>Status atual dos usuários</returns>
        [HttpGet("check-admin-status")]
        public async Task<IActionResult> CheckAdminStatus()
        {
            string variaveis = "";

            try
            {
                string metodo = _controller + " CheckAdminStatus";
                await RegistraAcao(metodo, "Verificação do status de administradores", "", variaveis);

                LogInfo("Verificando status de administradores", nameof(CheckAdminStatus), _controller);

                var usuarios = await _usuarioService.BuscarTodosAsync();
                
                var administradores = usuarios.Where(u => u.FlgAdministrador).ToList();
                var usuariosComuns = usuarios.Where(u => !u.FlgAdministrador).ToList();

                var resultado = new
                {
                    TotalUsuarios = usuarios.Count(),
                    TotalAdministradores = administradores.Count,
                    TotalUsuariosComuns = usuariosComuns.Count,
                    Administradores = administradores.Select(u => new
                    {
                        Id = u.Id,
                        Nome = u.Nome,
                        Email = u.Email,
                        FlgAtivo = u.FlgAtivo,
                        DtaCadastro = u.DtaCadastro
                    }).ToList(),
                    UsuariosComuns = usuariosComuns.Take(10).Select(u => new
                    {
                        Id = u.Id,
                        Nome = u.Nome,
                        Email = u.Email,
                        FlgAtivo = u.FlgAtivo
                    }).ToList(),
                    MostrandoApenas10UsuariosComuns = usuariosComuns.Count > 10
                };

                LogInfo($"Status verificado - Total: {usuarios.Count()}, Admins: {administradores.Count}", 
                    nameof(CheckAdminStatus), _controller);

                return Ok(new
                {
                    success = true,
                    message = "Status de administradores verificado",
                    data = resultado,
                    timestamp = DateTimeOffset.UtcNow
                });
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controller, variaveis);
            }
        }

        /// <summary>
        /// Endpoint para promover um usuário específico a administrador
        /// </summary>
        /// <param name="usuarioId">ID do usuário a ser promovido</param>
        /// <returns>Resultado da promoção</returns>
        [HttpPost("promote-to-admin/{usuarioId}")]
        public async Task<IActionResult> PromoteToAdmin(string usuarioId)
        {
            string variaveis = JsonSerializer.Serialize(new { usuarioId });

            try
            {
                string metodo = _controller + " PromoteToAdmin";
                await RegistraAcao(metodo, "Promoção de usuário a administrador", "", variaveis);

                if (string.IsNullOrWhiteSpace(usuarioId))
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "ID do usuário é obrigatório",
                        timestamp = DateTimeOffset.UtcNow
                    });
                }

                LogInfo($"Promovendo usuário {usuarioId} a administrador", nameof(PromoteToAdmin), _controller);

                var usuario = await _usuarioService.BuscarPorIdAsync(usuarioId);
                if (usuario == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "Usuário não encontrado",
                        timestamp = DateTimeOffset.UtcNow
                    });
                }

                if (usuario.FlgAdministrador)
                {
                    return Ok(new
                    {
                        success = true,
                        message = "Usuário já é administrador",
                        data = new
                        {
                            Id = usuario.Id,
                            Nome = usuario.Nome,
                            Email = usuario.Email,
                            FlgAdministrador = usuario.FlgAdministrador
                        },
                        timestamp = DateTimeOffset.UtcNow
                    });
                }

                usuario.FlgAdministrador = true;
                await _usuarioService.EditarAsync(usuario);

                LogInfo($"Usuário {usuario.Nome} promovido a administrador com sucesso", nameof(PromoteToAdmin), _controller);

                return Ok(new
                {
                    success = true,
                    message = "Usuário promovido a administrador com sucesso",
                    data = new
                    {
                        Id = usuario.Id,
                        Nome = usuario.Nome,
                        Email = usuario.Email,
                        FlgAdministrador = usuario.FlgAdministrador
                    },
                    timestamp = DateTimeOffset.UtcNow
                });
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controller, variaveis);
            }
        }
    }
}
