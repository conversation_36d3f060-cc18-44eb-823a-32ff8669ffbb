using AutoMapper;
using Microsoft.AspNetCore.Http;
using RepositoryAdmin.Interfaces;
using ServiceAdmin.Interfaces;
using ServiceAdmin.Repository.Generic;
using Shared.Entities.Admin;
using Shared.Utils;
using Shared.ViewModels.Admin;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using Microsoft.Extensions.Configuration;

namespace ServiceAdmin.Service
{
    public class AuthAdminService : GenericAdminService<UsuarioViewModel, Usuario>, IAuthAdminService
    {
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly IConfiguration _configuration;

        public AuthAdminService(
            IUsuarioRepository repository,
            IHttpContextAccessor httpContextAccessor,
            IMapper mapper,
            IConfiguration configuration) 
            : base(repository, httpContextAccessor, mapper)
        {
            _usuarioRepository = repository;
            _configuration = configuration;
        }

        /// <summary>
        /// Autentica um administrador no sistema
        /// </summary>
        /// <param name="loginRequest">Dados de login do administrador</param>
        /// <returns>Resposta com token e dados do administrador</returns>
        public async Task<LoginAdminResponseViewModel> AutenticarAdminAsync(LoginAdminRequestViewModel loginRequest)
        {
            try
            {
                // Criptografa a senha para comparação
                string senhaCriptografada = Criptografias.Encriptar(loginRequest.Senha, false);

                // Busca o usuário por CPF ou email
                Usuario? usuario = null;
                
                if (!string.IsNullOrEmpty(loginRequest.CPF))
                {
                    usuario = await _usuarioRepository.BuscarPrimeiroPorFiltroAsync(x => 
                        x.Cpf == loginRequest.CPF && x.Senha == senhaCriptografada);
                }
                else if (!string.IsNullOrEmpty(loginRequest.Email))
                {
                    usuario = await _usuarioRepository.BuscarPrimeiroPorFiltroAsync(x => 
                        x.Email == loginRequest.Email && x.Senha == senhaCriptografada);
                }

                // Validações de segurança
                if (usuario == null)
                    throw new UnauthorizedAccessException("Credenciais inválidas");

                if (!usuario.FlgAtivo)
                    throw new UnauthorizedAccessException("Usuário inativo");

                if (!usuario.FlgAdministrador)
                    throw new UnauthorizedAccessException("Acesso negado: Usuário não possui privilégios de administrador");

                // Atualiza o último acesso
                await AtualizarUltimoAcessoAsync(usuario.Id!);

                // Gera o token JWT
                var (token, expiresAt) = GerarTokenAdmin(usuario);

                // Monta a resposta
                var response = new LoginAdminResponseViewModel
                {
                    Token = token,
                    TokenType = "Bearer",
                    ExpiresAt = expiresAt,
                    Usuario = MapearUsuarioParaAdminViewModel(usuario),
                    Mensagem = $"Bem-vindo ao painel administrativo, {usuario.Nome}!"
                };

                return response;
            }
            catch (UnauthorizedAccessException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao autenticar administrador: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Valida um token de administrador
        /// </summary>
        /// <param name="token">Token JWT a ser validado</param>
        /// <returns>Resposta com dados de validação</returns>
        public async Task<ValidateAdminTokenResponseViewModel> ValidarTokenAdminAsync(string token)
        {
            try
            {
                var response = new ValidateAdminTokenResponseViewModel();

                // Valida o token JWT
                var principal = ValidarTokenJWT(token);
                if (principal == null)
                {
                    response.IsValid = false;
                    response.IsAdmin = false;
                    response.MensagemErro = "Token inválido ou expirado";
                    return response;
                }

                // Obtém o ID do usuário do token
                var usuarioId = principal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(usuarioId))
                {
                    response.IsValid = false;
                    response.IsAdmin = false;
                    response.MensagemErro = "ID do usuário não encontrado no token";
                    return response;
                }

                // Busca o usuário no banco
                var usuario = await BuscarPorIdAsync(usuarioId);
                if (usuario == null)
                {
                    response.IsValid = false;
                    response.IsAdmin = false;
                    response.MensagemErro = "Usuário não encontrado";
                    return response;
                }

                // Verifica se ainda é administrador e está ativo
                if (!usuario.FlgAtivo)
                {
                    response.IsValid = false;
                    response.IsAdmin = false;
                    response.MensagemErro = "Usuário inativo";
                    return response;
                }

                if (!usuario.FlgAdministrador)
                {
                    response.IsValid = false;
                    response.IsAdmin = false;
                    response.MensagemErro = "Usuário não possui mais privilégios de administrador";
                    return response;
                }

                // Token válido e usuário é administrador
                response.IsValid = true;
                response.IsAdmin = true;
                response.ExpiresAt = DateTime.FromBinary(long.Parse(principal.FindFirst("exp")?.Value ?? "0"));
                response.Usuario = MapearUsuarioParaAdminViewModel(await _usuarioRepository.BuscarPorIdAsync(usuarioId));

                return response;
            }
            catch (Exception ex)
            {
                return new ValidateAdminTokenResponseViewModel
                {
                    IsValid = false,
                    IsAdmin = false,
                    MensagemErro = $"Erro ao validar token: {ex.Message}"
                };
            }
        }

        #region Métodos Privados

        /// <summary>
        /// Gera um token JWT específico para administradores
        /// </summary>
        /// <param name="usuario">Dados do usuário administrador</param>
        /// <returns>Token JWT e data de expiração</returns>
        private (string token, DateTime expiresAt) GerarTokenAdmin(Usuario usuario)
        {
            var jwtSecret = _configuration["JwtSettings:Secret"] ?? 
                throw new InvalidOperationException("JWT Secret não configurado");
            
            var jwtEmissor = _configuration["JwtSettings:Emissor"] ?? "CasteloForteAdmin";
            var jwtAudiencia = _configuration["JwtSettings:Audiencia"] ?? "CasteloForteAdmin";
            var jwtExpiracaoHoras = int.Parse(_configuration["JwtSettings:ExpiracaoHoras"] ?? "8");

            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(jwtSecret);
            var expiresAt = DateTime.UtcNow.AddHours(jwtExpiracaoHoras);

            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, usuario.Id!),
                new(ClaimTypes.Name, usuario.Nome),
                new(ClaimTypes.Email, usuario.Email),
                new("cpf", usuario.Cpf),
                new("isAdmin", "true"),
                new("adminLevel", "system"), // Nível de administrador
                new("tokenType", "admin")
            };

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = expiresAt,
                Issuer = jwtEmissor,
                Audience = jwtAudiencia,
                SigningCredentials = new SigningCredentials(
                    new SymmetricSecurityKey(key),
                    SecurityAlgorithms.HmacSha256Signature)
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            return (tokenHandler.WriteToken(token), expiresAt);
        }

        /// <summary>
        /// Valida um token JWT
        /// </summary>
        /// <param name="token">Token a ser validado</param>
        /// <returns>ClaimsPrincipal se válido, null se inválido</returns>
        private ClaimsPrincipal? ValidarTokenJWT(string token)
        {
            try
            {
                var jwtSecret = _configuration["JwtSettings:Secret"] ?? 
                    throw new InvalidOperationException("JWT Secret não configurado");

                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.ASCII.GetBytes(jwtSecret);

                var validationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidIssuer = _configuration["JwtSettings:Emissor"],
                    ValidateAudience = true,
                    ValidAudience = _configuration["JwtSettings:Audiencia"],
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero
                };

                var principal = tokenHandler.ValidateToken(token, validationParameters, out _);
                return principal;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Mapeia Usuario para UsuarioAdminAutenticadoViewModel
        /// </summary>
        /// <param name="usuario">Entidade Usuario</param>
        /// <returns>ViewModel do administrador autenticado</returns>
        private static UsuarioAdminAutenticadoViewModel MapearUsuarioParaAdminViewModel(Usuario? usuario)
        {
            if (usuario == null)
                return new UsuarioAdminAutenticadoViewModel();

            return new UsuarioAdminAutenticadoViewModel
            {
                Id = usuario.Id ?? string.Empty,
                Nome = usuario.Nome,
                Email = usuario.Email,
                CpfMascarado = MascararCpf(usuario.Cpf),
                DtaUltimoAcesso = usuario.DtaUltimoAcesso,
                FlgAdministrador = usuario.FlgAdministrador,
                DtaCadastro = usuario.DtaCadastro,
                FlgAtivo = usuario.FlgAtivo
            };
        }

        /// <summary>
        /// Mascara o CPF para exibição segura
        /// </summary>
        /// <param name="cpf">CPF completo</param>
        /// <returns>CPF mascarado</returns>
        private static string MascararCpf(string cpf)
        {
            if (string.IsNullOrEmpty(cpf) || cpf.Length < 11)
                return "***.***.***-**";

            return $"{cpf.Substring(0, 3)}.***.***.{cpf.Substring(cpf.Length - 2)}";
        }

        /// <summary>
        /// Atualiza a data do último acesso do usuário
        /// </summary>
        /// <param name="usuarioId">ID do usuário</param>
        private async Task AtualizarUltimoAcessoAsync(string usuarioId)
        {
            try
            {
                var usuario = await _usuarioRepository.BuscarPorIdAsync(usuarioId);
                if (usuario != null)
                {
                    usuario.DtaUltimoAcesso = DateTime.Now;
                    await _usuarioRepository.EditarAsync(usuario);
                }
            }
            catch
            {
                // Falha silenciosa - não deve impedir o login
            }
        }

        #endregion
    }
}
