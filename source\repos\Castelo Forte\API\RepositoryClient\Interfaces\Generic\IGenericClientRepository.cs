﻿using System.Linq.Expressions;

namespace RepositoryClient.Interfaces.Generic
{
    /// <summary>
    /// Interface genérica para operações de repositório.
    /// </summary>
    /// <typeparam name="TEntidade">Tipo da entidade a ser gerenciada pelo repositório.</typeparam>
    public interface IGenericClientRepository<TEntidade> where TEntidade : class
    {
        /// <summary>
        /// Adiciona uma nova entidade ao repositório.
        /// </summary>
        /// <param name="Object">Entidade a ser adicionada.</param>
        /// <returns>Entidade adicionada.</returns>
        Task<TEntidade?> AdicionarAsync(TEntidade Object);

        /// <summary>
        /// Edita uma entidade existente no repositório.
        /// </summary>
        /// <param name="Object">Entidade a ser editada.</param>
        /// <returns>Entidade editada.</returns>
        Task<TEntidade?> EditarAsync(TEntidade Object);
        Task<List<TEntidade>> EditarArrayAsync(List<TEntidade> array);

        /// <summary>
        /// Exclui uma entidade do repositório.
        /// </summary>
        /// <param name="Object">Entidade a ser excluída.</param>
        Task ExcluirAsync(TEntidade Object);

        /// <summary>
        /// Busca uma entidade pelo seu identificador.
        /// </summary>
        /// <param name="Id">Identificador da entidade.</param>
        /// <returns>Entidade encontrada ou null.</returns>
        Task<TEntidade?> BuscarPorIdAsync(string? Id);

        /// <summary>
        /// Busca todas as entidades.
        /// </summary>
        /// <returns>Coleção de todas as entidades.</returns>
        Task<IEnumerable<TEntidade?>> BuscarTodosAsync();

        /// <summary>
        /// Lista entidades que satisfazem uma expressão.
        /// </summary>
        /// <param name="expression">Expressão lambda para filtrar entidades.</param>
        /// <returns>Coleção de entidades que satisfazem a expressão.</returns>
        Task<IEnumerable<TEntidade?>> BuscarPorFiltroAsync(Expression<Func<TEntidade?, bool>> expression);

        /// <summary>
        /// Lista entidades que satisfazem uma expressão com paginação.
        /// </summary>
        /// <param name="expression">Expressão lambda para filtrar entidades.</param>
        /// <param name="page">Número da página.</param>
        /// <param name="pageSize">Tamanho da página.</param>
        /// <returns>Coleção de entidades que satisfazem a expressão.</returns>
        Task<IEnumerable<TEntidade?>> BuscarPorFiltroPaginadoAsync(Expression<Func<TEntidade?, bool>> expression, int page, int pageSize);

        /// <summary>
        /// Busca o número total de entidades que satisfazem uma expressão.
        /// </summary>
        /// <param name="expression">Expressão lambda para filtrar entidades.</param>
        /// <returns>Contagem de entidades.</returns>
        Task<int> BuscarContagemTotalPorFiltroAsync(Expression<Func<TEntidade?, bool>> expression);

        /// <summary>
        /// Busca o número total de entidades.
        /// </summary>
        /// <returns>Contagem total de entidades.</returns>
        Task<int> BuscarContagemTotalAsync();

        Task<List<TEntidade>> AdicionarArrayAsync(List<TEntidade> array);
        Task<TEntidade?> BuscarPrimeiroPorFiltroAsync(Expression<Func<TEntidade?, bool>> expression);
    }
}