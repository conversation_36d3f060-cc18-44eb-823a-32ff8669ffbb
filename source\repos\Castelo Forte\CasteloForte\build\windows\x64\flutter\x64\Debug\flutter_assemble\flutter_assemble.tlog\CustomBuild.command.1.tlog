^C:\USERS\<USER>\SOURCE\REPOS\CASTELO FORTE\CASTELOFORTE\BUILD\WINDOWS\X64\CMAKEFILES\12C02A8DC09275B027C2620672A0417D\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=C:\flutter "PROJECT_DIR=C:\Users\<USER>\source\repos\Castelo Forte\CasteloForte" FLUTTER_ROOT=C:\flutter "FLUTTER_EPHEMERAL_DIR=C:\Users\<USER>\source\repos\Castelo Forte\CasteloForte\windows\flutter\ephemeral" "PROJECT_DIR=C:\Users\<USER>\source\repos\Castelo Forte\CasteloForte" "FLUTTER_TARGET=C:\Users\<USER>\source\repos\Castelo Forte\CasteloForte\lib\main.dart" DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuNQ==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049ZmNmMmMxMTU3Mg==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049ZGQ5M2RlNmZiMQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE= DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false "PACKAGE_CONFIG=C:\Users\<USER>\source\repos\Castelo Forte\CasteloForte\.dart_tool\package_config.json" C:/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\SOURCE\REPOS\CASTELO FORTE\CASTELOFORTE\BUILD\WINDOWS\X64\CMAKEFILES\47902860C3FB59A7F87CA8589BF6979C\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\SOURCE\REPOS\CASTELO FORTE\CASTELOFORTE\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SC:/Users/<USER>/source/repos/Castelo Forte/CasteloForte/windows" "-BC:/Users/<USER>/source/repos/Castelo Forte/CasteloForte/build/windows/x64" --check-stamp-file "C:/Users/<USER>/source/repos/Castelo Forte/CasteloForte/build/windows/x64/flutter/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
