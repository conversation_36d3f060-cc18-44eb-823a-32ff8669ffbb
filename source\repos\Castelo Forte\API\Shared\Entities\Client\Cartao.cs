﻿using MongoDB.Bson.Serialization.Attributes;
using Shared.Entities.Base;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shared.Entities.Client
{
    public class Cartao : BaseEntidade
    {
        public string NumeroCartao { get; set; } = "";
        public string NomeCartao { get; set; } = "";

        [DataType(DataType.Date)]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime DtaValidade { get; set; } = DateTime.Now;

        [MaxLength(3)]
        public string CVV { get; set; } = "";

        public int DiaFechamento { get; set; }
        public int DiaVencimento { get; set; }
        public string ApelidoCartao { get; set; } = "";

    }
}
