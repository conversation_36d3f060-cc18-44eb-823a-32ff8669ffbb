import 'package:flutter/material.dart';
import '../../accounts/presentation/add_account_screen.dart';
import '../../cards/presentation/add_card_screen.dart';

class BankConnectionScreen extends StatefulWidget {
  const BankConnectionScreen({super.key});

  @override
  State<BankConnectionScreen> createState() => _BankConnectionScreenState();
}

class _BankConnectionScreenState extends State<BankConnectionScreen> {
  final List<BankData> _availableBanks = [
    BankData(
      name: 'Nubank',
      logo: '💜',
      isSupported: true,
      description: 'Conecte sua conta Nubank automaticamente',
    ),
    BankData(
      name: 'Itaú',
      logo: '🔶',
      isSupported: true,
      description: 'Sincronize suas transações do Itaú',
    ),
    BankData(
      name: 'Bradesco',
      logo: '🔴',
      isSupported: true,
      description: 'Importe dados do Bradesco',
    ),
    BankData(
      name: 'Banco do Brasil',
      logo: '🟡',
      isSupported: true,
      description: 'Conecte sua conta do BB',
    ),
    BankData(
      name: 'Santander',
      logo: '🔴',
      isSupported: true,
      description: 'Sincronize com Santander',
    ),
    BankData(
      name: 'Caixa',
      logo: '🔵',
      isSupported: false,
      description: 'Em breve disponível',
    ),
    BankData(
      name: 'Inter',
      logo: '🧡',
      isSupported: true,
      description: 'Conecte sua conta Inter',
    ),
    BankData(
      name: 'C6 Bank',
      logo: '⚫',
      isSupported: true,
      description: 'Importe dados do C6',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Conexão Bancária',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header informativo
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFF16213E),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.security, color: Colors.blue, size: 24),
                      const SizedBox(width: 12),
                      const Text(
                        'Conexão Segura',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    'Conecte suas contas bancárias de forma segura usando Open Banking. Seus dados são criptografados e protegidos.',
                    style: TextStyle(color: Colors.white70),
                  ),
                  const SizedBox(height: 15),
                  Row(
                    children: [
                      _buildFeatureChip('🔒 Criptografado'),
                      const SizedBox(width: 8),
                      _buildFeatureChip('🏦 Open Banking'),
                      const SizedBox(width: 8),
                      _buildFeatureChip('⚡ Automático'),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 30),

            // Título da seção
            const Text(
              'Bancos Disponíveis',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 15),

            // Lista de bancos
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _availableBanks.length,
              itemBuilder: (context, index) {
                final bank = _availableBanks[index];
                return _buildBankCard(bank);
              },
            ),

            const SizedBox(height: 30),

            // Opção manual
            _buildManualOptionCard(),

            const SizedBox(height: 20),

            // Informações de segurança
            _buildSecurityInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureChip(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.blue,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildBankCard(BankData bank) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: bank.isSupported ? () => _connectBank(bank) : null,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: bank.isSupported
                ? const Color(0xFF16213E)
                : Colors.grey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: bank.isSupported
                  ? Colors.white10
                  : Colors.grey.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: bank.isSupported
                      ? Colors.white10
                      : Colors.grey.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Center(
                  child: Text(bank.logo, style: const TextStyle(fontSize: 24)),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      bank.name,
                      style: TextStyle(
                        color: bank.isSupported ? Colors.white : Colors.grey,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      bank.description,
                      style: TextStyle(
                        color: bank.isSupported ? Colors.white70 : Colors.grey,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                bank.isSupported ? Icons.arrow_forward_ios : Icons.lock,
                color: bank.isSupported ? Colors.white54 : Colors.grey,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildManualOptionCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF16213E),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.edit, color: Colors.orange, size: 24),
              const SizedBox(width: 12),
              const Text(
                'Adicionar Manualmente',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          const Text(
            'Prefere adicionar suas contas manualmente? Você pode criar contas e cartões personalizados.',
            style: TextStyle(color: Colors.white70),
          ),
          const SizedBox(height: 15),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _addManualAccount(),
                  icon: const Icon(Icons.account_balance, color: Colors.white),
                  label: const Text(
                    'Adicionar Conta',
                    style: TextStyle(color: Colors.white),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _addManualCard(),
                  icon: const Icon(Icons.credit_card, color: Colors.white),
                  label: const Text(
                    'Adicionar Cartão',
                    style: TextStyle(color: Colors.white),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityInfo() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.green.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.verified_user, color: Colors.green, size: 24),
              const SizedBox(width: 12),
              const Text(
                'Sua Segurança é Prioridade',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          const Text(
            '• Utilizamos protocolos de segurança bancária\n'
            '• Seus dados são criptografados ponta a ponta\n'
            '• Não armazenamos senhas ou dados sensíveis\n'
            '• Certificação Open Banking do Banco Central',
            style: TextStyle(color: Colors.white70, height: 1.5),
          ),
        ],
      ),
    );
  }

  void _connectBank(BankData bank) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213E),
        title: Text(
          'Conectar ${bank.name}',
          style: const TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Você será redirecionado para o site oficial do ${bank.name} para autorizar a conexão.',
              style: const TextStyle(color: Colors.white70),
            ),
            const SizedBox(height: 15),
            const Text(
              'Passos:',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              '1. Login no ${bank.name}\n'
              '2. Autorizar acesso aos dados\n'
              '3. Retornar ao Castelo Forte',
              style: const TextStyle(color: Colors.white70),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancelar',
              style: TextStyle(color: Colors.white70),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _startBankConnection(bank);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
            ),
            child: const Text(
              'Conectar',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  void _startBankConnection(BankData bank) async {
    // Mostrar dialog de loading
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213E),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
            ),
            const SizedBox(height: 20),
            Text(
              'Conectando com ${bank.name}...',
              style: const TextStyle(color: Colors.white),
            ),
          ],
        ),
      ),
    );

    try {
      // Processo de conexão com Open Banking
      await _connectToBank(bank);

      if (mounted) {
        Navigator.pop(context); // Close loading dialog

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${bank.name} conectado com sucesso!'),
            backgroundColor: Colors.green,
          ),
        );

        Navigator.pop(context); // Return to previous screen
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close loading dialog

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Erro ao conectar com ${bank.name}: Funcionalidade ainda não disponível',
            ),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  void _addManualAccount() {
    Navigator.pop(context);
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddAccountScreen()),
    );
  }

  void _addManualCard() {
    Navigator.pop(context);
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddCardScreen()),
    );
  }

  Future<void> _connectToBank(BankData bank) async {
    try {
      // Implementação real de conexão bancária seria feita aqui
      // Por enquanto, apenas mostra que a funcionalidade não está disponível
      await Future.delayed(const Duration(seconds: 2));

      // TODO: Implementar integração real com Open Banking
      // 1. Redirecionar para página de autorização do banco
      // 2. Aguardar callback de autorização
      // 3. Trocar código de autorização por token de acesso
      // 4. Buscar dados das contas do usuário
      // 5. Salvar dados no backend

      throw Exception('Integração bancária ainda não implementada');
    } catch (e) {
      rethrow;
    }
  }
}

class BankData {
  final String name;
  final String logo;
  final bool isSupported;
  final String description;

  BankData({
    required this.name,
    required this.logo,
    required this.isSupported,
    required this.description,
  });
}
