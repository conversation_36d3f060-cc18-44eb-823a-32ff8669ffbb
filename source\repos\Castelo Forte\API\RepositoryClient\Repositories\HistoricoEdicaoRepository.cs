﻿using Microsoft.AspNetCore.Http;
using RepositoryAdmin.Interfaces;
using RepositoryClient.Configuration.Interfaces;
using RepositoryClient.Interfaces;
using RepositoryClient.Repositories.Generic;
using Shared.Entities.Client;

namespace RepositoryClient.Repositories
{
    public class HistoricoEdicaoRepository(
        IContextoMultiTenantService contextoMultiTenant,
        IHttpContextAccessor httpContextAccessor,
         IUsuarioRepository usuarioRepository) : GenericClientRepository<HistoricoEdicao>(contextoMultiTenant, httpContextAccessor, usuarioRepository, "HistoricoEdicao"), IHistoricoEdicaoRepository
    {
    }
}
