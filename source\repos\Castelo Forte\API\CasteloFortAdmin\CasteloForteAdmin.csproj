<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.14" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.5" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CasteloForte\CasteloForteClient.csproj" />
    <ProjectReference Include="..\RepositoryAdmin\RepositoryAdmin.csproj" />
    <ProjectReference Include="..\RepositoryClient\RepositoryClient.csproj" />
    <ProjectReference Include="..\ServiceAdmin\ServiceAdmin.csproj" />
    <ProjectReference Include="..\ServiceClient\ServiceClient.csproj" />
    <ProjectReference Include="..\Shared\Shared.csproj" />
  </ItemGroup>

</Project>
