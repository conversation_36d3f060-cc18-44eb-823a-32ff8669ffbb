feat(cards): Refactor CardsListScreen to use AppColors for consistent theming

- Updated color references in CardsListScreen to use AppColors for better maintainability.
- Improved loading and error handling states.
- Enhanced search functionality and filter options.
- Cleaned up code formatting and structure for readability.

feat(goals): Add date filtering functionality to GoalsListScreen

- Introduced GoalsDateFilter widget for filtering goals by date.
- Implemented lifecycle management to reload goals when app resumes.
- Enhanced loading and error handling in GoalsListScreen.
- Added support for filtering open goals and custom date ranges.

feat(goals): Extend MetaFilterModel to include open goals filter

- Added 'apenasAbertas' property to MetaFilterModel for filtering open goals.
- Updated filtering logic in GoalsListScreen to utilize new filter options.
