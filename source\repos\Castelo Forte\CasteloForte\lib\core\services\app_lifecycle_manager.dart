import 'package:flutter/material.dart';
import 'secure_storage_service.dart';
import 'logger_service.dart';

/// Gerenciador do ciclo de vida da aplicação
class AppLifecycleManager extends WidgetsBindingObserver {
  static AppLifecycleManager? _instance;
  static AppLifecycleManager get instance {
    _instance ??= AppLifecycleManager._();
    return _instance!;
  }

  AppLifecycleManager._();

  bool _isInitialized = false;
  AppLifecycleState? _lastState;
  DateTime? _lastPauseTime;
  String? _currentRoute;

  /// Callbacks para eventos do ciclo de vida
  VoidCallback? onAppResumed;
  VoidCallback? onAppPaused;
  VoidCallback? onAppClosed;
  VoidCallback? onAppDetached;

  /// Inicializa o gerenciador de ciclo de vida
  Future<void> initialize() async {
    if (_isInitialized) return;

    WidgetsBinding.instance.addObserver(this);
    _isInitialized = true;

    // Marca o app como ativo
    await SecureStorageService.markAppAsActive();

    LoggerService.success('AppLifecycleManager inicializado');
  }

  /// Finaliza o gerenciador de ciclo de vida
  void dispose() {
    if (_isInitialized) {
      WidgetsBinding.instance.removeObserver(this);
      _isInitialized = false;
      LoggerService.info('AppLifecycleManager finalizado');
    }
  }

  /// Define a rota atual
  void setCurrentRoute(String route) {
    _currentRoute = route;
  }

  /// Obtém a rota atual
  String? getCurrentRoute() {
    return _currentRoute;
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    LoggerService.info(
      'Mudança de estado do app: ${_lastState?.name} -> ${state.name}',
    );

    _handleLifecycleChange(_lastState, state);
    _lastState = state;
  }

  /// Manipula mudanças no ciclo de vida
  void _handleLifecycleChange(
    AppLifecycleState? previous,
    AppLifecycleState current,
  ) {
    switch (current) {
      case AppLifecycleState.resumed:
        _handleAppResumed(previous);
        break;
      case AppLifecycleState.paused:
        _handleAppPaused();
        break;
      case AppLifecycleState.detached:
        _handleAppDetached();
        break;
      case AppLifecycleState.inactive:
        _handleAppInactive();
        break;
      case AppLifecycleState.hidden:
        _handleAppHidden();
        break;
    }
  }

  /// Manipula quando o app é retomado
  void _handleAppResumed(AppLifecycleState? previousState) async {
    LoggerService.info('App retomado');

    // Se estava pausado, calcula o tempo de pausa
    if (_lastPauseTime != null) {
      final pauseDuration = DateTime.now().difference(_lastPauseTime!);
      LoggerService.info(
        'App ficou pausado por: ${pauseDuration.inSeconds} segundos',
      );

      // Se ficou pausado por mais de 5 minutos, considera como "fechado"
      if (pauseDuration.inMinutes > 5) {
        await SecureStorageService.markAppAsClosed(lastRoute: _currentRoute);
        LoggerService.info(
          'App considerado como fechado devido ao tempo de pausa',
        );
      }
    }

    // Marca como ativo novamente
    await SecureStorageService.markAppAsActive();

    onAppResumed?.call();
    _lastPauseTime = null;
  }

  /// Manipula quando o app é pausado
  void _handleAppPaused() async {
    LoggerService.info('App pausado');
    _lastPauseTime = DateTime.now();

    // Salva o estado atual
    await SecureStorageService.saveAppState(
      wasClosedProperly: false,
      lastActiveTime: DateTime.now(),
      lastRoute: _currentRoute,
    );

    onAppPaused?.call();
  }

  /// Manipula quando o app é desanexado (fechado)
  void _handleAppDetached() async {
    LoggerService.info('App desanexado/fechado');

    // Marca como fechado adequadamente
    await SecureStorageService.markAppAsClosed(lastRoute: _currentRoute);

    onAppDetached?.call();
    onAppClosed?.call();
  }

  /// Manipula quando o app fica inativo
  void _handleAppInactive() {
    LoggerService.info('App inativo');
  }

  /// Manipula quando o app fica oculto
  void _handleAppHidden() {
    LoggerService.info('App oculto');
  }

  /// Verifica se o app precisa de re-autenticação
  Future<bool> needsReAuthentication() async {
    try {
      // Verifica se o app foi fechado e reaberto
      final wasClosedAndReopened =
          await SecureStorageService.wasAppClosedAndReopened();

      if (!wasClosedAndReopened) {
        LoggerService.info(
          'App não foi fechado e reaberto - não precisa de re-autenticação',
        );
        return false;
      }

      // Verifica se há credenciais armazenadas
      final hasCredentials = await SecureStorageService.hasStoredCredentials();

      if (!hasCredentials) {
        LoggerService.info(
          'Não há credenciais armazenadas - não precisa de re-autenticação',
        );
        return false;
      }

      // Verifica se há token de autenticação
      final hasToken = await SecureStorageService.hasAuthToken();

      if (!hasToken) {
        LoggerService.info(
          'Não há token de autenticação - não precisa de re-autenticação',
        );
        return false;
      }

      LoggerService.info('App precisa de re-autenticação');
      return true;
    } catch (e) {
      LoggerService.error(
        'Erro ao verificar necessidade de re-autenticação: $e',
      );
      return false;
    }
  }

  /// Obtém informações para re-autenticação
  Future<Map<String, dynamic>?> getReAuthenticationInfo() async {
    try {
      final credentials = await SecureStorageService.getUserCredentials();
      final lastRoute = await SecureStorageService.getLastRoute();

      if (credentials == null) return null;

      return {
        'cpf': credentials['cpf'],
        'email': credentials['email'],
        'nome': credentials['nome'],
        'lastRoute': lastRoute,
      };
    } catch (e) {
      LoggerService.error('Erro ao obter informações de re-autenticação: $e');
      return null;
    }
  }

  /// Força o fechamento do app (para testes)
  Future<void> forceAppClosure() async {
    await SecureStorageService.markAppAsClosed(lastRoute: _currentRoute);
    LoggerService.info('App marcado como fechado forçadamente');
  }

  /// Reseta o estado do app (para testes)
  Future<void> resetAppState() async {
    await SecureStorageService.markAppAsActive();
    _lastPauseTime = null;
    _lastState = null;
    LoggerService.info('Estado do app resetado');
  }

  /// Simula fechamento e reabertura do app (para testes)
  Future<void> simulateAppClosureAndReopen() async {
    // Simula fechamento
    await SecureStorageService.markAppAsClosed(lastRoute: _currentRoute);

    // Aguarda um pouco
    await Future.delayed(const Duration(seconds: 1));

    // Simula reabertura
    await SecureStorageService.markAppAsActive();

    LoggerService.info('Simulação de fechamento e reabertura concluída');
  }

  /// Obtém estatísticas do ciclo de vida
  Map<String, dynamic> getLifecycleStats() {
    return {
      'isInitialized': _isInitialized,
      'lastState': _lastState?.name,
      'currentRoute': _currentRoute,
      'lastPauseTime': _lastPauseTime?.toIso8601String(),
      'isPaused': _lastPauseTime != null,
    };
  }
}
