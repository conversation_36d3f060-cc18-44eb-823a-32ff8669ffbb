import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:casteloforte/features/auth/presentation/login_screen.dart';

void main() {
  group('Login Screen Tests', () {
    testWidgets('Login screen displays all required elements', (WidgetTester tester) async {
      // Build the login screen
      await tester.pumpWidget(
        const MaterialApp(
          home: LoginScreen(),
        ),
      );

      // Verify that key elements are present
      expect(find.text('Bem-vindo de volta!'), findsOneWidget);
      expect(find.text('Entre na sua conta Castelo Forte'), findsOneWidget);
      
      // Verify form fields
      expect(find.byType(TextFormField), findsNWidgets(2)); // Email and password
      expect(find.text('Email'), findsOneWidget);
      expect(find.text('Senha'), findsOneWidget);
      
      // Verify buttons
      expect(find.text('Entrar'), findsOneWidget);
      expect(find.text('Criar nova conta'), findsOneWidget);
      expect(find.text('<PERSON><PERSON><PERSON> minha senha'), findsOneWidget);
      
      // Verify checkbox
      expect(find.text('Lembrar de mim'), findsOneWidget);
      expect(find.byType(Checkbox), findsOneWidget);
    });

    testWidgets('Email validation works correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: LoginScreen(),
        ),
      );

      // Find the email field and enter invalid email
      final emailField = find.widgetWithText(TextFormField, 'Email');
      await tester.enterText(emailField, 'invalid-email');
      
      // Tap login button to trigger validation
      await tester.tap(find.text('Entrar'));
      await tester.pump();

      // Should show validation error
      expect(find.text('Por favor, digite um email válido'), findsOneWidget);
    });

    testWidgets('Password visibility toggle works', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: LoginScreen(),
        ),
      );

      // Find the password field
      final passwordField = find.widgetWithText(TextFormField, 'Senha');
      await tester.enterText(passwordField, 'testpassword');
      
      // Find and tap the visibility toggle button
      final visibilityButton = find.byIcon(Icons.visibility);
      expect(visibilityButton, findsOneWidget);
      
      await tester.tap(visibilityButton);
      await tester.pump();
      
      // After tapping, should show visibility_off icon
      expect(find.byIcon(Icons.visibility_off), findsOneWidget);
    });
  });
}
