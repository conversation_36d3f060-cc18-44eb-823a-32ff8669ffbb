import 'package:flutter/material.dart';

class FinancialAreaScreen extends StatelessWidget {
  const FinancialAreaScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Área Financeira',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: <PERSON>umn(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header com resumo
            _buildFinancialSummary(),
            const SizedBox(height: 30),

            // Seções principais
            _buildSection(
              'Investimentos',
              'Gerencie seus investimentos e acompanhe rentabilidade',
              Icons.trending_up,
              Colors.green,
              () => _showComingSoon(context, 'Investimentos'),
            ),
            const SizedBox(height: 15),

            _buildSection(
              'Empréstimos',
              'Simule e acompanhe empréstimos e financiamentos',
              Icons.account_balance,
              Colors.orange,
              () => _showComingSoon(context, 'Empréstimos'),
            ),
            const SizedBox(height: 15),

            _buildSection(
              'Seguros',
              'Gerencie suas apólices e coberturas',
              Icons.security,
              Colors.blue,
              () => _showComingSoon(context, 'Seguros'),
            ),
            const SizedBox(height: 15),

            _buildSection(
              'Planejamento',
              'Planeje sua aposentadoria e objetivos financeiros',
              Icons.timeline,
              Colors.purple,
              () => _showComingSoon(context, 'Planejamento'),
            ),
            const SizedBox(height: 15),

            _buildSection(
              'Educação Financeira',
              'Aprenda sobre finanças pessoais e investimentos',
              Icons.school,
              Colors.teal,
              () => _showComingSoon(context, 'Educação Financeira'),
            ),
            const SizedBox(height: 15),

            _buildSection(
              'Consultoria',
              'Agende uma consulta com nossos especialistas',
              Icons.person_outline,
              Colors.indigo,
              () => _showComingSoon(context, 'Consultoria'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialSummary() {
    return Container(
      padding: const EdgeInsets.all(25),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF6A1B9A),
            Color(0xFF8E24AA),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF6A1B9A).withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.diamond,
                color: Colors.white,
                size: 32,
              ),
              const SizedBox(width: 15),
              const Expanded(
                child: Text(
                  'Área Financeira Premium',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          const Text(
            'Acesse ferramentas avançadas para maximizar seus resultados financeiros',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 25),
          Row(
            children: [
              Expanded(
                child: _buildSummaryItem(
                  'Patrimônio Total',
                  'R\$ 125.430,00',
                  Icons.account_balance_wallet,
                ),
              ),
              Container(width: 1, height: 40, color: Colors.white24),
              Expanded(
                child: _buildSummaryItem(
                  'Rentabilidade',
                  '+12,5% a.a.',
                  Icons.trending_up,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Column(
        children: [
          Icon(icon, color: Colors.white, size: 24),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSection(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: const Color(0xFF16213E),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(30),
              ),
              child: Icon(icon, color: color, size: 28),
            ),
            const SizedBox(width: 20),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    description,
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: color,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  void _showComingSoon(BuildContext context, String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213E),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
        title: Row(
          children: [
            Icon(
              Icons.construction,
              color: Colors.orange,
              size: 24,
            ),
            const SizedBox(width: 12),
            const Text(
              'Em Desenvolvimento',
              style: TextStyle(color: Colors.white),
            ),
          ],
        ),
        content: Text(
          'A funcionalidade "$feature" está sendo desenvolvida e estará disponível em breve.',
          style: const TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Entendi',
              style: TextStyle(color: Color(0xFF6A1B9A)),
            ),
          ),
        ],
      ),
    );
  }
}
