﻿using Microsoft.AspNetCore.Mvc;
using ServiceAdmin.Interfaces;
using Shared.Enums;

namespace CasteloForteAdmin.Controllers.ControllerBaseComplemento
{
    /// <summary>
    /// Controlador base que fornece funcionalidades comuns para todos os controladores do sistema.
    /// </summary>
    /// <typeparam name="T">Tipo do controlador que herda esta classe base.</typeparam>
    public abstract class ControllerBaseComplemento<T>(
        ILogErroAdminService LogErrorAdminService,
        IHistoricoUsuarioService historicoUsuarioService,
        ILogger<T> logger) : ControllerBase where T : class
    {
        #region CONSTRUTOR
        private readonly ILogger<T> _logger = logger;
        private readonly ILogErroAdminService _LogErrorAdminService = LogErrorAdminService;
        private readonly IHistoricoUsuarioService _historicoUsuarioService = historicoUsuarioService;
        #endregion

        #region Loggers
        /// <summary>
        /// Registra um erro no sistema e retorna uma resposta BadRequest com a mensagem de erro.
        /// Este método não deve ser exposto como um endpoint de API.
        /// </summary>
        /// <param name="ex">Exceção que ocorreu.</param>
        /// <param name="metodo">Nome do método onde ocorreu o erro</param>
        /// <param name="controller">Nome do controller onde ocorreu o erro</param>
        /// <param name="variaveis">Variáveis relacionadas ao erro</param>
        /// <returns>BadRequestObjectResult com a mensagem de erro.</returns>
        [NonAction] // Este atributo indica que o método não é uma action de API
        public async Task<BadRequestObjectResult> LogErro(Exception ex, string metodo, string controller, string variaveis)
        {
            _logger.LogError($"[{controller}] - {metodo}: {ex.Message}. Variáveis: {variaveis}", DateTimeOffset.UtcNow);

            // Registra o erro no banco de dados Admin
            await _LogErrorAdminService.LogErro(ex, metodo, controller, variaveis);

            return BadRequest(new
            {
                error = ex.Message,
                timestamp = DateTimeOffset.UtcNow
            });
        }

        /// <summary>
        /// Registra uma ação ou alteração realizada no sistema.
        /// Este método não deve ser exposto como um endpoint de API.
        /// </summary>
        /// <param name="metodo">Nome do método</param>
        /// <param name="acao">Descrição da ação</param>
        /// <param name="dadoAntigo">Dados antes da alteração</param>
        /// <param name="dadoNovo">Dados após a alteração</param>
        [NonAction] // Este atributo indica que o método não é uma action de API
        public async Task RegistraAcao(string metodo, string acao, string dadoAntigo, string dadoNovo)
        {
            await _historicoUsuarioService.RegistrarAcao(metodo, acao, dadoAntigo, dadoNovo);
        }

        /// <summary>
        /// Registra informações no log do sistema.
        /// Este método não deve ser exposto como um endpoint de API.
        /// </summary>
        /// <param name="mensagem">Mensagem a ser registrada</param>
        /// <param name="metodo">Nome do método</param>
        /// <param name="controller">Nome do controller</param>
        [NonAction] // Este atributo indica que o método não é uma action de API
        public void LogInfo(string mensagem, string metodo, string controller)
        {
            _logger.LogInformation($"[{controller}] - {metodo}: {mensagem}", DateTimeOffset.UtcNow);
        }
        #endregion
    }
}
