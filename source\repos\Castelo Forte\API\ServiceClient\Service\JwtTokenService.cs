using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using Shared.Entities.Client;
using Shared.Entities.Admin;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace ServiceClient.Service
{
    /// <summary>
    /// Service para geração e validação de tokens JWT
    /// </summary>
    public class JwtTokenService
    {
        private readonly JwtSettings _jwtSettings;

        public JwtTokenService(IOptions<JwtSettings> jwtSettings)
        {
            _jwtSettings = jwtSettings.Value ?? throw new ArgumentNullException(nameof(jwtSettings));
        }

        /// <summary>
        /// Gera um token JWT para o usuário
        /// </summary>
        /// <param name="usuario">Dados do usuário</param>
        /// <returns>Token JWT e data de expiração</returns>
        public (string token, DateTime expiresAt) GerarToken(Usuario usuario)
        {
            if (usuario == null)
                throw new ArgumentNullException(nameof(usuario));

            if (string.IsNullOrEmpty(_jwtSettings.Secret))
                throw new InvalidOperationException("JWT Secret não configurado");

            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_jwtSettings.Secret);
            var expiresAt = DateTime.UtcNow.AddHours(_jwtSettings.ExpiracaoHoras);

            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, usuario.Id ?? string.Empty),
                new("UsuarioId", usuario.Id ?? string.Empty),
                new(ClaimTypes.Name, usuario.Nome ?? string.Empty),
                new(ClaimTypes.Email, usuario.Email ?? string.Empty),
                new("CPF", usuario.Cpf ?? string.Empty),
                new("NomeBaseDados", usuario.NomeBaseDados ?? string.Empty),
                new("ConnectionString", usuario.ConnectionString ?? string.Empty),
                new(ClaimTypes.Role, "Usuario"),
                new("TipoUsuario", "Client"),
                new("DataCadastro", usuario.DtaCadastro?.ToString("yyyy-MM-dd") ?? string.Empty),
                new("FlgAtivo", usuario.FlgAtivo.ToString()),
                new("DoisFatores", usuario.FlgDoisFatores.ToString())
            };

            // Adiciona celular se disponível
            if (!string.IsNullOrEmpty(usuario.Celular))
                claims.Add(new Claim("Celular", usuario.Celular));

            // Adiciona data de nascimento
            claims.Add(new Claim("DataNascimento", usuario.DtaNascimento.ToString("yyyy-MM-dd")));

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = expiresAt,
                Issuer = _jwtSettings.Emissor,
                Audience = _jwtSettings.Audiencia,
                SigningCredentials = new SigningCredentials(
                    new SymmetricSecurityKey(key),
                    SecurityAlgorithms.HmacSha256Signature)
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            var tokenString = tokenHandler.WriteToken(token);

            return (tokenString, expiresAt);
        }

        /// <summary>
        /// Valida um token JWT
        /// </summary>
        /// <param name="token">Token a ser validado</param>
        /// <returns>Claims do token se válido, null se inválido</returns>
        public ClaimsPrincipal? ValidarToken(string token)
        {
            if (string.IsNullOrEmpty(token))
                return null;

            if (string.IsNullOrEmpty(_jwtSettings.Secret))
                throw new InvalidOperationException("JWT Secret não configurado");

            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.ASCII.GetBytes(_jwtSettings.Secret);

                var validationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    ValidIssuer = _jwtSettings.Emissor,
                    ValidAudience = _jwtSettings.Audiencia,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ClockSkew = TimeSpan.FromMinutes(5)
                };

                var principal = tokenHandler.ValidateToken(token, validationParameters, out SecurityToken validatedToken);
                return principal;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Extrai informações do usuário de um token válido
        /// </summary>
        /// <param name="token">Token JWT válido</param>
        /// <returns>Informações do usuário</returns>
        public (string usuarioId, string nome, string email, string cpf, string nomeBaseDados, string connectionString)? ExtrairInformacoesUsuario(string token)
        {
            var principal = ValidarToken(token);
            if (principal == null)
                return null;

            var usuarioId = principal.FindFirst("UsuarioId")?.Value ?? string.Empty;
            var nome = principal.FindFirst(ClaimTypes.Name)?.Value ?? string.Empty;
            var email = principal.FindFirst(ClaimTypes.Email)?.Value ?? string.Empty;
            var cpf = principal.FindFirst("CPF")?.Value ?? string.Empty;
            var nomeBaseDados = principal.FindFirst("NomeBaseDados")?.Value ?? string.Empty;
            var connectionString = principal.FindFirst("ConnectionString")?.Value ?? string.Empty;

            return (usuarioId, nome, email, cpf, nomeBaseDados, connectionString);
        }

        /// <summary>
        /// Verifica se um token está expirado
        /// </summary>
        /// <param name="token">Token a ser verificado</param>
        /// <returns>True se expirado, false caso contrário</returns>
        public bool TokenExpirado(string token)
        {
            if (string.IsNullOrEmpty(token))
                return true;

            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var jsonToken = tokenHandler.ReadJwtToken(token);
                
                return jsonToken.ValidTo < DateTime.UtcNow;
            }
            catch
            {
                return true;
            }
        }

        /// <summary>
        /// Obtém a data de expiração de um token
        /// </summary>
        /// <param name="token">Token JWT</param>
        /// <returns>Data de expiração ou null se inválido</returns>
        public DateTime? ObterDataExpiracao(string token)
        {
            if (string.IsNullOrEmpty(token))
                return null;

            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var jsonToken = tokenHandler.ReadJwtToken(token);
                
                return jsonToken.ValidTo;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Gera um novo token baseado em um token existente (refresh)
        /// </summary>
        /// <param name="tokenAtual">Token atual válido</param>
        /// <param name="usuario">Dados atualizados do usuário</param>
        /// <returns>Novo token e data de expiração</returns>
        public (string token, DateTime expiresAt)? RefreshToken(string tokenAtual, Usuario usuario)
        {
            var principal = ValidarToken(tokenAtual);
            if (principal == null)
                return null;

            // Gera um novo token com os dados atualizados do usuário
            return GerarToken(usuario);
        }
    }
}
