using AutoMapper;
using CasteloForte.Middleware;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using MongoDB.Driver;
using RepositoryAdmin.Configuration;
using RepositoryAdmin.Injection;
using RepositoryClient.Cache;
using RepositoryClient.Cache.Interface;
using RepositoryClient.Configuration;
using RepositoryClient.Configuration.Interfaces;
using RepositoryClient.Configuration.Service;
using RepositoryClient.Injection;
using ServiceAdmin.Injection;
using ServiceClient.Injection;
using Shared.Entities.Client;
using Shared.Helpers;
using Shared.Helpers.Encripty;
using Shared.Mapping;
using Shared.Models.Configuration;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

try
{
    MongoDbClassMapConfiguration.Configure();
}
catch (Exception ex)
{
    Console.WriteLine($"Aviso: Erro ao configurar MongoDB ClassMap: {ex.Message}");
}

builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();

// Configuração CORS para permitir chamadas do Flutter
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowFlutter", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var mongoDBSettings = builder.Configuration.GetSection("MongoDBSettings").Get<MongoDBSettings>();
if (mongoDBSettings == null)
    throw new InvalidOperationException("MongoDBSettings não configurado");

// Validações específicas
if (string.IsNullOrEmpty(mongoDBSettings.ConnectionString))
    throw new InvalidOperationException("ConnectionString não configurado");
if (string.IsNullOrEmpty(mongoDBSettings.DatabaseNameAdmin))
    throw new InvalidOperationException("DatabaseNameAdmin não configurado");
if (string.IsNullOrEmpty(mongoDBSettings.DatabaseNameClient))
    throw new InvalidOperationException("DatabaseNameClient não configurado");

builder.Services.Configure<MongoDBSettings>(builder.Configuration.GetSection("MongoDBSettings"));

builder.Services.AddSingleton<IMongoClient>(serviceProvider =>
{
    var settings = MongoClientSettings.FromConnectionString(mongoDBSettings.ConnectionString);
    settings.ConnectTimeout = TimeSpan.FromSeconds(30);
    settings.SocketTimeout = TimeSpan.FromSeconds(30);
    settings.ServerSelectionTimeout = TimeSpan.FromSeconds(30);
    settings.MaxConnectionPoolSize = 50;
    settings.MinConnectionPoolSize = 5;
    settings.WaitQueueTimeout = TimeSpan.FromSeconds(5);
    settings.RetryWrites = true;
    settings.RetryReads = true;

    if (builder.Environment.IsDevelopment())
    {
        settings.ClusterConfigurator = cb =>
        {
            cb.Subscribe<MongoDB.Driver.Core.Events.CommandFailedEvent>(e =>
            {
                Console.WriteLine($"MongoDB Command Failed: {e.CommandName} - {e.Failure}");
            });
        };
    }

    return new MongoClient(settings);
});

builder.Services.AddDbContext<ContextBaseAdmin>((serviceProvider, options) =>
{
    var mongoClient = serviceProvider.GetRequiredService<IMongoClient>();
    var mongoSettings = serviceProvider.GetRequiredService<Microsoft.Extensions.Options.IOptions<MongoDBSettings>>().Value;
    options.UseMongoDB(mongoClient, mongoSettings.DatabaseNameAdmin);
});

builder.Services.AddDbContext<ContextBase>((serviceProvider, options) =>
{
    var mongoClient = serviceProvider.GetRequiredService<IMongoClient>();
    var mongoSettings = serviceProvider.GetRequiredService<Microsoft.Extensions.Options.IOptions<MongoDBSettings>>().Value;

    try
    {
        var databaseName = mongoSettings.DatabaseNameClient;
        var database = mongoClient.GetDatabase(databaseName);
        database.RunCommand((Command<MongoDB.Bson.BsonDocument>)"{ping:1}");
        options.UseMongoDB(mongoClient, databaseName);
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Aviso: Falha ao conectar com ContextBase: {ex.Message}");
        options.UseMongoDB(mongoClient, mongoSettings.DatabaseNameAdmin);
    }
});

builder.Services.Configure<JwtSettings>(builder.Configuration.GetSection("JwtSettings"));

// Registra o JwtTokenService para autenticação do Client
builder.Services.AddScoped<ServiceClient.Service.JwtTokenService>();

builder.Services.AddScoped<IEncryptionService, EncryptionService>();
builder.Services.AddScoped<IContextoMultiTenantService, ContextoMultiTenantService>();
builder.Services.AddSingleton<ITenantCache, TenantCache>();
builder.Services.AddScoped<Shared.Services.DataRecoveryService>();

builder.Services.AddHttpContextAccessor();

try
{
    builder.Services.AddInjectionRepository();
    builder.Services.AddInjectionService();
}
catch (Exception ex)
{
    Console.WriteLine($"Aviso: Serviços Client não disponíveis: {ex.Message}");
}

builder.Services.AddInjectionRepositoryAdmin();
builder.Services.AddInjectionServiceAdmin();

var jwtSettings = builder.Configuration.GetSection("JwtSettings").Get<JwtSettings>();
if (jwtSettings == null)
    throw new InvalidOperationException("JwtSettings não configurado");

var key = Encoding.ASCII.GetBytes(jwtSettings.Secret ??
    throw new InvalidOperationException("JWT Secret não configurado"));

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.SaveToken = true;
    options.RequireHttpsMetadata = false;
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = jwtSettings.Emissor,
        ValidAudience = jwtSettings.Audiencia,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ClockSkew = TimeSpan.FromMinutes(5)
    };
});

builder.Services.AddAuthorization();

builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo { Title = "Castelo Forte CLIENTE", Version = "v1" });

    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer",
        BearerFormat = "JWT"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });
});

var mappingConfig = new MapperConfiguration(mc => mc.AddProfile(new MappingProfile()));
builder.Services.AddSingleton(mappingConfig.CreateMapper());

var app = builder.Build();

try
{
    using var scope = app.Services.CreateScope();
    var mongoClient = scope.ServiceProvider.GetRequiredService<IMongoClient>();
    var mongoSettings = scope.ServiceProvider.GetRequiredService<Microsoft.Extensions.Options.IOptions<MongoDBSettings>>().Value;

    var database = mongoClient.GetDatabase(mongoSettings.DatabaseNameAdmin);
    await database.RunCommandAsync((Command<MongoDB.Bson.BsonDocument>)"{ping:1}");

    app.Logger.LogDebug("Conexão com MongoDB Admin ({Database}) estabelecida", mongoSettings.DatabaseNameAdmin);
}
catch (Exception ex)
{
    app.Logger.LogError(ex, "Falha ao conectar com MongoDB Admin");
    throw;
}

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

// Habilitar CORS
app.UseCors("AllowFlutter");

app.UseAuthentication();

// Middleware para configurar automaticamente o contexto multi-tenant após autenticação
app.UseTenantMiddleware();

app.UseAuthorization();

app.MapControllers();

app.MapGet("/health/mongodb", async (IMongoClient mongoClient, Microsoft.Extensions.Options.IOptions<MongoDBSettings> settings) =>
{
    try
    {
        var mongoSettings = settings.Value;
        if (string.IsNullOrEmpty(mongoSettings.DatabaseNameAdmin))
            throw new InvalidOperationException("DatabaseNameAdmin não configurado");

        var database = mongoClient.GetDatabase(mongoSettings.DatabaseNameAdmin);
        var startTime = DateTime.UtcNow;
        await database.RunCommandAsync((Command<MongoDB.Bson.BsonDocument>)"{ping:1}");
        var responseTime = DateTime.UtcNow - startTime;

        return Results.Ok(new
        {
            status = "healthy",
            database = settings.Value.DatabaseNameAdmin,
            responseTime = $"{responseTime.TotalMilliseconds}ms",
            timestamp = DateTime.UtcNow
        });
    }
    catch (Exception ex)
    {
        return Results.Problem(
            detail: ex.Message,
            title: "MongoDB Health Check Failed",
            statusCode: 503
        );
    }
});

app.Run();
