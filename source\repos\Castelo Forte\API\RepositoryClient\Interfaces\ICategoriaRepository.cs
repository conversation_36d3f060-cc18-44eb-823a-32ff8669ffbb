using RepositoryClient.Interfaces.Generic;
using Shared.Entities.Client;
using Shared.Enums;

namespace RepositoryClient.Interfaces
{
    /// <summary>
    /// Interface para repositório de categorias
    /// </summary>
    public interface ICategoriaRepository : IGenericClientRepository<Categoria>
    {
        /// <summary>
        /// Busca categorias por tipo
        /// </summary>
        /// <param name="tipo">Tipo da categoria</param>
        /// <returns>Lista de categorias do tipo especificado</returns>
        Task<IEnumerable<Categoria>> BuscarPorTipoAsync(TipoCategoria tipo);

        /// <summary>
        /// Busca categorias ativas
        /// </summary>
        /// <returns>Lista de categorias ativas</returns>
        Task<IEnumerable<Categoria>> BuscarAtivasAsync();

        /// <summary>
        /// Busca categorias por nome (busca parcial)
        /// </summary>
        /// <param name="nome">Nome ou parte do nome da categoria</param>
        /// <returns>Lista de categorias que contêm o nome especificado</returns>
        Task<IEnumerable<Categoria>> BuscarPorNomeAsync(string nome);



        /// <summary>
        /// Verifica se existe uma categoria com o nome especificado
        /// </summary>
        /// <param name="nome">Nome da categoria</param>
        /// <param name="idExcluir">ID da categoria a excluir da verificação (para edição)</param>
        /// <returns>True se existe, False caso contrário</returns>
        Task<bool> ExisteCategoriaPorNomeAsync(string nome, string? idExcluir = null);

        /// <summary>
        /// Inativa uma categoria (soft delete)
        /// </summary>
        /// <param name="id">ID da categoria</param>
        /// <returns>True se foi inativada com sucesso</returns>
        Task<bool> InativarAsync(string id);

        /// <summary>
        /// Reativa uma categoria
        /// </summary>
        /// <param name="id">ID da categoria</param>
        /// <returns>True se foi reativada com sucesso</returns>
        Task<bool> ReativarAsync(string id);

        /// <summary>
        /// Busca categorias com paginação
        /// </summary>
        /// <param name="pagina">Número da página (base 1)</param>
        /// <param name="tamanhoPagina">Tamanho da página</param>
        /// <param name="tipo">Filtro por tipo (opcional)</param>
        /// <param name="ativas">Filtro por ativas (opcional)</param>
        /// <returns>Lista paginada de categorias</returns>
        Task<(IEnumerable<Categoria> Categorias, int Total)> BuscarPaginadoAsync(
            int pagina = 1, 
            int tamanhoPagina = 10, 
            TipoCategoria? tipo = null, 
            bool? ativas = null);

        /// <summary>
        /// Conta o número de transações associadas a uma categoria
        /// </summary>
        /// <param name="idCategoria">ID da categoria</param>
        /// <returns>Número de transações</returns>
        Task<int> ContarTransacoesAsync(string idCategoria);

        /// <summary>
        /// Conta o número de metas associadas a uma categoria
        /// </summary>
        /// <param name="idCategoria">ID da categoria</param>
        /// <returns>Número de metas</returns>
        Task<int> ContarMetasAsync(string idCategoria);
    }
}
