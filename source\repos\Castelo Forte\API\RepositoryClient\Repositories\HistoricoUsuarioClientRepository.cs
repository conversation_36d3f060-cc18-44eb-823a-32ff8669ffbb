using MongoDB.Driver;
using Microsoft.AspNetCore.Http;
using RepositoryClient.Configuration;
using RepositoryClient.Configuration.Interfaces;
using RepositoryClient.Interfaces;
using RepositoryClient.Repositories.Generic;
using RepositoryAdmin.Interfaces;
using Shared.Entities.Client;

namespace RepositoryClient.Repositories
{
    /// <summary>
    /// Repositório para histórico de usuário no contexto Client (multi-tenant)
    /// </summary>
    public class HistoricoUsuarioClientRepository : GenericClientRepository<HistoricoUsuarioClient>, IHistoricoUsuarioClientRepository
    {
        public HistoricoUsuarioClientRepository(
            IContextoMultiTenantService contextoMultiTenant,
            IHttpContextAccessor httpContextAccessor,
            IUsuarioRepository usuarioRepository)
            : base(contextoMultiTenant, httpContextAccessor, usuarioRepository, "HistoricoUsuarioClient")
        {
        }

        /// <summary>
        /// Busca histórico por usuário
        /// </summary>
        /// <param name="idUsuario">ID do usuário</param>
        /// <returns>Lista de histórico do usuário</returns>
        public async Task<IEnumerable<HistoricoUsuarioClient>> BuscarPorUsuarioAsync(string idUsuario)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(idUsuario))
                    throw new ArgumentException("ID do usuário não pode ser nulo ou vazio", nameof(idUsuario));

                var filter = Builders<HistoricoUsuarioClient>.Filter.Eq(x => x.IdUsuario, idUsuario);
                var sort = Builders<HistoricoUsuarioClient>.Sort.Descending(x => x.DtaCadastro);

                return await _collection
                    .Find(filter)
                    .Sort(sort)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar histórico por usuário: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca histórico por período
        /// </summary>
        /// <param name="dataInicio">Data de início</param>
        /// <param name="dataFim">Data de fim</param>
        /// <returns>Lista de histórico no período</returns>
        public async Task<IEnumerable<HistoricoUsuarioClient>> BuscarPorPeriodoAsync(DateTime dataInicio, DateTime dataFim)
        {
            try
            {
                if (dataInicio > dataFim)
                    throw new ArgumentException("Data de início não pode ser maior que data de fim");

                var filter = Builders<HistoricoUsuarioClient>.Filter.And(
                    Builders<HistoricoUsuarioClient>.Filter.Gte(x => x.DtaCadastro, dataInicio),
                    Builders<HistoricoUsuarioClient>.Filter.Lte(x => x.DtaCadastro, dataFim)
                );
                var sort = Builders<HistoricoUsuarioClient>.Sort.Descending(x => x.DtaCadastro);

                return await _collection
                    .Find(filter)
                    .Sort(sort)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar histórico por período: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca histórico por tipo de operação
        /// </summary>
        /// <param name="tipoOperacao">Tipo da operação</param>
        /// <returns>Lista de histórico do tipo especificado</returns>
        public async Task<IEnumerable<HistoricoUsuarioClient>> BuscarPorTipoOperacaoAsync(string tipoOperacao)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(tipoOperacao))
                    throw new ArgumentException("Tipo de operação não pode ser nulo ou vazio", nameof(tipoOperacao));

                var filter = Builders<HistoricoUsuarioClient>.Filter.Eq(x => x.TipoOperacao, tipoOperacao);
                var sort = Builders<HistoricoUsuarioClient>.Sort.Descending(x => x.DtaCadastro);

                return await _collection
                    .Find(filter)
                    .Sort(sort)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar histórico por tipo de operação: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca histórico por controller
        /// </summary>
        /// <param name="controller">Nome do controller</param>
        /// <returns>Lista de histórico do controller</returns>
        public async Task<IEnumerable<HistoricoUsuarioClient>> BuscarPorControllerAsync(string controller)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(controller))
                    throw new ArgumentException("Controller não pode ser nulo ou vazio", nameof(controller));

                var filter = Builders<HistoricoUsuarioClient>.Filter.Eq(x => x.Controller, controller);
                var sort = Builders<HistoricoUsuarioClient>.Sort.Descending(x => x.DtaCadastro);

                return await _collection
                    .Find(filter)
                    .Sort(sort)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar histórico por controller: {ex.Message}", ex);
            }
        }
    }
}
