import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../utils/navigation_helper.dart';

/// Footer padrão para telas autenticadas com botões de navegação
class AppFooter extends StatelessWidget {
  final int currentIndex;

  const AppFooter({super.key, required this.currentIndex});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: AppTheme.navyBlueColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(50),
            blurRadius: 10,
            spreadRadius: 1,
            offset: const Offset(0, -2),
          ),
        ],
        border: Border(
          top: BorderSide(color: AppTheme.goldColor.withAlpha(100), width: 1),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          // Dashboard
          _buildNavItem(
            context,
            icon: Icons.home_outlined,
            label: 'Dashboard',
            index: 0,
            onTap: () => NavigationHelper.goToDashboard(context),
          ),

          // Lançamentos
          _buildNavItem(
            context,
            icon: Icons.list_alt_outlined,
            label: 'Lançamentos',
            index: 1,
            onTap: () => NavigationHelper.goToTransactions(context),
          ),

          // Botão de adicionar (centralizado e destacado)
          _buildAddButton(context),

          // Categorias
          _buildNavItem(
            context,
            icon: Icons.category_outlined,
            label: 'Categorias',
            index: 3,
            onTap: () => NavigationHelper.goToCategories(context),
          ),

          // Metas
          _buildNavItem(
            context,
            icon: Icons.track_changes_outlined,
            label: 'Metas',
            index: 4,
            onTap: () => NavigationHelper.goToGoals(context),
          ),
        ],
      ),
    );
  }

  /// Constrói um item de navegação
  Widget _buildNavItem(
    BuildContext context, {
    required IconData icon,
    required String label,
    required int index,
    required VoidCallback onTap,
  }) {
    final bool isSelected = index == currentIndex;

    return InkWell(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: isSelected ? AppTheme.goldColor : Colors.white70,
            size: 24,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: isSelected ? AppTheme.goldColor : Colors.white70,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  /// Constrói o botão de adicionar centralizado
  Widget _buildAddButton(BuildContext context) {
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        color: AppTheme.goldColor,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: AppTheme.goldColor.withAlpha(100),
            blurRadius: 8,
            spreadRadius: 2,
          ),
        ],
      ),
      child: IconButton(
        icon: const Icon(Icons.add, color: AppTheme.navyBlueColor, size: 32),
        onPressed: () {
          // Ação para adicionar novo lançamento
          // Pode abrir um modal ou navegar para tela de cadastro
        },
      ),
    );
  }
}
