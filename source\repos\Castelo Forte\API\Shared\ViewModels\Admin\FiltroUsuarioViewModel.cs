using System.ComponentModel.DataAnnotations;

namespace Shared.ViewModels.Admin
{
    /// <summary>
    /// ViewModel para filtrar usuários no sistema Admin
    /// </summary>
    public class FiltroUsuarioViewModel
    {
        /// <summary>
        /// Nome do usuário para busca
        /// </summary>
        public string? Nome { get; set; }

        /// <summary>
        /// Email do usuário para busca
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// Status do usuário (Ativo/Inativo)
        /// </summary>
        public bool? FlgAtivo { get; set; }

        /// <summary>
        /// Data de início para filtro por período de cadastro
        /// </summary>
        [DataType(DataType.Date)]
        public DateTime? DataCadastroInicio { get; set; }

        /// <summary>
        /// Data de fim para filtro por período de cadastro
        /// </summary>
        [DataType(DataType.Date)]
        public DateTime? DataCadastroFim { get; set; }

        /// <summary>
        /// Data de início para filtro por último acesso
        /// </summary>
        [DataType(DataType.Date)]
        public DateTime? UltimoAcessoInicio { get; set; }

        /// <summary>
        /// Data de fim para filtro por último acesso
        /// </summary>
        [DataType(DataType.Date)]
        public DateTime? UltimoAcessoFim { get; set; }

        /// <summary>
        /// Texto para busca geral (nome, email, etc.)
        /// </summary>
        public string? TextoBusca { get; set; }

        /// <summary>
        /// Número da página para paginação
        /// </summary>
        public int Pagina { get; set; } = 1;

        /// <summary>
        /// Quantidade de itens por página
        /// </summary>
        public int ItensPorPagina { get; set; } = 10;

        /// <summary>
        /// Campo para ordenação
        /// </summary>
        public string? OrdenarPor { get; set; } = "DtaCadastro";

        /// <summary>
        /// Direção da ordenação (ASC ou DESC)
        /// </summary>
        public string? DirecaoOrdenacao { get; set; } = "DESC";
    }
}
