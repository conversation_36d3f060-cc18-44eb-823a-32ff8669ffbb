﻿using Shared.ViewModels.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shared.ViewModels.Admin
{
    public class UsuarioViewModel : BaseViewModel
    {
        #region Dados usuario
        public string Nome { get; set; } = "";
        public string Email { get; set; } = "";
        public string Cpf { get; set; } = "";
        public string Celular { get; set; } = "";
        public DateTime DtaNascimento { get; set; }
        #endregion

        #region TermosECondicoes
        public bool FlgTermosECondicoes { get; set; }
        public DateTime? DtaTermosECondicoes { get; set; }
        #endregion
        public DateTime? DtaUltimoAcesso { get; set; }

        #region Token
        public string TokenAcesso { get; set; } = "";
        public DateTime? DtaTokenAcessoGerado { get; set; }
        #endregion

        public string Senha { get; set; } = "";
        public bool FlgDoisFatores { get; set; } = false;

        /// <summary>
        /// Flag que indica se o usuário tem privilégios de administrador do sistema
        /// </summary>
        public bool FlgAdministrador { get; set; } = false;

        #region BaseDeDados
        public string ConnectionString { get; set; } = "";
        public string NomeBaseDados { get; set; } = "";
        #endregion
        public string IdPerfilFinanceiro { get; set; } = "";

        #region PorcentagemPerfil
        public int CoracaoInquieto { get; set; } = 0;
        public int ConstrutorAnalitico { get; set; } = 0;
        public int VisionarioOusado { get; set; } = 0;
        public int ExploradorGeneroso { get; set; } = 0;
        public int EstrategistaConsciente { get; set; } = 0;
        #endregion
    }
}
