/// Modelo para filtros de metas baseado no MetaFilterViewModel da API
class MetaFilterModel {
  final List<String>? status;
  final DateTime? dataInicioInicio;
  final DateTime? dataInicioFim;
  final DateTime? dataVencimentoInicio;
  final DateTime? dataVencimentoFim;
  final List<String>? categoriasIds;
  final bool? apenasAtivas;
  final bool? apenasAbertas;
  final bool? apenasMensais;
  final double? valorMinimoAlvo;
  final double? valorMaximoAlvo;
  final double? progressoMinimo;
  final double? progressoMaximo;
  final String? textoBusca;
  final MetaOrdenacao? ordenacao;
  final bool? ordenacaoDecrescente;

  const MetaFilterModel({
    this.status,
    this.dataInicioInicio,
    this.dataInicioFim,
    this.dataVencimentoInicio,
    this.dataVencimentoFim,
    this.categoriasIds,
    this.apenasAtivas,
    this.apenasAbertas,
    this.apenasMensais,
    this.valorMinimoAlvo,
    this.valorMaximoAlvo,
    this.progressoMinimo,
    this.progressoMaximo,
    this.textoBusca,
    this.ordenacao,
    this.ordenacaoDecrescente,
  });

  /// Converte para JSON para envio à API
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};

    if (status != null && status!.isNotEmpty) {
      json['Status'] = status;
    }
    if (dataInicioInicio != null) {
      json['DataInicioInicio'] = dataInicioInicio!.toIso8601String();
    }
    if (dataInicioFim != null) {
      json['DataInicioFim'] = dataInicioFim!.toIso8601String();
    }
    if (dataVencimentoInicio != null) {
      json['DataVencimentoInicio'] = dataVencimentoInicio!.toIso8601String();
    }
    if (dataVencimentoFim != null) {
      json['DataVencimentoFim'] = dataVencimentoFim!.toIso8601String();
    }
    if (categoriasIds != null && categoriasIds!.isNotEmpty) {
      json['CategoriasIds'] = categoriasIds;
    }
    if (apenasAtivas != null) {
      json['ApenasAtivas'] = apenasAtivas;
    }
    if (apenasMensais != null) {
      json['ApenasMensais'] = apenasMensais;
    }
    if (valorMinimoAlvo != null) {
      json['ValorMinimoAlvo'] = valorMinimoAlvo;
    }
    if (valorMaximoAlvo != null) {
      json['ValorMaximoAlvo'] = valorMaximoAlvo;
    }
    if (progressoMinimo != null) {
      json['ProgressoMinimo'] = progressoMinimo;
    }
    if (progressoMaximo != null) {
      json['ProgressoMaximo'] = progressoMaximo;
    }
    if (textoBusca != null && textoBusca!.isNotEmpty) {
      json['TextoBusca'] = textoBusca;
    }
    if (ordenacao != null) {
      json['Ordenacao'] = ordenacao!.index;
    }
    if (ordenacaoDecrescente != null) {
      json['OrdenacaoDecrescente'] = ordenacaoDecrescente;
    }

    return json;
  }

  /// Converte para query parameters para requisições GET
  Map<String, String> toQueryParams() {
    final params = <String, String>{};

    if (status != null && status!.isNotEmpty) {
      for (int i = 0; i < status!.length; i++) {
        params['Status[$i]'] = status![i];
      }
    }
    if (dataInicioInicio != null) {
      params['DataInicioInicio'] = dataInicioInicio!.toIso8601String();
    }
    if (dataInicioFim != null) {
      params['DataInicioFim'] = dataInicioFim!.toIso8601String();
    }
    if (dataVencimentoInicio != null) {
      params['DataVencimentoInicio'] = dataVencimentoInicio!.toIso8601String();
    }
    if (dataVencimentoFim != null) {
      params['DataVencimentoFim'] = dataVencimentoFim!.toIso8601String();
    }
    if (categoriasIds != null && categoriasIds!.isNotEmpty) {
      for (int i = 0; i < categoriasIds!.length; i++) {
        params['CategoriasIds[$i]'] = categoriasIds![i];
      }
    }
    if (apenasAtivas != null) {
      params['ApenasAtivas'] = apenasAtivas.toString();
    }
    if (apenasAbertas != null) {
      params['ApenasAbertas'] = apenasAbertas.toString();
    }
    if (apenasMensais != null) {
      params['ApenasMensais'] = apenasMensais.toString();
    }
    if (valorMinimoAlvo != null) {
      params['ValorMinimoAlvo'] = valorMinimoAlvo.toString();
    }
    if (valorMaximoAlvo != null) {
      params['ValorMaximoAlvo'] = valorMaximoAlvo.toString();
    }
    if (progressoMinimo != null) {
      params['ProgressoMinimo'] = progressoMinimo.toString();
    }
    if (progressoMaximo != null) {
      params['ProgressoMaximo'] = progressoMaximo.toString();
    }
    if (textoBusca != null && textoBusca!.isNotEmpty) {
      params['TextoBusca'] = textoBusca!;
    }
    if (ordenacao != null) {
      params['Ordenacao'] = ordenacao!.index.toString();
    }
    if (ordenacaoDecrescente != null) {
      params['OrdenacaoDecrescente'] = ordenacaoDecrescente.toString();
    }

    return params;
  }

  /// Cria uma cópia com campos modificados
  MetaFilterModel copyWith({
    List<String>? status,
    DateTime? dataInicioInicio,
    DateTime? dataInicioFim,
    DateTime? dataVencimentoInicio,
    DateTime? dataVencimentoFim,
    List<String>? categoriasIds,
    bool? apenasAtivas,
    bool? apenasAbertas,
    bool? apenasMensais,
    double? valorMinimoAlvo,
    double? valorMaximoAlvo,
    double? progressoMinimo,
    double? progressoMaximo,
    String? textoBusca,
    MetaOrdenacao? ordenacao,
    bool? ordenacaoDecrescente,
  }) {
    return MetaFilterModel(
      status: status ?? this.status,
      dataInicioInicio: dataInicioInicio ?? this.dataInicioInicio,
      dataInicioFim: dataInicioFim ?? this.dataInicioFim,
      dataVencimentoInicio: dataVencimentoInicio ?? this.dataVencimentoInicio,
      dataVencimentoFim: dataVencimentoFim ?? this.dataVencimentoFim,
      categoriasIds: categoriasIds ?? this.categoriasIds,
      apenasAtivas: apenasAtivas ?? this.apenasAtivas,
      apenasAbertas: apenasAbertas ?? this.apenasAbertas,
      apenasMensais: apenasMensais ?? this.apenasMensais,
      valorMinimoAlvo: valorMinimoAlvo ?? this.valorMinimoAlvo,
      valorMaximoAlvo: valorMaximoAlvo ?? this.valorMaximoAlvo,
      progressoMinimo: progressoMinimo ?? this.progressoMinimo,
      progressoMaximo: progressoMaximo ?? this.progressoMaximo,
      textoBusca: textoBusca ?? this.textoBusca,
      ordenacao: ordenacao ?? this.ordenacao,
      ordenacaoDecrescente: ordenacaoDecrescente ?? this.ordenacaoDecrescente,
    );
  }
}

/// Enum para ordenação de metas
enum MetaOrdenacao {
  nome,
  dataCriacao,
  dataVencimento,
  valorAlvo,
  progressoAtual,
  percentualProgresso,
  status,
}

/// Extensão para obter descrições das ordenações
extension MetaOrdenacaoExtension on MetaOrdenacao {
  String get description {
    switch (this) {
      case MetaOrdenacao.nome:
        return 'Nome';
      case MetaOrdenacao.dataCriacao:
        return 'Data de Criação';
      case MetaOrdenacao.dataVencimento:
        return 'Data de Vencimento';
      case MetaOrdenacao.valorAlvo:
        return 'Valor Alvo';
      case MetaOrdenacao.progressoAtual:
        return 'Progresso Atual';
      case MetaOrdenacao.percentualProgresso:
        return 'Percentual de Progresso';
      case MetaOrdenacao.status:
        return 'Status';
    }
  }
}
