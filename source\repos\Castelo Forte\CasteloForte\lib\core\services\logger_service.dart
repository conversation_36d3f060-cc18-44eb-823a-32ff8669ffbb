import 'dart:developer' as developer;

/// Serviço de logging para substituir print statements
class LoggerService {
  static const String _appName = 'CasteloForte';

  /// Log de informação
  static void info(String message, {String? tag}) {
    developer.log(
      message,
      name: _appName,
      level: 800, // INFO level
      time: DateTime.now(),
    );
  }

  /// Log de debug
  static void debug(String message, {String? tag}) {
    developer.log(
      message,
      name: _appName,
      level: 700, // DEBUG level
      time: DateTime.now(),
    );
  }

  /// Log de warning
  static void warning(String message, {String? tag}) {
    developer.log(
      message,
      name: _appName,
      level: 900, // WARNING level
      time: DateTime.now(),
    );
  }

  /// Log de erro
  static void error(String message, {String? tag, Object? error}) {
    developer.log(
      message,
      name: _appName,
      level: 1000, // ERROR level
      time: DateTime.now(),
      error: error,
    );
  }

  /// Log específico para API
  static void api(String message, {String? endpoint}) {
    developer.log(
      '🌐 API: $message',
      name: '$_appName.API',
      level: 800,
      time: DateTime.now(),
    );
  }

  /// Log específico para autenticação
  static void auth(String message) {
    developer.log(
      '🔐 AUTH: $message',
      name: '$_appName.Auth',
      level: 800,
      time: DateTime.now(),
    );
  }

  /// Log específico para conectividade
  static void connectivity(String message) {
    developer.log(
      '🔗 CONNECTIVITY: $message',
      name: '$_appName.Network',
      level: 800,
      time: DateTime.now(),
    );
  }

  /// Log de sucesso
  static void success(String message) {
    developer.log(
      '✅ SUCCESS: $message',
      name: _appName,
      level: 800,
      time: DateTime.now(),
    );
  }

  /// Log de falha
  static void failure(String message) {
    developer.log(
      '❌ FAILURE: $message',
      name: _appName,
      level: 900,
      time: DateTime.now(),
    );
  }
}
