using AutoMapper;
using Microsoft.AspNetCore.Http;
using RepositoryClient.Interfaces;
using ServiceClient.Interfaces;
using ServiceClient.Repository.Generic;
using Shared.Entities.Client;
using Shared.ViewModels.Client;
using System.Linq.Expressions;

namespace ServiceClient.Service
{
    /// <summary>
    /// Service específico para operações com Cartão, incluindo soft delete e filtros para registros ativos
    /// </summary>
    public class CartaoService : GenericClientService<CartaoViewModel, Cartao>, ICartaoService
    {
        private readonly ICartaoRepository _cartaoRepository;

        public CartaoService(
            ICartaoRepository cartaoRepository,
            IHttpContextAccessor httpContextAccessor,
            IMapper mapper) : base(cartaoRepository, httpContextAccessor, mapper)
        {
            _cartaoRepository = cartaoRepository ?? throw new ArgumentNullException(nameof(cartaoRepository));
        }

        #region MÉTODOS QUE OCULTAM OS MÉTODOS BASE PARA FILTRAR APENAS ATIVOS

        /// <summary>
        /// Oculta o método base para retornar apenas cartões ativos por padrão
        /// </summary>
        public new async Task<IEnumerable<CartaoViewModel?>> BuscarTodosAsync()
        {
            return await BuscarTodosAtivosAsync();
        }

        /// <summary>
        /// Oculta o método base para incluir filtro de ativos automaticamente
        /// </summary>
        public new async Task<IEnumerable<CartaoViewModel?>> BuscarPorFiltroAsync(Expression<Func<Cartao?, bool>> expression)
        {
            return await BuscarPorFiltroAtivosAsync(expression);
        }

        /// <summary>
        /// Oculta o método base para incluir filtro de ativos automaticamente com paginação
        /// </summary>
        public new async Task<IEnumerable<CartaoViewModel?>> BuscarPorFiltroPaginadoAsync(Expression<Func<Cartao?, bool>> expression, int page, int pageSize)
        {
            return await BuscarPorFiltroAtivosPaginadoAsync(expression, page, pageSize);
        }

        /// <summary>
        /// Oculta o método base para contar apenas cartões ativos
        /// </summary>
        public new async Task<int> BuscarContagemTotalAsync()
        {
            return await BuscarContagemTotalAtivosAsync();
        }

        /// <summary>
        /// Oculta o método base para contar apenas cartões ativos com filtro
        /// </summary>
        public new async Task<int> BuscarContagemTotalPorFiltroAsync(Expression<Func<Cartao?, bool>> expression)
        {
            return await BuscarContagemTotalPorFiltroAtivosAsync(expression);
        }

        #endregion

        #region MÉTODOS ESPECÍFICOS PARA REGISTROS ATIVOS

        /// <summary>
        /// Busca todos os cartões ativos (FlgAtivo = true)
        /// </summary>
        public async Task<IEnumerable<CartaoViewModel?>> BuscarTodosAtivosAsync()
        {
            try
            {
                var cartoesAtivos = await _cartaoRepository.BuscarPorFiltroAsync(c => c.FlgAtivo == true);
                return cartoesAtivos.Select(c => ConverteEntidadeParaViewModel(c)).ToList();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar cartões ativos: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca cartões ativos com filtro personalizado
        /// </summary>
        public async Task<IEnumerable<CartaoViewModel?>> BuscarPorFiltroAtivosAsync(Expression<Func<Cartao?, bool>> expression)
        {
            try
            {
                if (expression == null)
                    throw new ArgumentNullException(nameof(expression), "Expressão de filtro não pode ser nula");

                // Combina o filtro fornecido com o filtro de ativos
                var filtroComAtivos = CombinarFiltroComAtivos(expression);
                var cartoesFiltrados = await _cartaoRepository.BuscarPorFiltroAsync(filtroComAtivos);
                return cartoesFiltrados.Select(c => ConverteEntidadeParaViewModel(c)).ToList();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar cartões ativos por filtro: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca cartões ativos com filtro personalizado e paginação
        /// </summary>
        public async Task<IEnumerable<CartaoViewModel?>> BuscarPorFiltroAtivosPaginadoAsync(Expression<Func<Cartao?, bool>> expression, int page, int pageSize)
        {
            try
            {
                if (expression == null)
                    throw new ArgumentNullException(nameof(expression), "Expressão de filtro não pode ser nula");

                if (page <= 0)
                    throw new ArgumentException("Página deve ser maior que zero", nameof(page));

                if (pageSize <= 0)
                    throw new ArgumentException("Tamanho da página deve ser maior que zero", nameof(pageSize));

                // Combina o filtro fornecido com o filtro de ativos
                var filtroComAtivos = CombinarFiltroComAtivos(expression);
                var cartoesPaginados = await _cartaoRepository.BuscarPorFiltroPaginadoAsync(filtroComAtivos, page, pageSize);
                return cartoesPaginados.Select(c => ConverteEntidadeParaViewModel(c)).ToList();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar cartões ativos paginados: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca contagem total de cartões ativos
        /// </summary>
        public async Task<int> BuscarContagemTotalAtivosAsync()
        {
            try
            {
                return await _cartaoRepository.BuscarContagemTotalPorFiltroAsync(c => c.FlgAtivo == true);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar contagem de cartões ativos: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca contagem de cartões ativos por filtro
        /// </summary>
        public async Task<int> BuscarContagemTotalPorFiltroAtivosAsync(Expression<Func<Cartao?, bool>> expression)
        {
            try
            {
                if (expression == null)
                    throw new ArgumentNullException(nameof(expression), "Expressão de filtro não pode ser nula");

                // Combina o filtro fornecido com o filtro de ativos
                var filtroComAtivos = CombinarFiltroComAtivos(expression);
                return await _cartaoRepository.BuscarContagemTotalPorFiltroAsync(filtroComAtivos);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar contagem de cartões ativos por filtro: {ex.Message}", ex);
            }
        }

        #endregion

        #region MÉTODOS DE SOFT DELETE

        /// <summary>
        /// Inativa um cartão (soft delete) definindo FlgAtivo = false
        /// </summary>
        public async Task<bool> InativarAsync(string id)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(id))
                    throw new ArgumentException("ID não pode ser nulo ou vazio", nameof(id));

                var cartao = await _cartaoRepository.BuscarPorIdAsync(id);
                if (cartao == null)
                    throw new KeyNotFoundException($"Cartão com ID '{id}' não encontrado");

                if (!cartao.FlgAtivo)
                    throw new InvalidOperationException("Cartão já está inativo");

                cartao.FlgAtivo = false;
                var cartaoAtualizado = await _cartaoRepository.EditarAsync(cartao);
                return cartaoAtualizado != null;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao inativar cartão: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Reativa um cartão definindo FlgAtivo = true
        /// </summary>
        public async Task<bool> ReativarAsync(string id)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(id))
                    throw new ArgumentException("ID não pode ser nulo ou vazio", nameof(id));

                var cartao = await _cartaoRepository.BuscarPorIdAsync(id);
                if (cartao == null)
                    throw new KeyNotFoundException($"Cartão com ID '{id}' não encontrado");

                if (cartao.FlgAtivo)
                    throw new InvalidOperationException("Cartão já está ativo");

                cartao.FlgAtivo = true;
                var cartaoAtualizado = await _cartaoRepository.EditarAsync(cartao);
                return cartaoAtualizado != null;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao reativar cartão: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Oculta o método de exclusão para fazer soft delete
        /// </summary>
        public new async Task ExcluirAsync(CartaoViewModel objViewModel)
        {
            try
            {
                if (objViewModel?.Id == null)
                    throw new ArgumentException("ViewModel ou ID não pode ser nulo", nameof(objViewModel));

                await InativarAsync(objViewModel.Id);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao excluir cartão: {ex.Message}", ex);
            }
        }

        #endregion

        #region MÉTODOS AUXILIARES

        /// <summary>
        /// Combina uma expressão de filtro com o filtro de registros ativos
        /// </summary>
        private static Expression<Func<Cartao?, bool>> CombinarFiltroComAtivos(Expression<Func<Cartao?, bool>> filtroOriginal)
        {
            // Filtro para registros ativos
            Expression<Func<Cartao?, bool>> filtroAtivos = c => c.FlgAtivo == true;

            // Combina os dois filtros usando AND
            var parametro = Expression.Parameter(typeof(Cartao), "c");
            var corpoFiltroAtivos = Expression.Invoke(filtroAtivos, parametro);
            var corpoFiltroOriginal = Expression.Invoke(filtroOriginal, parametro);
            var corpoCombinadoAnd = Expression.AndAlso(corpoFiltroAtivos, corpoFiltroOriginal);

            return Expression.Lambda<Func<Cartao?, bool>>(corpoCombinadoAnd, parametro);
        }

        #endregion
    }
}
