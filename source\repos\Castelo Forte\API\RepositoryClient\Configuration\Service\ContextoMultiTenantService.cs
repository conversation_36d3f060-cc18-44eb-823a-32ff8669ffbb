﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using MongoDB.Driver;
using RepositoryAdmin.Configuration;
using RepositoryAdmin.Interfaces;
using RepositoryClient.Cache.Interface;
using RepositoryClient.Configuration.Interfaces;
using Shared.Helpers.Encripty;
using Shared.Models.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace RepositoryClient.Configuration.Service
{
    public class ContextoMultiTenantService(
        IHttpContextAccessor httpContextAccessor,
        IEncryptionService encryptionService,
        IOptions<MongoDBSettings> mongoSettings,
        IUsuarioRepository usuarioRepository,
        ContextBaseAdmin adminContext,
        ITenantCache tenantCache) : IContextoMultiTenantService
    {
        #region CONSTRUTOR
        private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;
        private readonly IEncryptionService _encryptionService = encryptionService;
        private readonly IMongoDatabase _adminDatabase = adminContext.Database;
        private readonly ITenantCache _tenantCache = tenantCache;
        private readonly IUsuarioRepository _usuarioRepository = usuarioRepository;

        private static string? _currentIdTenant;
        private static string? _currentConnectionString;
        private static string? _currentDatabaseName;
        #endregion

        #region AUXILIARES
        public string? BuscarTenantAtualId()
        {
            if (_httpContextAccessor.HttpContext?.User?.Identity?.IsAuthenticated == true)
            {
                // Tenta primeiro com "UsuarioId" (padrão do JWT)
                var userIdClaim = _httpContextAccessor.HttpContext.User.FindFirst("UsuarioId");
                if (userIdClaim != null)
                {
                    return userIdClaim.Value;
                }

                // Fallback para "IdUsuario" (compatibilidade)
                userIdClaim = _httpContextAccessor.HttpContext.User.FindFirst("IdUsuario");
                if (userIdClaim != null)
                {
                    return userIdClaim.Value;
                }
            }

            var contextItem = _httpContextAccessor.HttpContext?.Items["IdTenant"]?.ToString();
            if (!string.IsNullOrEmpty(contextItem))
                return contextItem;

            return _currentIdTenant;
        }

        public void ConfigurarTenant(string UsuarioId, string connectionString, string nomeBaseDados)
        {
            if (_httpContextAccessor.HttpContext != null)
            {
                _httpContextAccessor.HttpContext.Items["IdTenant"] = UsuarioId;
                _httpContextAccessor.HttpContext.Items["ConnectionStringTenant"] = connectionString;
                _httpContextAccessor.HttpContext.Items["NomeBaseDadosTenant"] = nomeBaseDados;
            }

            _currentIdTenant = UsuarioId;
            _currentConnectionString = connectionString;
            _currentDatabaseName = nomeBaseDados;

            _tenantCache.SetTenant(UsuarioId, connectionString, nomeBaseDados);
        }

        public async Task<IMongoDatabase> ObterBaseDadosUsuarioAsync(string UsuarioId)
        {
            var Usuario = await _usuarioRepository.BuscarPorIdAsync(UsuarioId);
            if (Usuario == null)
                throw new ApplicationException($"Usuario com ID {UsuarioId} não encontrado");

            if (string.IsNullOrEmpty(Usuario.ConnectionString))
                throw new ApplicationException("Connection string do usuario não configurada");

            try
            {
                var connectionString = _encryptionService.Decrypt(Usuario.ConnectionString);
                var mongoClient = new MongoClient(connectionString);
                return mongoClient.GetDatabase(Usuario.NomeBaseDados);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao obter base de dados: {ex.Message}", ex);
            }
        }

        public async Task<IMongoDatabase> ObterDatabaseUsuarioAsync()
        {
            await ValidarContextoUsuario();

            var UsuarioId = ObterIdUsuarioAtual();
            var tenantInfo = _tenantCache.GetTenant(UsuarioId!);

            if (tenantInfo?.MongoClient != null)
                return tenantInfo.MongoClient.GetDatabase(tenantInfo.NomeBaseDados);

            throw new ApplicationException("Contexto da Usuario não configurado");
        }

        public async Task<string> ObterConnectionStringUsuarioAsync()
        {
            await ValidarContextoUsuario();

            var UsuarioId = ObterIdUsuarioAtual();
            var tenantInfo = _tenantCache.GetTenant(UsuarioId!);

            if (!string.IsNullOrEmpty(tenantInfo?.ConnectionString))
                return tenantInfo.ConnectionString;

            if (!string.IsNullOrEmpty(_currentConnectionString))
                return _currentConnectionString;

            throw new ApplicationException("Connection string não encontrada");
        }

        public IMongoDatabase ObterBaseDadosAdmin()
        {
            return _adminDatabase;
        }

        public string? ObterConnectionStringTenantAtual()
        {
            var contextString = _httpContextAccessor.HttpContext?.Items["ConnectionStringTenant"]?.ToString();
            if (!string.IsNullOrEmpty(contextString))
                return contextString;

            return _currentConnectionString;
        }

        public string? ObterNomeBaseDadosTenantAtual()
        {
            var contextName = _httpContextAccessor.HttpContext?.Items["NomeBaseDadosTenant"]?.ToString();
            if (!string.IsNullOrEmpty(contextName))
                return contextName;

            return _currentDatabaseName;
        }

        public string? ObterIdUsuarioAtual()
        {
            var UsuarioIdFromToken = _httpContextAccessor.HttpContext?.User?.FindFirst("UsuarioId")?.Value;
            if (!string.IsNullOrEmpty(UsuarioIdFromToken))
                return UsuarioIdFromToken;

            var contextId = _httpContextAccessor.HttpContext?.Items["IdTenant"]?.ToString();
            if (!string.IsNullOrEmpty(contextId))
                return contextId;

            return _currentIdTenant;
        }

        public bool TemContextoUsuario()
        {
            var UsuarioId = ObterIdUsuarioAtual();
            if (string.IsNullOrEmpty(UsuarioId))
                return false;

            return _tenantCache.HasTenant(UsuarioId) || !string.IsNullOrEmpty(_currentIdTenant);
        }

        public void LimparContexto()
        {
            if (_httpContextAccessor.HttpContext != null)
            {
                _httpContextAccessor.HttpContext.Items.Remove("IdTenant");
                _httpContextAccessor.HttpContext.Items.Remove("ConnectionStringTenant");
                _httpContextAccessor.HttpContext.Items.Remove("NomeBaseDadosTenant");
            }

            _currentIdTenant = null;
            _currentConnectionString = null;
            _currentDatabaseName = null;
        }

        private async Task ValidarContextoUsuario()
        {
            var UsuarioId = ObterIdUsuarioAtual();

            if (string.IsNullOrEmpty(UsuarioId))
            {
                // Log detalhado para debug
                var isAuthenticated = _httpContextAccessor.HttpContext?.User?.Identity?.IsAuthenticated;
                var claims = _httpContextAccessor.HttpContext?.User?.Claims?.Select(c => $"{c.Type}={c.Value}").ToList();
                var claimsString = claims != null ? string.Join(", ", claims) : "Nenhum claim encontrado";

                throw new UnauthorizedAccessException($"Contexto de Usuario não encontrado. IsAuthenticated: {isAuthenticated}, Claims: {claimsString}");
            }

            if (!_tenantCache.HasTenant(UsuarioId) && string.IsNullOrEmpty(_currentIdTenant))
            {
                await ConfigurarContextoUsuarioAsync(UsuarioId);
            }
        }

        public async Task ConfigurarContextoUsuarioAsync(string UsuarioId)
        {
            var Usuario = await _usuarioRepository.BuscarPorIdAsync(UsuarioId);
            if (Usuario == null)
                throw new ApplicationException($"Usuario com ID {UsuarioId} não encontrada");

            try
            {
                var connectionString = _encryptionService.Decrypt(Usuario.ConnectionString);

                ConfigurarTenant(UsuarioId, connectionString, Usuario.NomeBaseDados);

                await TestarConexaoUsuario(UsuarioId);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao configurar contexto do usuário: {ex.Message}", ex);
            }
        }

        private async Task TestarConexaoUsuario(string UsuarioId)
        {
            var tenantInfo = _tenantCache.GetTenant(UsuarioId);

            if (tenantInfo?.MongoClient == null && !string.IsNullOrEmpty(_currentConnectionString))
            {
                try
                {
                    var mongoClient = new MongoClient(_currentConnectionString);
                    var database = mongoClient.GetDatabase(_currentDatabaseName);
                    await database.RunCommandAsync<MongoDB.Bson.BsonDocument>(
                        new MongoDB.Bson.BsonDocument("ping", 1));
                    return;
                }
                catch (Exception ex)
                {
                    throw new ApplicationException($"Falha ao conectar com a base da Usuario (estático): {ex.Message}", ex);
                }
            }

            if (tenantInfo?.MongoClient == null || string.IsNullOrEmpty(tenantInfo.NomeBaseDados))
                throw new ApplicationException("Contexto da Usuario não configurado");

            try
            {
                var database = tenantInfo.MongoClient.GetDatabase(tenantInfo.NomeBaseDados);
                await database.RunCommandAsync<MongoDB.Bson.BsonDocument>(
                    new MongoDB.Bson.BsonDocument("ping", 1));
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Falha ao conectar com a base da Usuario: {ex.Message}", ex);
            }
        }

        #endregion
    }
}
