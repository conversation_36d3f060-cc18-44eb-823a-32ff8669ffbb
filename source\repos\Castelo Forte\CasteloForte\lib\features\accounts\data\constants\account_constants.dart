import 'package:flutter/material.dart';

/// Constantes para ícones e cores de contas
class AccountConstants {
  /// Ícones disponíveis para contas
  static const Map<String, IconData> availableIcons = {
    'account_balance': Icons.account_balance,
    'credit_card': Icons.credit_card,
    'savings': Icons.savings,
    'attach_money': Icons.attach_money,
    'account_balance_wallet': Icons.account_balance_wallet,
    'payment': Icons.payment,
    'monetization_on': Icons.monetization_on,
    'local_atm': Icons.local_atm,
    'business': Icons.business,
    'store': Icons.store,
    'home': Icons.home,
    'work': Icons.work,
    'school': Icons.school,
    'shopping_cart': Icons.shopping_cart,
    'car_rental': Icons.car_rental,
    'flight': Icons.flight,
    'restaurant': Icons.restaurant,
    'medical_services': Icons.medical_services,
    'fitness_center': Icons.fitness_center,
    'pets': Icons.pets,
  };

  /// Cores disponíveis para contas
  static const Map<String, Color> availableColors = {
    'blue': Color(0xFF2196F3),
    'green': Color(0xFF4CAF50),
    'purple': Color(0xFF9C27B0),
    'orange': Color(0xFFFF9800),
    'red': Color(0xFFF44336),
    'teal': Color(0xFF009688),
    'indigo': Color(0xFF3F51B5),
    'pink': Color(0xFFE91E63),
    'amber': Color(0xFFFFC107),
    'deep_orange': Color(0xFFFF5722),
    'light_blue': Color(0xFF03A9F4),
    'lime': Color(0xFFCDDC39),
    'cyan': Color(0xFF00BCD4),
    'brown': Color(0xFF795548),
    'blue_grey': Color(0xFF607D8B),
    'deep_purple': Color(0xFF673AB7),
  };

  /// Retorna o ícone padrão
  static IconData getDefaultIcon() {
    return Icons.account_balance;
  }

  /// Retorna a cor padrão
  static Color getDefaultColor() {
    return const Color(0xFF4ECDC4); // Cor tema do app
  }

  /// Retorna o ícone pelo nome
  static IconData? getIconByName(String? iconName) {
    if (iconName == null) return getDefaultIcon();
    return availableIcons[iconName] ?? getDefaultIcon();
  }

  /// Retorna a cor pelo nome
  static Color? getColorByName(String? colorName) {
    if (colorName == null) return getDefaultColor();
    return availableColors[colorName] ?? getDefaultColor();
  }

  /// Retorna o nome do ícone pelo IconData
  static String? getIconName(IconData icon) {
    for (final entry in availableIcons.entries) {
      if (entry.value == icon) {
        return entry.key;
      }
    }
    return null;
  }

  /// Retorna o nome da cor pelo Color
  static String? getColorName(Color color) {
    for (final entry in availableColors.entries) {
      if (entry.value == color) {
        return entry.key;
      }
    }
    return null;
  }

  /// Lista de nomes de ícones para exibição
  static List<String> get iconNames => availableIcons.keys.toList();

  /// Lista de nomes de cores para exibição
  static List<String> get colorNames => availableColors.keys.toList();

  /// Lista de ícones para exibição
  static List<IconData> get icons => availableIcons.values.toList();

  /// Lista de cores para exibição
  static List<Color> get colors => availableColors.values.toList();
}
