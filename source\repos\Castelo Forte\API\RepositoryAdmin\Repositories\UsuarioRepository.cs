﻿using MongoDB.Driver;
using RepositoryAdmin.Configuration;
using RepositoryAdmin.Interfaces;
using RepositoryAdmin.Repositories.Generic;
using Shared.Entities.Admin;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepositoryAdmin.Repositories
{
    public class UsuarioRepository(ContextBaseAdmin context) : GenericAdminRepository<Usuario>(context, context.UsuarioCollection), IUsuarioRepository
    {
        public async Task<Usuario?> BuscarUsuarioPorEmailAsync(string email)
        {
            var result = await BuscarPorFiltroAsync(u => u.Email == email);
            return result.FirstOrDefault(); 
        }

        public async Task<Usuario?> BuscarUsuarioPorCpfAsync(string cpf)
        {
            var result = await BuscarPorFiltroAsync(u => u.Cpf == cpf);
            return result.FirstOrDefault();
        }

        public async Task<Usuario?> BuscarUsuarioPorTokenAcessoAsync(string token)
        {
            var result = await BuscarPorFiltroAsync(u => u.TokenAcesso == token);
            return result.FirstOrDefault();
        }

        public async Task<bool> AtualizarTokenAcessoAsync(string usuarioId, string newToken, DateTime geradoEm)
        {
            var filter = Builders<Usuario>.Filter.Eq("_id", usuarioId);
            var update = Builders<Usuario>.Update
                .Set(u => u.TokenAcesso, newToken)
                .Set(u => u.DtaTokenAcessoGerado, geradoEm);
            var result = await _collection.UpdateOneAsync(filter, update);
            return result.ModifiedCount > 0;
        }

        public async Task<bool> AtualizarUltimoAcessoAsync(string usuarioId, DateTime ultimoAcesso)
        {
            var filter = Builders<Usuario>.Filter.Eq("_id", usuarioId);
            var update = Builders<Usuario>.Update.Set(u => u.DtaUltimoAcesso, ultimoAcesso);
            var result = await _collection.UpdateOneAsync(filter, update);
            return result.ModifiedCount > 0;
        }

        public async Task<bool> AtualizarSenhaAsync(string usuarioId, string novaSenha)
        {
            var filter = Builders<Usuario>.Filter.Eq("_id", usuarioId);
            var update = Builders<Usuario>.Update.Set(u => u.Senha, novaSenha);
            var result = await _collection.UpdateOneAsync(filter, update);
            return result.ModifiedCount > 0;
        }

        public async Task<bool> AtualizarFlgDoisFatoresAsync(string usuarioId, bool flgDoisFatores)
        {
            var filter = Builders<Usuario>.Filter.Eq("_id", usuarioId);
            var update = Builders<Usuario>.Update.Set(u => u.FlgDoisFatores, flgDoisFatores);
            var result = await _collection.UpdateOneAsync(filter, update);
            return result.ModifiedCount > 0;
        }

        public async Task<bool> AtualizarTermosECondicoesAsync(string usuarioId, bool flgTermos, DateTime dtaTermos)
        {
            var filter = Builders<Usuario>.Filter.Eq("_id", usuarioId);
            var update = Builders<Usuario>.Update
                .Set(u => u.FlgTermosECondicoes, flgTermos)
                .Set(u => u.DtaTermosECondicoes, dtaTermos);
            var result = await _collection.UpdateOneAsync(filter, update);
            return result.ModifiedCount > 0;
        }

        public async Task<bool> AtualizarPerfilFinanceiroAsync(string usuarioId, string perfilFinanceiro)
        {
            var filter = Builders<Usuario>.Filter.Eq("_id", usuarioId);
            var update = Builders<Usuario>.Update.Set(u => u.IdPerfilFinanceiro, perfilFinanceiro);
            var result = await _collection.UpdateOneAsync(filter, update);
            return result.ModifiedCount > 0;
        }

        public async Task<bool> AtualizarPorcentagensPerfilAsync(string usuarioId, int coracaoInquieto, int construtorAnalitico, int visionarioOusado, int exploradorGeneroso, int estrategistaConsciente)
        {
            var filter = Builders<Usuario>.Filter.Eq("_id", usuarioId);
            var update = Builders<Usuario>.Update
                .Set(u => u.CoracaoInquieto, coracaoInquieto)
                .Set(u => u.ConstrutorAnalitico, construtorAnalitico)
                .Set(u => u.VisionarioOusado, visionarioOusado)
                .Set(u => u.ExploradorGeneroso, exploradorGeneroso)
                .Set(u => u.EstrategistaConsciente, estrategistaConsciente);
            var result = await _collection.UpdateOneAsync(filter, update);
            return result.ModifiedCount > 0;
        }
    }
}