﻿using System.Security.Cryptography;
using System.Text;

namespace Shared.Utils
{
    public static class Criptografias
    {
        public static string Encriptar(string str, bool flgDescriptografa = false)
        {
            string senha = "";
            if (flgDescriptografa)
                senha = Criptografa_ComDescriptografia(str);
            else
                senha = Criptografa_SemDescriptografia(str);

            return senha;
        }

        public static string Desencripitar(string str)
        {
            return Descriptografa(str);
        }

        private static string Criptografa_ComDescriptografia(string senha)
        {
            byte[] textoBytes = Encoding.UTF8.GetBytes(senha);
            return Convert.ToBase64String(textoBytes);
        }

        private static string Criptografa_SemDescriptografia(string senha)
        {
            using (SHA256 sha256 = SHA256.Create())
            {
                byte[] bytes = Encoding.UTF8.GetBytes(senha);
                byte[] hash = sha256.ComputeHash(bytes);
                StringBuilder builder = new StringBuilder();
                foreach (byte b in hash)
                    builder.Append(b.ToString("x2")); // hex
                return builder.ToString();
            }
        }

        private static string Descriptografa(string senha)
        {
            byte[] textoBytes = Convert.FromBase64String(senha);
            return Encoding.UTF8.GetString(textoBytes);
        }
    }
}
