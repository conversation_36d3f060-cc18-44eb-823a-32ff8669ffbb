﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace ServiceClient.Interfaces.Generic
{
    public interface IGenericClientService<TViewModel, TEntidade>
        where TViewModel : class
        where TEntidade : class
    {
        string? IdUsuarioLogado { get; }

        Task<TViewModel?> BuscarPorIdAsync(string Id);

        Task<IEnumerable<TViewModel?>> BuscarTodosAsync();

        Task<IEnumerable<TViewModel?>> BuscarPorFiltroAsync(Expression<Func<TEntidade?, bool>> expression);

        Task<IEnumerable<TViewModel?>> BuscarPorFiltroPaginadoAsync(Expression<Func<TEntidade?, bool>> expression, int page, int pageSize);

        Task<int> BuscarContagemTotalPorFiltroAsync(Expression<Func<TEntidade?, bool>> expression);

        Task<int> BuscarContagemTotalAsync();
        Task<TViewModel?> BuscarPrimeiroPorFiltroAsync(Expression<Func<TEntidade?, bool>> expression);
        Task ExcluirAsync(TViewModel objViewModel);
        Task<TViewModel?> AdicionarAsync(TViewModel objViewModel);
        Task<List<TViewModel>?> AdicionarArrayAsync(List<TViewModel> arrayObjViewModel);
        Task<TViewModel?> EditarAsync(TViewModel objViewModel);
    }
}