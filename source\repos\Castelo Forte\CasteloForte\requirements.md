📄 Castelo Forte — Documento de Recursos do Boilerplate Flutter

**Última Atualização:** 10/07/2025
**Status:** Em Desenvolvimento Ativo - Fase de Implementação de Serviços Core

📋 Resumo
O boilerplate Castelo Forte é uma base de projeto Flutter moderna e escalável, pensada para servir como template para qualquer aplicativo móvel, especialmente com foco em gestão financeira pessoal.
Este documento descreve todos os recursos e funcionalidades incluídas, bem como detalhes técnicos e arquiteturais que o tornam adequado para produção e fácil de manter.

🎨 **Identidade Visual Oficial Implementada**
- **Cores Oficiais**: <PERSON><PERSON><PERSON> (#C4A35A), <PERSON><PERSON><PERSON> (#002147), <PERSON><PERSON><PERSON> (#4A4A4A), <PERSON><PERSON><PERSON> (#FDFDFD)
- **Logo Oficial**: Implementada em alta qualidade (300x300px login, 336x336px splash)
- **Design Minimalista**: Foco na logo sem elementos desnecessários

🏆 Objetivo
Fornecer uma estrutura inicial para desenvolvimento de aplicativos móveis com:

Telas essenciais já prontas.

Integração com API externa.

Notificações.

Gerenciamento de estado robusto.

Suporte para temas, internacionalização, gráficos e muito mais.

🧱 Funcionalidades Inclusas
✨ Funcionais
✅ Tela Splash Screen inicial - **IMPLEMENTADA**
  - Logo oficial do Castelo Forte (340x340px)
  - Fundo azul marinho com animações suaves
  - Validação de sessão automática
  - Design minimalista apenas com logo e spinner
  - Sem textos ou elementos decorativos

✅ Sistema completo de autenticação (login com e-mail e senha) - **IMPLEMENTADA**
  - Layout seguindo design oficial fornecido
  - Logo destacada (300x300px) sem decorações
  - Campos com validação em tempo real
  - Botão Google preparado para integração
  - Link "Crie uma conta" estilizado

✅ Tela de Cadastro de Usuário - **IMPLEMENTADA**
  - Formulário completo com validação
  - Campos para nome, email, telefone, data de nascimento
  - Senha com confirmação e toggle de visibilidade
  - Termos de uso com checkbox
  - Navegação fluida entre login e cadastro

✅ Configuração da API - **IMPLEMENTADA**
  - Tela para configurar URL base da API
  - Serviço ApiService configurado para comunicação
  - Tratamento de erros e timeout

✅ Integração simples e flexível com API externa (C#) - **IMPLEMENTADA**
  - Autenticação via token
  - Registro de novos usuários
  - Formatação de dados conforme especificação da API

✅ Controle de sessão do usuário com persistência local - **IMPLEMENTADA**
  - Armazenamento de token
  - Validação de sessão ativa
  - Logout com limpeza de dados

⏳ Notificações push (Firebase) e locais - **PLANEJADA**

⏳ Dashboard com saldo, resumo e últimas movimentações - **PLANEJADA**

⏳ Lançamentos financeiros com filtros e detalhamento - **PLANEJADA**

⏳ Relatórios com gráficos dinâmicos - **PLANEJADA**

⏳ Metas financeiras mensais e anuais - **PLANEJADA**

⏳ Gerenciamento de categorias personalizadas - **PLANEJADA**

⏳ Gerenciamento de contas bancárias - **PLANEJADA**

⏳ Exportação de relatórios em imagem/PDF - **PLANEJADA**

⏳ Central de notificações para alertas e lembretes - **PLANEJADA**

⏳ Tela de ajuda e suporte com FAQ e formulário - **PLANEJADA**

🎨 UI/UX
✅ **Identidade Visual Oficial** - **IMPLEMENTADA**
  - Cores oficiais do Castelo Forte aplicadas
  - Logo real integrada em alta qualidade
  - Design minimalista e profissional

✅ Responsividade para múltiplos tamanhos de tela - **IMPLEMENTADA**

✅ Temas claro e escuro configuráveis pelo usuário - **IMPLEMENTADA**
  - Cores oficiais integradas ao sistema de temas
  - Material Design 3 com paleta personalizada

⏳ Internacionalização (i18n) para múltiplos idiomas - **PLANEJADA**

✅ Feedback visual para erros, carregamentos e sucessos - **IMPLEMENTADA**

✅ Navegação fluida com go_router - **IMPLEMENTADA**

🧰 Técnicos
✅ Arquitetura limpa e modular - **IMPLEMENTADA**

⏳ Estado gerenciado com Riverpod - **EM PROGRESSO**

✅ Persistência local com shared_preferences - **IMPLEMENTADA**

✅ Código desacoplado por feature - **IMPLEMENTADA**

⏳ Suporte a 2FA (autenticação em dois fatores) - **PLANEJADA**

⏳ Histórico de login e auditoria básica - **PLANEJADA**

🗂️ Telas e Fluxos
Tela	Descrição	Status
✅ Splash Screen	Carregamento inicial com logo oficial (340x340px), fundo azul marinho, animações suaves, apenas logo e spinner	**IMPLEMENTADA**
✅ Login	Layout oficial com logo destacada (300x300px), campos estilizados, botão Google, validação em tempo real	**IMPLEMENTADA**
✅ Cadastro	Formulário completo com validação, campos para nome, email, telefone, data de nascimento, senha com confirmação	**IMPLEMENTADA**
✅ Dashboard (Básico)	Estrutura básica implementada, preparada para dados reais	**IMPLEMENTADA**
✅ Perfil (Básico)	Estrutura básica implementada, preparada para dados do usuário	**IMPLEMENTADA**
✅ Configurações da API	Tela para configurar URL base da API	**IMPLEMENTADA**
⏳ Editar Perfil	Atualização de foto e informações pessoais.	**PLANEJADA**
⏳ Configurações	Alterar tema, idioma e notificações.	**PLANEJADA**
⏳ Segurança	Ativar/desativar 2FA, alterar senha, visualizar histórico de login.	**PLANEJADA**
⏳ Lançamentos	Histórico financeiro por mês com filtros (entradas, saídas, todos).	**PLANEJADA**
⏳ Detalhe de Lançamento	Visualização e edição completa de um lançamento.	**PLANEJADA**
⏳ Cadastro de Lançamento	Formulário para criar um novo lançamento.	**PLANEJADA**
⏳ Relatórios	Gráficos por categoria e mês, metas atingidas, exportação.	**PLANEJADA**
⏳ Metas Avançadas	Definição e acompanhamento de metas financeiras.	**PLANEJADA**
⏳ Gerenciar Categorias	Criar, editar ou excluir categorias personalizadas.	**PLANEJADA**
⏳ Gerenciar Contas Bancárias	Adicionar ou editar contas e bancos vinculados.	**PLANEJADA**
⏳ Ajuda & Suporte	Perguntas frequentes e formulário de contato.	**PLANEJADA**
⏳ Central de Notificações	Histórico de notificações push e locais.	**PLANEJADA**

🔨 Arquitetura
📁 lib/main.dart: inicialização do app.

📁 lib/app.dart: widget principal com configuração de tema e rotas.

📁 lib/core: temas, serviços globais (API, notificações) e utilitários.

📁 lib/features: cada módulo funcional isolado em sua pasta.

📁 lib/routes: navegação centralizada com go_router.

Padrões:

Modularização por feature.

Camada de dados separada da apresentação.

Facilmente testável e extensível.

⚙️ Integração com a API
URL base configurável no serviço ApiService.

**IMPORTANTE**: Todos os endpoints da API seguem o padrão `/api` antes do controller:
- Login: `https://localhost:7097/api/Auth/Login`
- Registro: `https://localhost:7097/api/Auth/Register`
- Outros endpoints: `https://localhost:7097/api/[Controller]/[Action]`

Métodos REST (GET, POST, PUT, DELETE) com autenticação via token.

Persistência do token em shared_preferences.

Flexível para novas rotas e endpoints.

Integração com endpoints de autenticação e registro.

Configuração para ignorar certificados SSL em desenvolvimento.

🔔 Notificações
Push: implementadas com Firebase Cloud Messaging.

Locais: lembretes e alertas internos do app.

Central para histórico de notificações.

📈 Relatórios e Metas
Gráficos dinâmicos com fl_chart.

Relatórios mensais por categoria.

Progresso das metas exibido de forma visual.

Exportação dos relatórios em formato de imagem ou PDF.

📦 Dependências Principais
Pacote	Uso
flutter_riverpod	Gerenciamento de estado
go_router	Navegação
http	Consumo da API externa
firebase_messaging	Push notifications
flutter_local_notifications	Notificações locais
shared_preferences	Persistência local
intl	Datas e internacionalização
fl_chart	Gráficos
flutter_screenutil	Responsividade
image_picker	Upload de imagens

📚 Detalhes Importantes
✅ Pronto para produção: arquitetura sólida e boas práticas já aplicadas.
✅ Extensível: novas features podem ser adicionadas sem impactar as existentes.
✅ Internacionalização: pronto para múltiplos idiomas desde o início.
✅ Experiência de usuário: design consistente, responsivo e com suporte a temas.
✅ Segurança: suporte para 2FA e histórico de login, com autenticação segura.

📌 Próximos Passos Sugeridos
1. Implementar Riverpod para gerenciamento de estado
2. Refinar a integração com a API para dashboard
3. Adicionar ícone do Google real e integração com login social

📝 Conclusão
Este boilerplate foi desenvolvido para acelerar o início de novos projetos Flutter, fornecendo uma base robusta, escalável e profissional, cobrindo todas as funcionalidades esperadas em um app moderno de finanças pessoais — além de ser facilmente adaptável para outras áreas.

Seja para o projeto Castelo Forte ou futuros aplicativos, esta estrutura garante produtividade, qualidade e manutenção simplificada.

🔎 Revisão Final: Status das Telas

## ✅ **IMPLEMENTADAS COM DESIGN OFICIAL**
- ✅ **Splash Screen** - Logo oficial 340x340px, animações, cores oficiais, apenas logo e spinner
- ✅ **Login** - Layout do designer, logo 300x300px, formulário completo
- ✅ **Cadastro** - Formulário completo com validação, campos para nome, email, telefone, data de nascimento
- ✅ **Dashboard (Básico)** - Estrutura básica implementada
- ✅ **Perfil (Básico)** - Estrutura básica implementada
- ✅ **Configurações da API** - Tela para configurar URL base da API

## ⏳ **PLANEJADAS PARA IMPLEMENTAÇÃO**
- ⏳ Editar Perfil
- ⏳ Configurações
- ⏳ Segurança
- ⏳ Lançamentos
- ⏳ Detalhe de Lançamento
- ⏳ Cadastro de Lançamento
- ⏳ Relatórios
- ⏳ Metas Avançadas
- ⏳ Gerenciar Categorias
- ⏳ Gerenciar Contas Bancárias
- ⏳ Ajuda & Suporte
- ⏳ Central de Notificações

## 🎯 **PRÓXIMAS PRIORIDADES**
1. **Implementação do Riverpod** - Gerenciamento de estado
2. **Refinamento da API** - Integração com dashboard
3. **Ícone Google Real** - Substituir ícone temporário e integrar login social

