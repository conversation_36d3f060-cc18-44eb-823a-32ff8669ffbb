using Shared.Entities.Client;
using Shared.ViewModels.Base;
using System;
using System.ComponentModel.DataAnnotations;

namespace Shared.ViewModels.Client
{
    /// <summary>
    /// ViewModel para transação externa
    /// </summary>
    public class TransacaoExternaViewModel : BaseViewModel
    {
        #region Dados Obrigatórios
        /// <summary>
        /// Valor da transação
        /// </summary>
        [Required]
        public decimal Valor { get; set; }

        /// <summary>
        /// Descrição da transação
        /// </summary>
        [Required]
        [StringLength(500)]
        public string Descricao { get; set; } = "";

        /// <summary>
        /// Data da transação
        /// </summary>
        [Required]
        public DateTime DataTransacao { get; set; }

        /// <summary>
        /// ID de referência externa
        /// </summary>
        [Required]
        public string ExternalReferenceId { get; set; } = "";
        #endregion

        #region Dados Opcionais da Fonte
        /// <summary>
        /// Nome do estabelecimento
        /// </summary>
        public string? NomeEstabelecimento { get; set; }

        /// <summary>
        /// Sugestão de categoria
        /// </summary>
        public string? SugestaoCategoria { get; set; }

        /// <summary>
        /// Identificador da conta
        /// </summary>
        public string? IdentificadorConta { get; set; }

        /// <summary>
        /// Email do cliente
        /// </summary>
        public string? EmailCliente { get; set; }

        /// <summary>
        /// ID do cliente externo
        /// </summary>
        public string? IdClienteExterno { get; set; }

        /// <summary>
        /// Tipo de transação externa
        /// </summary>
        public string? TipoTransacaoExterna { get; set; }
        #endregion

        #region Processamento Interno
        /// <summary>
        /// Status do processamento
        /// </summary>
        public StatusProcessamentoExterno StatusProcessamento { get; set; } = StatusProcessamentoExterno.Recebida;

        /// <summary>
        /// Descrição do status
        /// </summary>
        public string StatusDescricao { get; set; } = "";

        /// <summary>
        /// ID do usuário identificado
        /// </summary>
        public string? IdUsuarioIdentificado { get; set; }

        /// <summary>
        /// Nome do usuário identificado (para exibição)
        /// </summary>
        public string? NomeUsuarioIdentificado { get; set; }

        /// <summary>
        /// ID da conta identificada
        /// </summary>
        public string? IdContaIdentificada { get; set; }

        /// <summary>
        /// Nome da conta identificada (para exibição)
        /// </summary>
        public string? NomeContaIdentificada { get; set; }

        /// <summary>
        /// ID da categoria atribuída
        /// </summary>
        public string? IdCategoriaAtribuida { get; set; }

        /// <summary>
        /// Nome da categoria atribuída (para exibição)
        /// </summary>
        public string? NomeCategoriaAtribuida { get; set; }

        /// <summary>
        /// Cor da categoria (para exibição)
        /// </summary>
        public string? CorCategoria { get; set; }

        /// <summary>
        /// Método de identificação usado
        /// </summary>
        public string? MetodoIdentificacao { get; set; }

        /// <summary>
        /// Pontuação de confiança
        /// </summary>
        [Range(0, 100)]
        public decimal? PontuacaoConfianca { get; set; }

        /// <summary>
        /// ID da transferência gerada
        /// </summary>
        public string? IdTransferenciaGerada { get; set; }

        /// <summary>
        /// Data de processamento
        /// </summary>
        public DateTime? DataProcessamento { get; set; }

        /// <summary>
        /// Motivo de falha
        /// </summary>
        public string? MotivoFalha { get; set; }

        /// <summary>
        /// Precisa de revisão
        /// </summary>
        public bool PrecisaRevisao { get; set; } = false;

        /// <summary>
        /// Motivo da revisão
        /// </summary>
        public string? MotivoRevisao { get; set; }

        /// <summary>
        /// Possível duplicata
        /// </summary>
        public bool PossivelDuplicata { get; set; } = false;

        /// <summary>
        /// ID da transação original (se duplicata)
        /// </summary>
        public string? IdTransacaoOriginal { get; set; }
        #endregion

        #region Propriedades Calculadas
        /// <summary>
        /// Verifica se foi processada com sucesso
        /// </summary>
        public bool IsProcessadaComSucesso => StatusProcessamento == StatusProcessamentoExterno.Concluida;

        /// <summary>
        /// Verifica se falhou
        /// </summary>
        public bool IsFalhou => StatusProcessamento == StatusProcessamentoExterno.Falhou;

        /// <summary>
        /// Verifica se está aguardando revisão
        /// </summary>
        public bool IsAguardandoRevisao => StatusProcessamento == StatusProcessamentoExterno.AguardandoRevisao || PrecisaRevisao;

        /// <summary>
        /// Verifica se pode ser reprocessada
        /// </summary>
        public bool PodeReprocessar => StatusProcessamento == StatusProcessamentoExterno.Falhou || 
                                       StatusProcessamento == StatusProcessamentoExterno.AguardandoRevisao;

        /// <summary>
        /// Verifica se tem confiança alta
        /// </summary>
        public bool TemConfiancaAlta => PontuacaoConfianca.HasValue && PontuacaoConfianca.Value >= 80;

        /// <summary>
        /// Valor formatado
        /// </summary>
        public string ValorFormatado => $"R$ {Valor:N2}";

        /// <summary>
        /// Data formatada
        /// </summary>
        public string DataFormatada => DataTransacao.ToString("dd/MM/yyyy");

        /// <summary>
        /// Data e hora formatada
        /// </summary>
        public string DataHoraFormatada => DataTransacao.ToString("dd/MM/yyyy HH:mm");

        /// <summary>
        /// Tempo desde o recebimento
        /// </summary>
        public string TempoDesdeRecebimento
        {
            get
            {
                var tempo = DateTime.Now - (DtaCadastro ?? DateTime.Now);
                if (tempo.TotalMinutes < 60)
                    return $"{(int)tempo.TotalMinutes} min atrás";
                if (tempo.TotalHours < 24)
                    return $"{(int)tempo.TotalHours}h atrás";
                return $"{(int)tempo.TotalDays} dias atrás";
            }
        }

        /// <summary>
        /// Cor do status para exibição
        /// </summary>
        public string CorStatus => StatusProcessamento switch
        {
            StatusProcessamentoExterno.Recebida => "#FFA500", // Laranja
            StatusProcessamentoExterno.Processando => "#2196F3", // Azul
            StatusProcessamentoExterno.UsuarioIdentificado => "#4CAF50", // Verde claro
            StatusProcessamentoExterno.ContaIdentificada => "#4CAF50", // Verde claro
            StatusProcessamentoExterno.CategoriaAtribuida => "#4CAF50", // Verde claro
            StatusProcessamentoExterno.TransferenciaGerada => "#4CAF50", // Verde claro
            StatusProcessamentoExterno.Concluida => "#4CAF50", // Verde
            StatusProcessamentoExterno.Falhou => "#F44336", // Vermelho
            StatusProcessamentoExterno.AguardandoRevisao => "#FF9800", // Amarelo
            _ => "#9E9E9E" // Cinza
        };

        /// <summary>
        /// Ícone do status para exibição
        /// </summary>
        public string IconeStatus => StatusProcessamento switch
        {
            StatusProcessamentoExterno.Recebida => "inbox",
            StatusProcessamentoExterno.Processando => "sync",
            StatusProcessamentoExterno.UsuarioIdentificado => "person_check",
            StatusProcessamentoExterno.ContaIdentificada => "account_balance",
            StatusProcessamentoExterno.CategoriaAtribuida => "label",
            StatusProcessamentoExterno.TransferenciaGerada => "swap_horiz",
            StatusProcessamentoExterno.Concluida => "check_circle",
            StatusProcessamentoExterno.Falhou => "error",
            StatusProcessamentoExterno.AguardandoRevisao => "pending",
            _ => "help"
        };

        /// <summary>
        /// Descrição da confiança
        /// </summary>
        public string DescricaoConfianca
        {
            get
            {
                if (!PontuacaoConfianca.HasValue) return "Não avaliada";
                
                return PontuacaoConfianca.Value switch
                {
                    >= 90 => "Muito Alta",
                    >= 80 => "Alta",
                    >= 60 => "Média",
                    >= 40 => "Baixa",
                    _ => "Muito Baixa"
                };
            }
        }
        #endregion
    }

    /// <summary>
    /// ViewModel para recebimento de transação externa via API
    /// </summary>
    public class TransacaoExternaInputViewModel
    {
        /// <summary>
        /// Valor da transação
        /// </summary>
        [Required]
        public decimal Valor { get; set; }

        /// <summary>
        /// Descrição da transação
        /// </summary>
        [Required]
        [StringLength(500)]
        public string Descricao { get; set; } = "";

        /// <summary>
        /// Data da transação
        /// </summary>
        [Required]
        public DateTime DataTransacao { get; set; }

        /// <summary>
        /// ID de referência externa único
        /// </summary>
        [Required]
        public string ExternalReferenceId { get; set; } = "";

        /// <summary>
        /// Nome do estabelecimento (opcional)
        /// </summary>
        public string? NomeEstabelecimento { get; set; }

        /// <summary>
        /// Sugestão de categoria (opcional)
        /// </summary>
        public string? SugestaoCategoria { get; set; }

        /// <summary>
        /// Identificador da conta (opcional)
        /// </summary>
        public string? IdentificadorConta { get; set; }

        /// <summary>
        /// Email do cliente (opcional)
        /// </summary>
        public string? EmailCliente { get; set; }

        /// <summary>
        /// ID do cliente externo (opcional)
        /// </summary>
        public string? IdClienteExterno { get; set; }

        /// <summary>
        /// Tipo de transação externa (opcional)
        /// </summary>
        public string? TipoTransacaoExterna { get; set; }

        /// <summary>
        /// Dados adicionais em JSON (opcional)
        /// </summary>
        public string? DadosAdicionaisJson { get; set; }
    }
}
