import 'package:flutter/material.dart';
import '../utils/navigation_helper.dart';

/// Botão de voltar seguro que verifica se pode fazer pop antes de navegar
class SafeBackButton extends StatelessWidget {
  final Color? color;
  final double? size;
  final VoidCallback? onPressed;
  final String? fallbackRoute;

  const SafeBackButton({
    super.key,
    this.color,
    this.size,
    this.onPressed,
    this.fallbackRoute,
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: Icon(
        Icons.arrow_back,
        color: color ?? Colors.white,
        size: size,
      ),
      onPressed: onPressed ?? () {
        if (fallbackRoute != null) {
          NavigationHelper.safeGoBackOr(context, fallbackRoute!);
        } else {
          NavigationHelper.safeGoBack(context);
        }
      },
    );
  }
}

/// AppBar personalizada com botão de voltar seguro
class SafeAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final VoidCallback? onBackPressed;
  final String? fallbackRoute;
  final bool centerTitle;
  final double elevation;

  const SafeAppBar({
    super.key,
    required this.title,
    this.actions,
    this.backgroundColor,
    this.foregroundColor,
    this.onBackPressed,
    this.fallbackRoute,
    this.centerTitle = true,
    this.elevation = 0,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: backgroundColor ?? Colors.transparent,
      foregroundColor: foregroundColor ?? Colors.white,
      elevation: elevation,
      centerTitle: centerTitle,
      leading: SafeBackButton(
        color: foregroundColor ?? Colors.white,
        onPressed: onBackPressed,
        fallbackRoute: fallbackRoute,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: foregroundColor ?? Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      actions: actions,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
