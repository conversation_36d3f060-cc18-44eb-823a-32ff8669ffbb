class CategoriaModel {
  final String? id;
  final String nome;
  final String? descricao;
  final String tipo; // '<PERSON><PERSON><PERSON>' ou 'Despesa'
  final String? cor;
  final String? icone;
  final int ordem;
  final String? idCategoriaPai;
  final String? nomeCategoriaPai;
  final double? limiteGastos;
  final String? periodoLimite;
  final bool ativa;
  final DateTime? dataCriacao;
  final DateTime? dataAlteracao;
  final int numeroTransacoes;
  final int numeroMetas;
  final double valorGasto;

  CategoriaModel({
    this.id,
    required this.nome,
    this.descricao,
    required this.tipo,
    this.cor,
    this.icone,
    this.ordem = 0,
    this.idCategoriaPai,
    this.nomeCategoriaPai,
    this.limiteGastos,
    this.periodoLimite,
    this.ativa = true,
    this.dataCriacao,
    this.dataAlteracao,
    this.numeroTransacoes = 0,
    this.numeroMetas = 0,
    this.valorGasto = 0.0,
  });

  factory CategoriaModel.fromJson(Map<String, dynamic> json) {
    return CategoriaModel(
      id: json['id'],
      nome: json['nome'] ?? '',
      descricao: json['descricao'],
      tipo: json['tipo'] ?? 'Despesa',
      cor: json['cor'],
      icone: json['icone'],
      ordem: json['ordem'] ?? 0,
      idCategoriaPai: json['idCategoriaPai'],
      nomeCategoriaPai: json['nomeCategoriaPai'],
      limiteGastos: json['limiteGastos']?.toDouble(),
      periodoLimite: json['periodoLimite'],
      ativa: json['ativa'] ?? true,
      dataCriacao: json['dataCriacao'] != null 
          ? DateTime.parse(json['dataCriacao']) 
          : null,
      dataAlteracao: json['dataAlteracao'] != null 
          ? DateTime.parse(json['dataAlteracao']) 
          : null,
      numeroTransacoes: json['numeroTransacoes'] ?? 0,
      numeroMetas: json['numeroMetas'] ?? 0,
      valorGasto: json['valorGasto']?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nome': nome,
      'descricao': descricao,
      'tipo': tipo,
      'cor': cor,
      'icone': icone,
      'ordem': ordem,
      'idCategoriaPai': idCategoriaPai,
      'limiteGastos': limiteGastos,
      'periodoLimite': periodoLimite,
      'ativa': ativa,
    };
  }

  CategoriaModel copyWith({
    String? id,
    String? nome,
    String? descricao,
    String? tipo,
    String? cor,
    String? icone,
    int? ordem,
    String? idCategoriaPai,
    String? nomeCategoriaPai,
    double? limiteGastos,
    String? periodoLimite,
    bool? ativa,
    DateTime? dataCriacao,
    DateTime? dataAlteracao,
    int? numeroTransacoes,
    int? numeroMetas,
    double? valorGasto,
  }) {
    return CategoriaModel(
      id: id ?? this.id,
      nome: nome ?? this.nome,
      descricao: descricao ?? this.descricao,
      tipo: tipo ?? this.tipo,
      cor: cor ?? this.cor,
      icone: icone ?? this.icone,
      ordem: ordem ?? this.ordem,
      idCategoriaPai: idCategoriaPai ?? this.idCategoriaPai,
      nomeCategoriaPai: nomeCategoriaPai ?? this.nomeCategoriaPai,
      limiteGastos: limiteGastos ?? this.limiteGastos,
      periodoLimite: periodoLimite ?? this.periodoLimite,
      ativa: ativa ?? this.ativa,
      dataCriacao: dataCriacao ?? this.dataCriacao,
      dataAlteracao: dataAlteracao ?? this.dataAlteracao,
      numeroTransacoes: numeroTransacoes ?? this.numeroTransacoes,
      numeroMetas: numeroMetas ?? this.numeroMetas,
      valorGasto: valorGasto ?? this.valorGasto,
    );
  }
}

class CategoriaCreateUpdateModel {
  final String nome;
  final String? descricao;
  final String tipo;
  final String? cor;
  final String? icone;
  final int ordem;
  final String? idCategoriaPai;
  final double? limiteGastos;
  final String? periodoLimite;

  CategoriaCreateUpdateModel({
    required this.nome,
    this.descricao,
    required this.tipo,
    this.cor,
    this.icone,
    this.ordem = 0,
    this.idCategoriaPai,
    this.limiteGastos,
    this.periodoLimite,
  });

  Map<String, dynamic> toJson() {
    return {
      'nome': nome,
      'descricao': descricao,
      'tipo': tipo,
      'cor': cor,
      'icone': icone,
      'ordem': ordem,
      'idCategoriaPai': idCategoriaPai,
      'limiteGastos': limiteGastos,
      'periodoLimite': periodoLimite,
    };
  }
}
