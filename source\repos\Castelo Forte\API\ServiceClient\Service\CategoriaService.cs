using AutoMapper;
using Microsoft.AspNetCore.Http;
using RepositoryClient.Interfaces;
using ServiceClient.Interfaces;
using ServiceClient.Repository.Generic;
using Shared.Entities.Client;
using Shared.ViewModels.Client;
using Shared.Enums;

namespace ServiceClient.Service
{
    /// <summary>
    /// Serviço para operações com categorias
    /// </summary>
    public class CategoriaService : GenericClientService<CategoriaViewModel, Categoria>, ICategoriaService
    {
        private readonly ICategoriaRepository _categoriaRepository;
        private readonly IMapper _mapper;

        public CategoriaService(
            ICategoriaRepository categoriaRepository,
            IHttpContextAccessor httpContextAccessor,
            IMapper mapper)
            : base(categoriaRepository, httpContextAccessor, mapper)
        {
            _categoriaRepository = categoriaRepository;
            _mapper = mapper;
        }

        /// <summary>
        /// Busca categorias por tipo
        /// </summary>
        public async Task<IEnumerable<CategoriaViewModel?>> BuscarPorTipoAsync(TipoCategoria tipo)
        {
            try
            {
                var categorias = await _categoriaRepository.BuscarPorTipoAsync(tipo);
                var viewModels = _mapper.Map<IEnumerable<CategoriaViewModel>>(categorias);
                
                // Enriquecer com dados adicionais
                await EnriquecerCategoriasAsync(viewModels);
                
                return viewModels;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar categorias por tipo: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca categorias ativas
        /// </summary>
        public async Task<IEnumerable<CategoriaViewModel?>> BuscarAtivasAsync()
        {
            try
            {
                var categorias = await _categoriaRepository.BuscarAtivasAsync();
                var viewModels = _mapper.Map<IEnumerable<CategoriaViewModel>>(categorias);
                
                await EnriquecerCategoriasAsync(viewModels);
                
                return viewModels;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar categorias ativas: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca categorias por nome
        /// </summary>
        public async Task<IEnumerable<CategoriaViewModel?>> BuscarPorNomeAsync(string nome)
        {
            try
            {
                var categorias = await _categoriaRepository.BuscarPorNomeAsync(nome);
                var viewModels = _mapper.Map<IEnumerable<CategoriaViewModel>>(categorias);
                
                await EnriquecerCategoriasAsync(viewModels);
                
                return viewModels;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar categorias por nome: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca categorias com paginação
        /// </summary>
        public async Task<CategoriaListViewModel> BuscarPaginadoAsync(
            int pagina = 1, 
            int tamanhoPagina = 10, 
            TipoCategoria? tipo = null, 
            bool? ativas = null)
        {
            try
            {
                var (categorias, total) = await _categoriaRepository.BuscarPaginadoAsync(pagina, tamanhoPagina, tipo, ativas);
                var viewModels = _mapper.Map<IEnumerable<CategoriaViewModel>>(categorias);
                
                await EnriquecerCategoriasAsync(viewModels);

                return new CategoriaListViewModel
                {
                    Categorias = viewModels,
                    Total = total,
                    Pagina = pagina,
                    TamanhoPagina = tamanhoPagina
                };
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar categorias paginadas: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Cria uma nova categoria
        /// </summary>
        public async Task<CategoriaViewModel?> CriarAsync(CategoriaCreateUpdateViewModel viewModel)
        {
            try
            {
                // Validar dados
                var erros = await ValidarCategoriaAsync(viewModel);
                if (erros.Any())
                {
                    throw new ArgumentException($"Dados inválidos: {string.Join(", ", erros)}");
                }

                // Mapear para entidade
                var categoria = _mapper.Map<Categoria>(viewModel);
                categoria.DataCriacao = DateTime.Now;
                categoria.Ativa = true;

                // Salvar no banco
                var categoriaCriada = await _categoriaRepository.AdicionarAsync(categoria);
                
                // Retornar ViewModel
                var resultado = _mapper.Map<CategoriaViewModel>(categoriaCriada);
                await EnriquecerCategoriaAsync(resultado);
                
                return resultado;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao criar categoria: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Atualiza uma categoria existente
        /// </summary>
        public async Task<CategoriaViewModel?> AtualizarAsync(string id, CategoriaCreateUpdateViewModel viewModel)
        {
            try
            {
                // Buscar categoria existente
                var categoriaExistente = await _categoriaRepository.BuscarPorIdAsync(id);
                if (categoriaExistente == null)
                {
                    throw new ArgumentException("Categoria não encontrada");
                }

                // Validar dados
                var erros = await ValidarCategoriaAsync(viewModel, id);
                if (erros.Any())
                {
                    throw new ArgumentException($"Dados inválidos: {string.Join(", ", erros)}");
                }

                // Atualizar propriedades
                _mapper.Map(viewModel, categoriaExistente);
                categoriaExistente.DataAlteracao = DateTime.Now;

                // Salvar no banco
                var categoriaAtualizada = await _categoriaRepository.EditarAsync(categoriaExistente);
                
                // Retornar ViewModel
                var resultado = _mapper.Map<CategoriaViewModel>(categoriaAtualizada);
                await EnriquecerCategoriaAsync(resultado);
                
                return resultado;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao atualizar categoria: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Exclui uma categoria (soft delete)
        /// </summary>
        public async Task<bool> ExcluirAsync(string id)
        {
            try
            {
                // Verificar se pode excluir
                var podeExcluir = await PodeExcluirAsync(id);
                if (!podeExcluir)
                {
                    throw new InvalidOperationException("Categoria não pode ser excluída pois possui transações ou metas associadas");
                }

                return await _categoriaRepository.InativarAsync(id);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao excluir categoria: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Reativa uma categoria
        /// </summary>
        public async Task<bool> ReativarAsync(string id)
        {
            try
            {
                return await _categoriaRepository.ReativarAsync(id);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao reativar categoria: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Verifica se uma categoria pode ser excluída
        /// </summary>
        public async Task<bool> PodeExcluirAsync(string id)
        {
            try
            {
                var numeroTransacoes = await _categoriaRepository.ContarTransacoesAsync(id);
                var numeroMetas = await _categoriaRepository.ContarMetasAsync(id);
                
                return numeroTransacoes == 0 && numeroMetas == 0;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao verificar se categoria pode ser excluída: {ex.Message}", ex);
            }
        }



        /// <summary>
        /// Valida se uma categoria pode ser criada/atualizada
        /// </summary>
        public async Task<IEnumerable<string>> ValidarCategoriaAsync(CategoriaCreateUpdateViewModel viewModel, string? id = null)
        {
            var erros = new List<string>();

            try
            {



                // Validar limite e período
                if (viewModel.LimiteGastos.HasValue && viewModel.LimiteGastos <= 0)
                {
                    erros.Add("Limite de gastos deve ser maior que zero");
                }

                if (viewModel.LimiteGastos.HasValue && !viewModel.PeriodoLimite.HasValue)
                {
                    erros.Add("Período do limite é obrigatório quando há limite de gastos");
                }
            }
            catch (Exception ex)
            {
                erros.Add($"Erro na validação: {ex.Message}");
            }

            return erros;
        }

        /// <summary>
        /// Enriquece uma lista de categorias com dados adicionais
        /// </summary>
        private async Task EnriquecerCategoriasAsync(IEnumerable<CategoriaViewModel> categorias)
        {
            foreach (var categoria in categorias)
            {
                await EnriquecerCategoriaAsync(categoria);
            }
        }

        /// <summary>
        /// Enriquece uma categoria com dados adicionais
        /// </summary>
        private async Task EnriquecerCategoriaAsync(CategoriaViewModel categoria)
        {
            try
            {
                // Buscar número de transações e metas
                categoria.NumeroTransacoes = await _categoriaRepository.ContarTransacoesAsync(categoria.Id);
                categoria.NumeroMetas = await _categoriaRepository.ContarMetasAsync(categoria.Id);

                // TODO: Calcular valor gasto na categoria (quando transações estiverem implementadas)
                categoria.ValorGasto = 0;
            }
            catch (Exception)
            {
                // Em caso de erro, manter valores padrão
                categoria.NumeroTransacoes = 0;
                categoria.NumeroMetas = 0;
                categoria.ValorGasto = 0;
            }
        }
    }
}
