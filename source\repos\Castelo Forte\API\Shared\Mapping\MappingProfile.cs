﻿using AutoMapper;
using Shared.Entities.Admin;
using Shared.Entities.Client;
using Shared.ViewModels.Admin;
using Shared.ViewModels.Client;

namespace Shared.Mapping
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            //Admin
            CreateMap<Usuario, UsuarioViewModel>().ReverseMap();
            CreateMap<LogErroAdmin, LogErroAdminViewModel>().ReverseMap();
            CreateMap<HistoricoUsuario, HistoricoUsuarioViewModel>().ReverseMap();

            //Client
            CreateMap<Cartao, CartaoViewModel>().ReverseMap();
            CreateMap<Categoria, CategoriaViewModel>().ReverseMap();
            CreateMap<CategoriaCreateUpdateViewModel, Categoria>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.DataCriacao, opt => opt.Ignore())
                .ForMember(dest => dest.DataAlteracao, opt => opt.Ignore())
                .ForMember(dest => dest.Ativa, opt => opt.Ignore());
            CreateMap<Conta, ContaViewModel>().ReverseMap();
            CreateMap<HistoricoUsuario, HistoricoUsuarioViewModel>().ReverseMap();
            CreateMap<LogErroClient, LogErroClientViewModel>().ReverseMap();
            CreateMap<LogNotificacao, LogNotificacaoViewModel>().ReverseMap();
            CreateMap<HistoricoUsuarioClient, HistoricoUsuarioClientViewModel>().ReverseMap();
            CreateMap<Meta, MetaViewModel>().ReverseMap();
            CreateMap<PlanoFinanceiro, PlanoFinanceiroViewModel>().ReverseMap();
            CreateMap<Transferencia, TransferenciaViewModel>().ReverseMap();
            CreateMap<TransferenciasRecorrentes, TransferenciaRecorrentesViewModel>().ReverseMap();

        }
    }
}