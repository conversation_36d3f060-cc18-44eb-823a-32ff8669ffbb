import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/constants.dart';
import '../../../core/services/logger_service.dart';
import '../../../core/services/secure_storage_service.dart';
import '../../../core/services/app_lifecycle_manager.dart';
import '../data/auth_service.dart';

/// Tela de re-entrada de senha para verificação de segurança
class PasswordReentryScreen extends StatefulWidget {
  final Map<String, dynamic> userInfo;
  final String? targetRoute;

  const PasswordReentryScreen({
    super.key,
    required this.userInfo,
    this.targetRoute,
  });

  @override
  State<PasswordReentryScreen> createState() => _PasswordReentryScreenState();
}

class _PasswordReentryScreenState extends State<PasswordReentryScreen>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  
  bool _isLoading = false;
  bool _obscurePassword = true;
  int _failedAttempts = 0;
  static const int _maxFailedAttempts = 3;
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    LoggerService.auth('Tela de re-entrada de senha iniciada para: ${widget.userInfo['nome']}');
  }

  @override
  void dispose() {
    _animationController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _animationController.forward();
  }

  Future<void> _handlePasswordVerification() async {
    if (!(_formKey.currentState?.validate() ?? false)) return;

    setState(() => _isLoading = true);

    try {
      LoggerService.auth('Verificando senha para re-autenticação...');

      // Usa o CPF das credenciais armazenadas
      final cpf = widget.userInfo['cpf'] as String;
      final password = _passwordController.text;

      // Realiza o login para verificar a senha
      final user = await AuthService.login(cpf, password);
      
      LoggerService.success('Re-autenticação bem-sucedida para: ${user.nome}');

      // Salva as credenciais atualizadas
      await SecureStorageService.saveUserCredentials(
        cpf: cpf,
        email: user.email,
        nome: user.nome,
      );

      // Marca o app como ativo novamente
      await AppLifecycleManager.instance.resetAppState();

      if (mounted) {
        // Navega para a rota de destino ou dashboard
        final targetRoute = widget.targetRoute ?? AppConstants.dashboardRoute;
        context.go(targetRoute);
      }
    } catch (e) {
      _failedAttempts++;
      LoggerService.warning('Falha na re-autenticação (tentativa $_failedAttempts): $e');

      if (mounted) {
        if (_failedAttempts >= _maxFailedAttempts) {
          _handleMaxAttemptsReached();
        } else {
          _showErrorMessage(
            'Senha incorreta. Tentativas restantes: ${_maxFailedAttempts - _failedAttempts}',
          );
        }
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _handleMaxAttemptsReached() {
    LoggerService.warning('Número máximo de tentativas de re-autenticação atingido');
    
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213E),
        title: const Text(
          'Muitas tentativas',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          'Você excedeu o número máximo de tentativas. Por segurança, você será redirecionado para a tela de login.',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => _logout(),
            child: const Text(
              'Entendi',
              style: TextStyle(color: Color(0xFF4ECDC4)),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _logout() async {
    try {
      await AuthService.logout();
      await SecureStorageService.clearAllSecureData();
      
      if (mounted) {
        context.go(AppConstants.loginRoute);
      }
    } catch (e) {
      LoggerService.error('Erro durante logout forçado: $e');
      if (mounted) {
        context.go(AppConstants.loginRoute);
      }
    }
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Por favor, digite sua senha';
    }
    if (value.length < 6) {
      return 'A senha deve ter pelo menos 6 caracteres';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.navyBlueColor,
      body: SafeArea(
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: child,
              ),
            );
          },
          child: Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Logo
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      color: const Color(0xFF4ECDC4).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(60),
                    ),
                    child: const Icon(
                      Icons.security,
                      size: 60,
                      color: Color(0xFF4ECDC4),
                    ),
                  ),
                  
                  const SizedBox(height: 32),
                  
                  // Título
                  const Text(
                    'Verificação de Segurança',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Subtítulo
                  Text(
                    'Olá, ${widget.userInfo['nome']}!\nPor favor, digite sua senha para continuar.',
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  const SizedBox(height: 40),
                  
                  // Formulário
                  Container(
                    constraints: const BoxConstraints(maxWidth: 400),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        children: [
                          // Campo de email (somente leitura)
                          TextFormField(
                            initialValue: widget.userInfo['email'],
                            enabled: false,
                            style: const TextStyle(color: Colors.white70),
                            decoration: InputDecoration(
                              labelText: 'Email',
                              labelStyle: const TextStyle(color: Colors.white54),
                              prefixIcon: const Icon(
                                Icons.email_outlined,
                                color: Colors.white54,
                              ),
                              filled: true,
                              fillColor: Colors.white.withValues(alpha: 0.05),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide.none,
                              ),
                              disabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: const BorderSide(
                                  color: Colors.white24,
                                ),
                              ),
                            ),
                          ),
                          
                          const SizedBox(height: 20),
                          
                          // Campo de senha
                          TextFormField(
                            controller: _passwordController,
                            obscureText: _obscurePassword,
                            style: const TextStyle(color: Colors.white),
                            validator: _validatePassword,
                            decoration: InputDecoration(
                              labelText: 'Senha',
                              labelStyle: const TextStyle(color: Colors.white54),
                              prefixIcon: const Icon(
                                Icons.lock_outline,
                                color: Colors.white54,
                              ),
                              suffixIcon: IconButton(
                                icon: Icon(
                                  _obscurePassword
                                      ? Icons.visibility_outlined
                                      : Icons.visibility_off_outlined,
                                  color: Colors.white54,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _obscurePassword = !_obscurePassword;
                                  });
                                },
                              ),
                              filled: true,
                              fillColor: Colors.white.withValues(alpha: 0.05),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide.none,
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: const BorderSide(
                                  color: Colors.white24,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: const BorderSide(
                                  color: Color(0xFF4ECDC4),
                                  width: 2,
                                ),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: const BorderSide(
                                  color: Colors.red,
                                ),
                              ),
                            ),
                          ),
                          
                          const SizedBox(height: 32),
                          
                          // Botão de verificar
                          SizedBox(
                            width: double.infinity,
                            height: 56,
                            child: ElevatedButton(
                              onPressed: _isLoading ? null : _handlePasswordVerification,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF4ECDC4),
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                elevation: 0,
                              ),
                              child: _isLoading
                                  ? const SizedBox(
                                      width: 24,
                                      height: 24,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(
                                          Colors.white,
                                        ),
                                      ),
                                    )
                                  : const Text(
                                      'Verificar Senha',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                            ),
                          ),
                          
                          const SizedBox(height: 24),
                          
                          // Link para logout
                          TextButton(
                            onPressed: _isLoading ? null : _logout,
                            child: const Text(
                              'Sair e fazer login novamente',
                              style: TextStyle(
                                color: Colors.white54,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
