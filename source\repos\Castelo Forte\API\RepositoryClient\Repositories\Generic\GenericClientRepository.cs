﻿using Microsoft.AspNetCore.Http;
using MongoDB.Driver;
using RepositoryAdmin.Interfaces;
using RepositoryAdmin.Interfaces.Generic;
using RepositoryClient.Configuration.Interfaces;
using Shared.Entities.Admin;
using Shared.Entities.Client;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace RepositoryClient.Repositories.Generic
{
    public class GenericClientRepository<TEntidade>(
    IContextoMultiTenantService contextoMultiTenant,
    IHttpContextAccessor httpContextAccessor,
    IUsuarioRepository usuarioRepository,
    string nomeColecao) : IGenericAdminRepository<TEntidade>, IDisposable
    where TEntidade : class
    {
        #region CONSTRUTOR

        protected readonly IContextoMultiTenantService _contextoMultiTenant = contextoMultiTenant;
        protected readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;
        protected readonly IUsuarioRepository _usuarioRepository = usuarioRepository;

        protected readonly string _nomeColecao = nomeColecao;
        private bool disposed = false;
        protected IMongoCollection<TEntidade> _collection => ObterCollectionAtual();
        protected IMongoDatabase _database => ObterBaseDadosAtual().Result;

        #endregion

        #region CONSULTAS

        public async Task<IEnumerable<TEntidade?>> BuscarTodosAsync()
        {
            try
            {
                return await _collection.Find(Builders<TEntidade>.Filter.Empty).ToListAsync();
            }
            catch (Exception ex) when (ex.Message.Contains("Padding is invalid") ||
                                     ex.Message.Contains("descriptografar") ||
                                     ex.InnerException?.Message.Contains("Padding is invalid") == true)
            {
                // Erro de criptografia detectado - tenta recuperação individual
                return await BuscarTodosComRecuperacaoAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar todas as entidades: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca todos os registros com recuperação individual para falhas de criptografia
        /// </summary>
        /// <returns>Lista de entidades válidas</returns>
        private async Task<IEnumerable<TEntidade?>> BuscarTodosComRecuperacaoAsync()
        {
            var resultados = new List<TEntidade?>();
            var registrosComErro = 0;

            try
            {
                // Busca registro por registro para contornar problemas de criptografia
                var cursor = await _collection.Find(Builders<TEntidade>.Filter.Empty).ToCursorAsync();

                await cursor.ForEachAsync(documento =>
                {
                    try
                    {
                        if (documento != null)
                        {
                            resultados.Add(documento);
                        }
                    }
                    catch (Exception)
                    {
                        registrosComErro++;
                        // Ignora registros com erro de criptografia e continua
                    }
                });

                if (registrosComErro > 0)
                {
                    // Log do problema mas não quebra a operação
                    Console.WriteLine($"Aviso: {registrosComErro} registros com erro de criptografia foram ignorados na coleção {_nomeColecao}");
                }

                return resultados;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro na recuperação de dados da coleção {_nomeColecao}: {ex.Message}", ex);
            }
        }

        public async Task<IEnumerable<TEntidade?>> BuscarPorFiltroAsync(Expression<Func<TEntidade?, bool>> expression)
        {
            try
            {
                if (expression == null)
                    throw new ArgumentNullException(nameof(expression), "Expressão de filtro não pode ser nula");

                return await _collection.Find(expression!).ToListAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao listar entidades com expressão personalizada: {ex.Message}", ex);
            }
        }

        public async Task<IEnumerable<TEntidade?>> BuscarPorFiltroPaginadoAsync(Expression<Func<TEntidade?, bool>> expression, int page, int pageSize)
        {
            try
            {
                if (expression == null)
                    throw new ArgumentNullException(nameof(expression), "Expressão de filtro não pode ser nula");

                return await _collection.Find(expression!)
                    .Skip((page - 1) * pageSize)
                    .Limit(pageSize)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao listar entidades com expressão personalizada: {ex.Message}", ex);
            }
        }

        public async Task<TEntidade?> BuscarPorIdAsync(string? Id)
        {
            try
            {
                if (string.IsNullOrEmpty(Id))
                    throw new ArgumentNullException(nameof(Id), "O ID não pode ser nulo ou vazio");

                var filter = Builders<TEntidade>.Filter.Eq("Id", Id);
                return await _collection.Find(filter).FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar entidade por ID '{Id}': {ex.Message}", ex);
            }
        }

        public async Task<int> BuscarContagemTotalPorFiltroAsync(Expression<Func<TEntidade?, bool>> expression)
        {
            try
            {
                return (int)await _collection.CountDocumentsAsync(expression!);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar contagem total de entidades: {ex.Message}", ex);
            }
        }

        public async Task<int> BuscarContagemTotalAsync()
        {
            try
            {
                return (int)await _collection.CountDocumentsAsync(Builders<TEntidade>.Filter.Empty);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar contagem total de entidades: {ex.Message}", ex);
            }
        }

        public async Task<TEntidade?> BuscarPrimeiroPorFiltroAsync(Expression<Func<TEntidade?, bool>> expression)
        {
            try
            {
                if (expression == null)
                    throw new ArgumentNullException(nameof(expression), "Expressão de filtro não pode ser nula");

                return await _collection.Find(expression!).FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar a entidade com expressão personalizada: {ex.Message}", ex);
            }
        }

        #endregion

        #region OPERAÇÕES

        public async Task<TEntidade?> AdicionarAsync(TEntidade Object)
        {
            try
            {
                if (Object == null)
                    throw new ArgumentNullException(nameof(Object), "Objeto a ser adicionado não pode ser nulo");

                await _collection.InsertOneAsync(Object);
                return Object;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao AdicionarAsync entidade: {ex.Message}", ex);
            }
        }

        public async Task<List<TEntidade>> AdicionarArrayAsync(List<TEntidade> array)
        {
            try
            {
                if (array == null)
                    throw new ArgumentNullException(nameof(array), "Objeto a ser adicionado não pode ser nulo");

                await _collection.InsertManyAsync(array);
                return array;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao AdicionarArrayAsync entidade: {ex.Message}", ex);
            }
        }

        public async Task<TEntidade?> EditarAsync(TEntidade objeto)
        {
            try
            {
                if (objeto == null)
                    throw new ArgumentNullException(nameof(objeto), "Objeto a ser editado não pode ser nulo");

                var idProperty = typeof(TEntidade).GetProperty("Id");
                if (idProperty == null)
                    throw new InvalidOperationException("A entidade não possui uma propriedade 'Id'");

                var id = idProperty.GetValue(objeto)?.ToString();
                if (string.IsNullOrEmpty(id))
                    throw new InvalidOperationException("O ID da entidade não pode ser nulo ou vazio");

                var filter = Builders<TEntidade>.Filter.Eq("Id", id);
                var entidadeAtual = await _collection.Find(filter).FirstOrDefaultAsync();

                if (entidadeAtual == null)
                    throw new KeyNotFoundException($"Entidade com ID '{id}' não encontrada");

                var updates = new List<UpdateDefinition<TEntidade>>();
                var props = typeof(TEntidade).GetProperties();

                foreach (var prop in props)
                {
                    if (prop.Name == "Id")
                        continue;

                    var novoValor = prop.GetValue(objeto);
                    var valorAtual = prop.GetValue(entidadeAtual);

                    if (novoValor == null)
                        continue;

                    if (!Equals(novoValor, valorAtual))
                    {
                        updates.Add(Builders<TEntidade>.Update.Set(prop.Name, novoValor));
                    }
                }

                if (updates.Count == 0)
                    return entidadeAtual;

                var updateDefinition = Builders<TEntidade>.Update.Combine(updates);
                var result = await _collection.UpdateOneAsync(filter, updateDefinition);

                return result.ModifiedCount > 0 ? objeto : entidadeAtual;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro no método: {System.Reflection.MethodBase.GetCurrentMethod()!.Name}: {ex.Message}", ex);
            }
        }

        public async Task<List<TEntidade>> EditarArrayAsync(List<TEntidade> array)
        {
            try
            {
                if (array == null || array.Count == 0)
                    throw new ArgumentNullException(nameof(array), "Array não pode ser nulo ou vazio");

                var idProperty = typeof(TEntidade).GetProperty("Id") ?? throw new InvalidOperationException("A entidade não possui uma propriedade 'Id'");
                var tasks = array.Select(async item =>
                {
                    var id = idProperty.GetValue(item)?.ToString();
                    if (string.IsNullOrEmpty(id))
                        throw new InvalidOperationException("O ID da entidade não pode ser nulo ou vazio");

                    var filter = Builders<TEntidade>.Filter.Eq("Id", id);
                    var result = await _collection.ReplaceOneAsync(filter, item);

                    if (result.ModifiedCount == 0 && result.MatchedCount == 0)
                        throw new KeyNotFoundException($"Entidade com ID '{id}' não encontrada");

                    return item;
                });

                var updatedItems = await Task.WhenAll(tasks);
                return [.. updatedItems];
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao editar array de entidades: {ex.Message}", ex);
            }
        }

        public async Task ExcluirAsync(TEntidade Object)
        {
            try
            {
                if (Object == null)
                    throw new ArgumentNullException(nameof(Object), "Objeto a ser excluído não pode ser nulo");

                var idProperty = typeof(TEntidade).GetProperty("Id") ?? throw new InvalidOperationException("A entidade não possui uma propriedade 'Id'");
                var id = idProperty.GetValue(Object)?.ToString();
                if (string.IsNullOrEmpty(id))
                    throw new InvalidOperationException("O ID da entidade não pode ser nulo ou vazio");

                var filter = Builders<TEntidade>.Filter.Eq("Id", id);
                var result = await _collection.DeleteOneAsync(filter);

                if (result.DeletedCount == 0)
                    throw new KeyNotFoundException($"Entidade com ID '{id}' não encontrada");
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao excluir entidade: {ex.Message}", ex);
            }
        }

        #endregion

        #region AUXILIARES

        protected async virtual Task<IMongoDatabase> ObterBaseDadosAtual(string? idUsuario = null)
        {
            try
            {
                if (!string.IsNullOrEmpty(idUsuario))
                {
                    return await _contextoMultiTenant.ObterBaseDadosUsuarioAsync(idUsuario);
                }

                var connectionStringDoContexto = _httpContextAccessor.HttpContext?.Items["ConnectionStringTenant"]?.ToString();
                if (!string.IsNullOrEmpty(connectionStringDoContexto))
                {
                    var nomeBase = _httpContextAccessor.HttpContext?.Items["NomeBaseDadosTenant"]?.ToString();
                    var clienteTenant = new MongoClient(connectionStringDoContexto);
                    return clienteTenant.GetDatabase(nomeBase ?? ExtrairNomeBaseDaConnectionString(connectionStringDoContexto));
                }

                try
                {
                    var connectionStringTenant = _contextoMultiTenant.ObterConnectionStringTenantAtual();
                    var nomeBaseTenant = _contextoMultiTenant.ObterNomeBaseDadosTenantAtual();

                    if (!string.IsNullOrEmpty(connectionStringTenant) && !string.IsNullOrEmpty(nomeBaseTenant))
                    {
                        var clienteTenant = new MongoClient(connectionStringTenant);
                        return clienteTenant.GetDatabase(nomeBaseTenant);
                    }
                }
                catch
                {
                }

                var UsuarioAtualId = _contextoMultiTenant.BuscarTenantAtualId();

                if (!string.IsNullOrEmpty(UsuarioAtualId))
                {
                    return await _contextoMultiTenant.ObterBaseDadosUsuarioAsync(UsuarioAtualId);
                }

                throw new ApplicationException("Não foi possível conectar a base de dados! Contexto de Usuario não encontrado.");
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao obter base de dados: {ex.Message}", ex);
            }
        }

        protected async Task<IMongoCollection<T>> ObterCollectionAsync<T>(string nomeColecao, string? idUsuario = null)
        {
            var database = await ObterBaseDadosAtual(idUsuario);
            return database.GetCollection<T>(nomeColecao);
        }

        protected async Task<IMongoDatabase> ObterDatabaseAsync(string? idUsuario = null)
        {
            return await ObterBaseDadosAtual(idUsuario);
        }

        protected IMongoCollection<TEntidade> ObterCollectionAtual(string? idUsuario = null)
        {
            var database = ObterBaseDadosAtual(idUsuario).Result;
            return database.GetCollection<TEntidade>(_nomeColecao);
        }

        private static string ExtrairNomeBaseDaConnectionString(string connectionString)
        {
            try
            {
                var mongoUrl = MongoUrl.Create(connectionString);
                return !string.IsNullOrEmpty(mongoUrl.DatabaseName) ? mongoUrl.DatabaseName : "Castelo_Tenant_Padrao";
            }
            catch
            {
                return "Castelo_Tenant_Padrao";
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposed)
                return;

            disposed = true;
        }
        #endregion

        #region CLASSES
        protected async Task<IMongoCollection<Meta>> GetCollectionMetasMultibaseAsync(string? idUsuario = null) => await ObterCollectionAsync<Meta>("Metas", idUsuario);
        protected async Task<IMongoCollection<Conta>> GetCollectionContaMultibaseAsync(string? idUsuario = null) => await ObterCollectionAsync<Conta>("Conta", idUsuario);
        protected async Task<IMongoCollection<Cartao>> GetCollectionCartaoMultibaseAsync(string? idUsuario = null) => await ObterCollectionAsync<Cartao>("Cartao", idUsuario);
        protected async Task<IMongoCollection<Transferencia>> GetCollectionTransferenciaMultibaseAsync(string? idUsuario = null) => await ObterCollectionAsync<Transferencia>("Transferencia", idUsuario);
        protected async Task<IMongoCollection<TransferenciasRecorrentes>> GetCollectionTransferenciasRecorrentesMultibaseAsync(string? idUsuario = null) => await ObterCollectionAsync<TransferenciasRecorrentes>("TransferenciasRecorrentes", idUsuario);
        protected async Task<IMongoCollection<LogErroClient>> GetCollectionLogErroClientMultibaseAsync(string? idUsuario = null) => await ObterCollectionAsync<LogErroClient>("LogErroClient", idUsuario);
        protected async Task<IMongoCollection<LogNotificacao>> GetCollectionLogNotificacaoMultibaseAsync(string? idUsuario = null) => await ObterCollectionAsync<LogNotificacao>("LogNotificacao", idUsuario);
        protected async Task<IMongoCollection<HistoricoEdicao>> GetCollectionHistoricoEdicaoMultibaseAsync(string? idUsuario = null) => await ObterCollectionAsync<HistoricoEdicao>("HistoricoEdicao", idUsuario);
        protected async Task<IMongoCollection<PlanoFinanceiro>> GetCollectionPlanoFinanceiroMultibaseAsync(string? idUsuario = null) => await ObterCollectionAsync<PlanoFinanceiro>("PlanoFinanceiro", idUsuario);
        #endregion
    }
}
