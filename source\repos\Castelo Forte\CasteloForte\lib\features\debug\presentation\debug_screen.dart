import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/constants.dart';
import '../../../core/services/app_lifecycle_manager.dart';
import '../../../core/services/secure_storage_service.dart';
import '../../../core/services/logger_service.dart';
import '../../auth/data/auth_service.dart';

/// Tela de debug para testar funcionalidades de re-autenticação
class DebugScreen extends StatefulWidget {
  const DebugScreen({super.key});

  @override
  State<DebugScreen> createState() => _DebugScreenState();
}

class _DebugScreenState extends State<DebugScreen> {
  Map<String, dynamic> _lifecycleStats = {};
  Map<String, dynamic>? _appState;
  Map<String, dynamic>? _userCredentials;
  bool _hasAuthToken = false;
  bool _needsReAuth = false;

  @override
  void initState() {
    super.initState();
    _loadDebugInfo();
  }

  Future<void> _loadDebugInfo() async {
    try {
      final stats = AppLifecycleManager.instance.getLifecycleStats();
      final appState = await SecureStorageService.getAppState();
      final credentials = await SecureStorageService.getUserCredentials();
      final hasToken = await SecureStorageService.hasAuthToken();
      final needsReAuth = await AuthService.needsReAuthentication();

      setState(() {
        _lifecycleStats = stats;
        _appState = appState;
        _userCredentials = credentials;
        _hasAuthToken = hasToken;
        _needsReAuth = needsReAuth;
      });
    } catch (e) {
      LoggerService.error('Erro ao carregar informações de debug: $e');
    }
  }

  Future<void> _simulateAppClosure() async {
    try {
      await AppLifecycleManager.instance.simulateAppClosureAndReopen();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Fechamento e reabertura simulados com sucesso!'),
          backgroundColor: Colors.green,
        ),
      );
      await _loadDebugInfo();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erro ao simular fechamento: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _resetAppState() async {
    try {
      await AppLifecycleManager.instance.resetAppState();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Estado do app resetado com sucesso!'),
          backgroundColor: Colors.green,
        ),
      );
      await _loadDebugInfo();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erro ao resetar estado: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _clearSecureData() async {
    try {
      await SecureStorageService.clearAllSecureData();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Dados seguros removidos com sucesso!'),
          backgroundColor: Colors.green,
        ),
      );
      await _loadDebugInfo();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erro ao limpar dados seguros: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _testReAuthentication() async {
    try {
      // Simula fechamento e reabertura
      await _simulateAppClosure();
      
      // Aguarda um pouco
      await Future.delayed(const Duration(seconds: 1));
      
      // Verifica se precisa de re-autenticação
      final needsReAuth = await AuthService.needsReAuthentication();
      
      if (needsReAuth) {
        // Vai para splash para testar o fluxo completo
        if (mounted) {
          context.go(AppConstants.splashRoute);
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Re-autenticação não é necessária'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erro no teste de re-autenticação: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.navyBlueColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => context.go(AppConstants.dashboardRoute),
        ),
        title: const Text(
          'Debug - Re-autenticação',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadDebugInfo,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status atual
            _buildSection(
              'Status Atual',
              [
                _buildInfoRow('Precisa Re-auth', _needsReAuth ? 'SIM' : 'NÃO'),
                _buildInfoRow('Tem Token', _hasAuthToken ? 'SIM' : 'NÃO'),
                _buildInfoRow('Tem Credenciais', _userCredentials != null ? 'SIM' : 'NÃO'),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Estatísticas do ciclo de vida
            _buildSection(
              'Ciclo de Vida',
              [
                _buildInfoRow('Inicializado', _lifecycleStats['isInitialized']?.toString() ?? 'N/A'),
                _buildInfoRow('Último Estado', _lifecycleStats['lastState']?.toString() ?? 'N/A'),
                _buildInfoRow('Rota Atual', _lifecycleStats['currentRoute']?.toString() ?? 'N/A'),
                _buildInfoRow('Pausado', _lifecycleStats['isPaused']?.toString() ?? 'N/A'),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Estado do app
            if (_appState != null) ...[
              _buildSection(
                'Estado do App',
                [
                  _buildInfoRow('Fechado Corretamente', _appState!['wasClosedProperly']?.toString() ?? 'N/A'),
                  _buildInfoRow('Última Atividade', _appState!['lastActiveTime']?.toString() ?? 'N/A'),
                  _buildInfoRow('Última Rota', _appState!['lastRoute']?.toString() ?? 'N/A'),
                ],
              ),
              const SizedBox(height: 20),
            ],
            
            // Credenciais do usuário
            if (_userCredentials != null) ...[
              _buildSection(
                'Credenciais Armazenadas',
                [
                  _buildInfoRow('Nome', _userCredentials!['nome']?.toString() ?? 'N/A'),
                  _buildInfoRow('Email', _userCredentials!['email']?.toString() ?? 'N/A'),
                  _buildInfoRow('CPF', _userCredentials!['cpf']?.toString() ?? 'N/A'),
                ],
              ),
              const SizedBox(height: 20),
            ],
            
            // Ações de teste
            _buildSection(
              'Ações de Teste',
              [],
              actions: [
                ElevatedButton.icon(
                  onPressed: _simulateAppClosure,
                  icon: const Icon(Icons.power_settings_new),
                  label: const Text('Simular Fechamento'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
                const SizedBox(height: 12),
                ElevatedButton.icon(
                  onPressed: _testReAuthentication,
                  icon: const Icon(Icons.security),
                  label: const Text('Testar Re-autenticação'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4ECDC4),
                    foregroundColor: Colors.white,
                  ),
                ),
                const SizedBox(height: 12),
                ElevatedButton.icon(
                  onPressed: _resetAppState,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Resetar Estado'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
                const SizedBox(height: 12),
                ElevatedButton.icon(
                  onPressed: _clearSecureData,
                  icon: const Icon(Icons.delete_forever),
                  label: const Text('Limpar Dados Seguros'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children, {List<Widget>? actions}) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF16213E),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white24),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (children.isNotEmpty) ...[
            const SizedBox(height: 12),
            ...children,
          ],
          if (actions != null) ...[
            const SizedBox(height: 12),
            ...actions,
          ],
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(color: Colors.white70, fontSize: 14),
          ),
          Flexible(
            child: Text(
              value,
              style: const TextStyle(color: Colors.white, fontSize: 14),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }
}
