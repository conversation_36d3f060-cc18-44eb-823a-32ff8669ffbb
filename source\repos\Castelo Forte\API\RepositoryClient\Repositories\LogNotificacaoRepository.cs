﻿
using Microsoft.AspNetCore.Http;
using RepositoryAdmin.Interfaces;
using RepositoryClient.Configuration.Interfaces;
using RepositoryClient.Interfaces;
using RepositoryClient.Repositories.Generic;
using Shared.Entities.Client;

namespace RepositoryClient.Repositories
{
    public class LogNotificacaoRepository(
        IContextoMultiTenantService contextoMultiTenant,
        IHttpContextAccessor httpContextAccessor,
         IUsuarioRepository usuarioRepository) : GenericClientRepository<LogNotificacao>(contextoMultiTenant, httpContextAccessor, usuarioRepository, "LogNotificacao"), ILogNotificacaoRepository
    {
    }
}
