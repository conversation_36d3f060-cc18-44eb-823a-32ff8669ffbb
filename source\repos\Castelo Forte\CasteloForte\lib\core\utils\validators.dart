import 'package:flutter/services.dart';

/// Classe com validadores e formatadores para formulários
class Validators {
  /// Valida email
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Por favor, digite seu email';
    }
    if (value.length > 100) {
      return 'O email deve ter no máximo 100 caracteres';
    }
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    if (!emailRegex.hasMatch(value)) {
      return 'Por favor, digite um email válido';
    }
    return null;
  }

  /// Valida CPF
  static String? validateCpf(String? value) {
    if (value == null || value.isEmpty) {
      return 'Por favor, digite seu CPF';
    }
    
    // Remove formatação para validação
    final cpfLimpo = value.replaceAll(RegExp(r'[^0-9]'), '');
    
    if (cpfLimpo.length != 11) {
      return 'CPF deve ter 11 dígitos';
    }
    
    if (!_isValidCpf(cpfLimpo)) {
      return 'CPF inválido';
    }
    
    return null;
  }

  /// Valida senha
  static String? validatePassword(String? value, {int minLength = 8}) {
    if (value == null || value.isEmpty) {
      return 'Por favor, digite sua senha';
    }
    if (value.length < minLength) {
      return 'A senha deve ter pelo menos $minLength caracteres';
    }
    if (value.length > 100) {
      return 'A senha deve ter no máximo 100 caracteres';
    }
    return null;
  }

  /// Valida confirmação de senha
  static String? validatePasswordConfirmation(String? value, String? password) {
    if (value == null || value.isEmpty) {
      return 'Por favor, confirme sua senha';
    }
    if (value != password) {
      return 'As senhas não coincidem';
    }
    return null;
  }

  /// Valida nome
  static String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Por favor, digite seu nome completo';
    }
    if (value.trim().length < 2) {
      return 'O nome deve ter pelo menos 2 caracteres';
    }
    if (value.trim().length > 100) {
      return 'O nome deve ter no máximo 100 caracteres';
    }
    if (RegExp(r'[0-9]').hasMatch(value)) {
      return 'O nome não deve conter números';
    }
    if (RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(value)) {
      return 'O nome não deve conter símbolos';
    }
    return null;
  }

  /// Valida telefone
  static String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'Por favor, digite seu telefone';
    }
    
    // Remove caracteres não numéricos para validação
    final numericValue = value.replaceAll(RegExp(r'[^0-9]'), '');
    
    if (numericValue.length < 10 || numericValue.length > 11) {
      return 'Telefone inválido. Use o formato (00) 00000-0000';
    }
    
    return null;
  }

  /// Valida data de nascimento
  static String? validateBirthDate(DateTime? selectedDate) {
    if (selectedDate == null) {
      return 'Por favor, selecione sua data de nascimento';
    }

    // Verifica se a pessoa tem pelo menos 18 anos
    final now = DateTime.now();
    final age = now.year - selectedDate.year;
    final hasHadBirthdayThisYear = now.month > selectedDate.month ||
        (now.month == selectedDate.month && now.day >= selectedDate.day);

    final actualAge = hasHadBirthdayThisYear ? age : age - 1;

    if (actualAge < 18) {
      return 'Você deve ter pelo menos 18 anos';
    }

    return null;
  }

  /// Valida se os termos foram aceitos
  static String? validateTermsAcceptance(bool accepted) {
    if (!accepted) {
      return 'Você precisa aceitar os termos de uso';
    }
    return null;
  }

  /// Valida CPF usando o algoritmo oficial
  static bool _isValidCpf(String cpf) {
    if (cpf.length != 11) return false;

    // Verifica se todos os dígitos são iguais
    if (RegExp(r'^(\d)\1*$').hasMatch(cpf)) return false;

    // Calcula o primeiro dígito verificador
    int sum = 0;
    for (int i = 0; i < 9; i++) {
      sum += int.parse(cpf[i]) * (10 - i);
    }
    int firstDigit = 11 - (sum % 11);
    if (firstDigit >= 10) firstDigit = 0;

    // Verifica o primeiro dígito
    if (int.parse(cpf[9]) != firstDigit) return false;

    // Calcula o segundo dígito verificador
    sum = 0;
    for (int i = 0; i < 10; i++) {
      sum += int.parse(cpf[i]) * (11 - i);
    }
    int secondDigit = 11 - (sum % 11);
    if (secondDigit >= 10) secondDigit = 0;

    // Verifica o segundo dígito
    return int.parse(cpf[10]) == secondDigit;
  }
}

/// Classe com formatadores de texto
class TextFormatters {
  /// Formatador de CPF
  static TextInputFormatter get cpfFormatter {
    return TextInputFormatter.withFunction((oldValue, newValue) {
      final text = newValue.text;
      if (text.length <= 11) {
        String formatted = '';
        for (int i = 0; i < text.length; i++) {
          if (i == 3 || i == 6) {
            formatted += '.';
          } else if (i == 9) {
            formatted += '-';
          }
          formatted += text[i];
        }
        return TextEditingValue(
          text: formatted,
          selection: TextSelection.collapsed(offset: formatted.length),
        );
      }
      return oldValue;
    });
  }

  /// Formatador de telefone
  static TextInputFormatter get phoneFormatter {
    return TextInputFormatter.withFunction((oldValue, newValue) {
      final text = newValue.text;
      if (text.isEmpty) return newValue;

      String formatted = '';
      if (text.length <= 2) {
        formatted = '($text';
      } else if (text.length <= 7) {
        formatted = '(${text.substring(0, 2)}) ${text.substring(2)}';
      } else if (text.length <= 11) {
        formatted = '(${text.substring(0, 2)}) ${text.substring(2, 7)}-${text.substring(7)}';
      } else {
        formatted = '(${text.substring(0, 2)}) ${text.substring(2, 7)}-${text.substring(7, 11)}';
      }

      return TextEditingValue(
        text: formatted,
        selection: TextSelection.collapsed(offset: formatted.length),
      );
    });
  }

  /// Formatador que remove números e símbolos (para nomes)
  static TextInputFormatter get nameFormatter {
    return TextInputFormatter.withFunction((oldValue, newValue) {
      final filteredText = newValue.text.replaceAll(
        RegExp(r'[0-9!@#$%^&*(),.?":{}|<>]'),
        '',
      );
      return TextEditingValue(
        text: filteredText,
        selection: TextSelection.collapsed(offset: filteredText.length),
      );
    });
  }
}
