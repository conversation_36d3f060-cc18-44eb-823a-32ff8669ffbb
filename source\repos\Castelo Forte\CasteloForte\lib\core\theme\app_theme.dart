import 'package:flutter/material.dart';

/// Configurações de tema da aplicação
class AppTheme {
  // Cores oficiais
  static const Color primaryColor = Color(0xFF002147); // Azul Marinho
  static const Color navyBlueColor = Color(
    0xFF002147,
  ); // A<PERSON><PERSON> (alias para primaryColor)
  static const Color goldColor = Color(0xFFC4A35A); // Dourado
  static const Color secondaryColor = Color(0xFF4A4A4A); // Cinza Chumbo
  static const Color snowWhiteColor = Color(0xFFFDFDFD); // Branco Neve
  static const Color charcoalGrayColor = Color(
    0xFF4A4A4A,
  ); // Cinza Chumbo (alias para secondaryColor)

  // Cores de texto
  static const Color primaryTextColor = Color(
    0xFF002147,
  ); // Texto principal (Azu<PERSON>)
  static const Color secondaryTextColor = Color(
    0xFF4A4A4A,
  ); // Texto secundário (Cinza Chumbo)
  static const Color tertiaryTextColor = Color(
    0xFF6E6E6E,
  ); // Texto terciário (Cinza mais claro)

  // Cores de status
  static const Color successColor = Color(0xFF4CAF50); // Verde
  static const Color errorColor = Color(0xFFE53935); // Vermelho
  static const Color warningColor = Color(0xFFFFC107); // Amarelo
  static const Color infoColor = Color(0xFF2196F3); // Azul

  // Cores de fundo
  static const Color backgroundLight = snowWhiteColor;
  static const Color backgroundDark = navyBlueColor;
  static const Color surfaceLight = snowWhiteColor;
  static const Color surfaceDark = Color(0xFF1A1A2E);

  // Cores de texto
  static const Color textPrimaryLight = charcoalGrayColor;
  static const Color textSecondaryLight = Color(0xFF757575);
  static const Color textPrimaryDark = snowWhiteColor;
  static const Color textSecondaryDark = Color(0xFFB0B0B0);

  /// Tema claro
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        brightness: Brightness.light,
        primary: primaryColor,
        secondary: secondaryColor,
        error: errorColor,
        surface: surfaceLight,
      ),
      scaffoldBackgroundColor: backgroundLight,
      appBarTheme: const AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: snowWhiteColor,
        elevation: 0,
        centerTitle: true,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: goldColor,
          foregroundColor: navyBlueColor,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(foregroundColor: goldColor),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: primaryColor, width: 2),
        ),
      ),
      cardTheme: CardThemeData(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  /// Tema escuro
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        brightness: Brightness.dark,
        primary: secondaryColor,
        secondary: primaryColor,
        error: errorColor,
        surface: surfaceDark,
      ),
      scaffoldBackgroundColor: backgroundDark,
      appBarTheme: const AppBarTheme(
        backgroundColor: surfaceDark,
        foregroundColor: textPrimaryDark,
        elevation: 0,
        centerTitle: true,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: secondaryColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(foregroundColor: secondaryColor),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: secondaryColor, width: 2),
        ),
      ),
      cardTheme: CardThemeData(
        elevation: 2,
        color: surfaceDark,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }
}
