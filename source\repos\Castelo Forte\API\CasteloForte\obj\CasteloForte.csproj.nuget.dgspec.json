{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\CasteloLeaoFinance\\Castelo Forte\\CasteloForte\\CasteloForte\\CasteloForte.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\CasteloLeaoFinance\\Castelo Forte\\CasteloForte\\CasteloForte\\CasteloForte.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\CasteloLeaoFinance\\Castelo Forte\\CasteloForte\\CasteloForte\\CasteloForte.csproj", "projectName": "<PERSON><PERSON>Fort<PERSON>", "projectPath": "C:\\Users\\<USER>\\source\\repos\\CasteloLeaoFinance\\Castelo Forte\\CasteloForte\\CasteloForte\\CasteloForte.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\CasteloLeaoFinance\\Castelo Forte\\CasteloForte\\CasteloForte\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}