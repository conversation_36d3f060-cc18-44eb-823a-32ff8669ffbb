import 'package:flutter/material.dart';

/// Modais reutilizáveis para telas de listagem
/// Baseado no design system da AccountsListScreen
class ListingModals {
  
  /// Modal de detalhes genérico
  static void showDetailsModal({
    required BuildContext context,
    required String title,
    required IconData titleIcon,
    required Color titleColor,
    required List<MapEntry<String, String>> details,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213E),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        title: Row(
          children: [
            Icon(titleIcon, color: titleColor),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: details.map((detail) => 
            _buildDetailRow(detail.key, detail.value)
          ).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Fechar',
              style: TextStyle(color: Color(0xFF4ECDC4)),
            ),
          ),
        ],
      ),
    );
  }

  /// Modal de confirmação genérico
  static void showConfirmationModal({
    required BuildContext context,
    required String title,
    required String message,
    required String confirmLabel,
    required VoidCallback onConfirm,
    String cancelLabel = 'Cancelar',
    Color confirmColor = Colors.red,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213E),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        title: Text(
          title,
          style: const TextStyle(color: Colors.white),
        ),
        content: Text(
          message,
          style: TextStyle(color: Colors.white.withValues(alpha: 0.7)),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              cancelLabel,
              style: const TextStyle(color: Color(0xFF4ECDC4)),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              onConfirm();
            },
            style: TextButton.styleFrom(foregroundColor: confirmColor),
            child: Text(confirmLabel),
          ),
        ],
      ),
    );
  }

  /// Linha de detalhe para modais
  static Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 60,
            child: Text(
              '$label:',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(value, style: const TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  /// SnackBar de feedback padronizado
  static void showFeedback({
    required BuildContext context,
    required String message,
    bool isSuccess = true,
    Duration duration = const Duration(seconds: 3),
  }) {
    if (!Scaffold.of(context).mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isSuccess ? Colors.green : Colors.red,
        duration: duration,
      ),
    );
  }

  /// Modal de ações (PopupMenuButton alternativo)
  static void showActionsModal({
    required BuildContext context,
    required String title,
    required List<ActionItem> actions,
  }) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xFF16213E),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 20),
            ...actions.map((action) => ListTile(
              leading: Icon(action.icon, color: action.color),
              title: Text(
                action.label,
                style: TextStyle(color: action.color),
              ),
              onTap: () {
                Navigator.pop(context);
                action.onTap();
              },
            )),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }
}

/// Item de ação para modal de ações
class ActionItem {
  final IconData icon;
  final String label;
  final Color color;
  final VoidCallback onTap;

  const ActionItem({
    required this.icon,
    required this.label,
    required this.color,
    required this.onTap,
  });
}
