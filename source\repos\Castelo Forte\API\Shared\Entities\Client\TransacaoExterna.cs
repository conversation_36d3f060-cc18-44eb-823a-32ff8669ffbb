using MongoDB.Bson.Serialization.Attributes;
using Shared.Entities.Base;
using Shared.Enums;
using System;
using System.ComponentModel.DataAnnotations;

namespace Shared.Entities.Client
{
    /// <summary>
    /// Status de processamento da transação externa
    /// </summary>
    public enum StatusProcessamentoExterno
    {
        Recebida = 1,
        Processando = 2,
        UsuarioIdentificado = 3,
        ContaIdentificada = 4,
        CategoriaAtribuida = 5,
        TransferenciaGerada = 6,
        Concluida = 7,
        Falhou = 8,
        AguardandoRevisao = 9
    }

    /// <summary>
    /// Entidade que representa uma transação recebida de fonte externa
    /// </summary>
    public class TransacaoExterna : BaseEntidade
    {
        #region Dados Obrigatórios
        /// <summary>
        /// Valor da transação
        /// </summary>
        [Required]
        public decimal Valor { get; set; }

        /// <summary>
        /// Descrição da transação
        /// </summary>
        [Required]
        [StringLength(500)]
        public string Descricao { get; set; } = "";

        /// <summary>
        /// Data da transação
        /// </summary>
        [Required]
        [DataType(DataType.Date)]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime DataTransacao { get; set; }

        /// <summary>
        /// ID de referência externa único
        /// </summary>
        [Required]
        public string ExternalReferenceId { get; set; } = "";
        #endregion

        #region Dados Opcionais da Fonte
        /// <summary>
        /// Nome do estabelecimento/comerciante
        /// </summary>
        public string? NomeEstabelecimento { get; set; }

        /// <summary>
        /// Sugestão de categoria da fonte externa
        /// </summary>
        public string? SugestaoCategoria { get; set; }

        /// <summary>
        /// Identificador da conta na fonte externa
        /// </summary>
        public string? IdentificadorConta { get; set; }

        /// <summary>
        /// Email do cliente (para identificação)
        /// </summary>
        public string? EmailCliente { get; set; }

        /// <summary>
        /// ID do cliente na fonte externa
        /// </summary>
        public string? IdClienteExterno { get; set; }

        /// <summary>
        /// Tipo de transação na fonte (débito, crédito, etc.)
        /// </summary>
        public string? TipoTransacaoExterna { get; set; }

        /// <summary>
        /// Dados JSON adicionais da fonte
        /// </summary>
        public string? DadosAdicionaisJson { get; set; }
        #endregion

        #region Processamento Interno
        /// <summary>
        /// Status do processamento
        /// </summary>
        public StatusProcessamentoExterno StatusProcessamento { get; set; } = StatusProcessamentoExterno.Recebida;

        /// <summary>
        /// ID do usuário identificado
        /// </summary>
        public string? IdUsuarioIdentificado { get; set; }

        /// <summary>
        /// ID da conta identificada
        /// </summary>
        public string? IdContaIdentificada { get; set; }

        /// <summary>
        /// ID da categoria atribuída
        /// </summary>
        public string? IdCategoriaAtribuida { get; set; }

        /// <summary>
        /// Método de identificação usado
        /// </summary>
        public string? MetodoIdentificacao { get; set; }

        /// <summary>
        /// Pontuação de confiança da identificação (0-100)
        /// </summary>
        [Range(0, 100)]
        public decimal? PontuacaoConfianca { get; set; }

        /// <summary>
        /// ID da transferência gerada
        /// </summary>
        public string? IdTransferenciaGerada { get; set; }

        /// <summary>
        /// Data de processamento
        /// </summary>
        [DataType(DataType.DateTime)]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime? DataProcessamento { get; set; }

        /// <summary>
        /// Motivo de falha (se houver)
        /// </summary>
        public string? MotivoFalha { get; set; }

        /// <summary>
        /// Log de processamento
        /// </summary>
        public string? LogProcessamento { get; set; }

        /// <summary>
        /// Indica se precisa de revisão manual
        /// </summary>
        public bool PrecisaRevisao { get; set; } = false;

        /// <summary>
        /// Motivo da necessidade de revisão
        /// </summary>
        public string? MotivoRevisao { get; set; }
        #endregion

        #region Hash e Duplicação
        /// <summary>
        /// Hash único para detectar duplicatas
        /// </summary>
        public string? HashTransacao { get; set; }

        /// <summary>
        /// Indica se é uma possível duplicata
        /// </summary>
        public bool PossivelDuplicata { get; set; } = false;

        /// <summary>
        /// ID da transação original (se for duplicata)
        /// </summary>
        public string? IdTransacaoOriginal { get; set; }
        #endregion

        #region Propriedades Calculadas
        /// <summary>
        /// Verifica se foi processada com sucesso
        /// </summary>
        public bool IsProcessadaComSucesso => StatusProcessamento == StatusProcessamentoExterno.Concluida;

        /// <summary>
        /// Verifica se falhou no processamento
        /// </summary>
        public bool IsFalhou => StatusProcessamento == StatusProcessamentoExterno.Falhou;

        /// <summary>
        /// Verifica se está aguardando revisão
        /// </summary>
        public bool IsAguardandoRevisao => StatusProcessamento == StatusProcessamentoExterno.AguardandoRevisao || PrecisaRevisao;

        /// <summary>
        /// Verifica se pode ser reprocessada
        /// </summary>
        public bool PodeReprocessar => StatusProcessamento == StatusProcessamentoExterno.Falhou || 
                                       StatusProcessamento == StatusProcessamentoExterno.AguardandoRevisao;

        /// <summary>
        /// Verifica se tem confiança alta na identificação
        /// </summary>
        public bool TemConfiancaAlta => PontuacaoConfianca.HasValue && PontuacaoConfianca.Value >= 80;

        /// <summary>
        /// Descrição do status
        /// </summary>
        public string StatusDescricao => StatusProcessamento switch
        {
            StatusProcessamentoExterno.Recebida => "Recebida",
            StatusProcessamentoExterno.Processando => "Processando",
            StatusProcessamentoExterno.UsuarioIdentificado => "Usuário Identificado",
            StatusProcessamentoExterno.ContaIdentificada => "Conta Identificada",
            StatusProcessamentoExterno.CategoriaAtribuida => "Categoria Atribuída",
            StatusProcessamentoExterno.TransferenciaGerada => "Transferência Gerada",
            StatusProcessamentoExterno.Concluida => "Concluída",
            StatusProcessamentoExterno.Falhou => "Falhou",
            StatusProcessamentoExterno.AguardandoRevisao => "Aguardando Revisão",
            _ => "Desconhecido"
        };
        #endregion

        #region Métodos de Negócio
        /// <summary>
        /// Gera hash único para a transação
        /// </summary>
        public void GerarHashTransacao()
        {
            var dados = $"{ExternalReferenceId}|{Valor}|{DataTransacao:yyyyMMddHHmmss}|{Descricao}|{IdentificadorConta}";
            HashTransacao = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(dados));
        }

        /// <summary>
        /// Atualiza o status de processamento
        /// </summary>
        /// <param name="novoStatus">Novo status</param>
        /// <param name="observacao">Observação sobre a mudança</param>
        public void AtualizarStatus(StatusProcessamentoExterno novoStatus, string? observacao = null)
        {
            StatusProcessamento = novoStatus;
            
            if (!string.IsNullOrEmpty(observacao))
            {
                LogProcessamento = string.IsNullOrEmpty(LogProcessamento) 
                    ? $"{DateTime.Now:yyyy-MM-dd HH:mm:ss}: {observacao}"
                    : $"{LogProcessamento}\n{DateTime.Now:yyyy-MM-dd HH:mm:ss}: {observacao}";
            }

            if (novoStatus == StatusProcessamentoExterno.Concluida || 
                novoStatus == StatusProcessamentoExterno.Falhou)
            {
                DataProcessamento = DateTime.Now;
            }
        }

        /// <summary>
        /// Marca como necessitando revisão
        /// </summary>
        /// <param name="motivo">Motivo da revisão</param>
        public void MarcarParaRevisao(string motivo)
        {
            PrecisaRevisao = true;
            MotivoRevisao = motivo;
            AtualizarStatus(StatusProcessamentoExterno.AguardandoRevisao, $"Marcada para revisão: {motivo}");
        }

        /// <summary>
        /// Marca como possível duplicata
        /// </summary>
        /// <param name="idOriginal">ID da transação original</param>
        public void MarcarComoDuplicata(string idOriginal)
        {
            PossivelDuplicata = true;
            IdTransacaoOriginal = idOriginal;
            MarcarParaRevisao("Possível duplicata detectada");
        }

        /// <summary>
        /// Registra falha no processamento
        /// </summary>
        /// <param name="motivo">Motivo da falha</param>
        public void RegistrarFalha(string motivo)
        {
            MotivoFalha = motivo;
            AtualizarStatus(StatusProcessamentoExterno.Falhou, $"Falha: {motivo}");
        }
        #endregion
    }
}
