﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shared.Utils
{
    public static class Utils
    {
        public static string GerarNomeBaseDados(string CPF)
        {
            if (string.IsNullOrEmpty(CPF))
                throw new ArgumentException("CPF é obrigatório");

            var cnpjCpfLimpo = CPF
                .Replace(".", "")
                .Replace("/", "")
                .Replace("-", "")
                .Replace(" ", "")
                .Trim();

            return $"CasteloForte_{cnpjCpfLimpo}";
        }

    }
}
