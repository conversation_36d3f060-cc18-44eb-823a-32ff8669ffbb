using Shared.Enums;
using Shared.ViewModels.Client;

namespace ServiceClient.Interfaces
{
    /// <summary>
    /// Interface para serviços de categoria
    /// </summary>
    public interface ICategoriaService
    {
        /// <summary>
        /// Busca todas as categorias
        /// </summary>
        /// <returns>Lista de categorias</returns>
        Task<IEnumerable<CategoriaViewModel?>> BuscarTodosAsync();

        /// <summary>
        /// Busca categoria por ID
        /// </summary>
        /// <param name="id">ID da categoria</param>
        /// <returns>Categoria encontrada ou null</returns>
        Task<CategoriaViewModel?> BuscarPorIdAsync(string? id);

        /// <summary>
        /// Busca categorias por tipo
        /// </summary>
        /// <param name="tipo">Tipo da categoria</param>
        /// <returns>Lista de categorias do tipo especificado</returns>
        Task<IEnumerable<CategoriaViewModel?>> BuscarPorTipoAsync(TipoCategoria tipo);

        /// <summary>
        /// Busca categorias ativas
        /// </summary>
        /// <returns>Lista de categorias ativas</returns>
        Task<IEnumerable<CategoriaViewModel?>> BuscarAtivasAsync();

        /// <summary>
        /// Busca categorias por nome
        /// </summary>
        /// <param name="nome">Nome ou parte do nome</param>
        /// <returns>Lista de categorias que contêm o nome</returns>
        Task<IEnumerable<CategoriaViewModel?>> BuscarPorNomeAsync(string nome);

        /// <summary>
        /// Busca categorias com paginação
        /// </summary>
        /// <param name="pagina">Número da página</param>
        /// <param name="tamanhoPagina">Tamanho da página</param>
        /// <param name="tipo">Filtro por tipo (opcional)</param>
        /// <param name="ativas">Filtro por ativas (opcional)</param>
        /// <returns>Lista paginada de categorias</returns>
        Task<CategoriaListViewModel> BuscarPaginadoAsync(
            int pagina = 1, 
            int tamanhoPagina = 10, 
            TipoCategoria? tipo = null, 
            bool? ativas = null);

        /// <summary>
        /// Cria uma nova categoria
        /// </summary>
        /// <param name="viewModel">Dados da categoria</param>
        /// <returns>Categoria criada</returns>
        Task<CategoriaViewModel?> CriarAsync(CategoriaCreateUpdateViewModel viewModel);

        /// <summary>
        /// Atualiza uma categoria existente
        /// </summary>
        /// <param name="id">ID da categoria</param>
        /// <param name="viewModel">Dados atualizados</param>
        /// <returns>Categoria atualizada</returns>
        Task<CategoriaViewModel?> AtualizarAsync(string id, CategoriaCreateUpdateViewModel viewModel);

        /// <summary>
        /// Exclui uma categoria (soft delete)
        /// </summary>
        /// <param name="id">ID da categoria</param>
        /// <returns>True se foi excluída com sucesso</returns>
        Task<bool> ExcluirAsync(string id);

        /// <summary>
        /// Reativa uma categoria
        /// </summary>
        /// <param name="id">ID da categoria</param>
        /// <returns>True se foi reativada com sucesso</returns>
        Task<bool> ReativarAsync(string id);

        /// <summary>
        /// Verifica se uma categoria pode ser excluída
        /// </summary>
        /// <param name="id">ID da categoria</param>
        /// <returns>True se pode ser excluída</returns>
        Task<bool> PodeExcluirAsync(string id);



        /// <summary>
        /// Valida se uma categoria pode ser criada/atualizada
        /// </summary>
        /// <param name="viewModel">Dados da categoria</param>
        /// <param name="id">ID da categoria (para atualização)</param>
        /// <returns>Lista de erros de validação</returns>
        Task<IEnumerable<string>> ValidarCategoriaAsync(CategoriaCreateUpdateViewModel viewModel, string? id = null);
    }
}
