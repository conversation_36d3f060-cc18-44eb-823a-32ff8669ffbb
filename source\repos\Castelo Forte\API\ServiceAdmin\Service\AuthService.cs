﻿using AutoMapper;
using Shared.Models;
using Shared.Entities.Admin;
using ServiceAdmin.Interfaces;
using Shared.ViewModels.Admin;
using Microsoft.AspNetCore.Http;
using RepositoryAdmin.Interfaces;
using ServiceAdmin.Repository.Generic;
using static Shared.Models.LoginModels;
using Shared.Utils;

namespace ServiceAdmin.Service
{
    public class AuthService(
        IUsuarioRepository repository,
        IHttpContextAccessor httpContextAccessor,
        IMapper mapper) : GenericAdminService<UsuarioViewModel, Usuario>(repository, httpContextAccessor, mapper), IAuthService
    {

        private readonly IUsuarioRepository _Repository = repository;

        public async Task<TokenAuth> Autenticar(LoginViewModel login)
        {
            try
            {
                string senhaCriptografada = Criptografias.Encriptar(login.Senha, false);
                Usuario? user = await _Repository.BuscarPrimeiroPorFiltroAsync(x => x.Cpf == login.CPF && x.Senha == senhaCriptografada);

                if (user == null) throw new Exception("Usuário não encontrado");
                if (user.FlgAtivo != true) throw new Exception("Usuário inativado");
                if (user.FlgDoisFatores)
                {
                    throw new Exception("");
                }
                
                else
                {
                    throw new Exception("");
                }
            }
            catch (Exception)
            {

                throw;
            }

        }
    }
}
