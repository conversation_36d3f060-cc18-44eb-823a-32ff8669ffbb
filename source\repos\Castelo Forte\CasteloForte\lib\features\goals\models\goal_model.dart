/// Modelo de dados para meta financeira
class GoalModel {
  final String id;
  final String titulo;
  final String descricao;
  final double valorMeta;
  final double valorAtual;
  final DateTime dataInicio;
  final DateTime dataFim;
  final String categoria;
  final String cor;
  final bool ativa;

  const GoalModel({
    required this.id,
    required this.titulo,
    required this.descricao,
    required this.valorMeta,
    required this.valorAtual,
    required this.dataInicio,
    required this.dataFim,
    required this.categoria,
    required this.cor,
    required this.ativa,
  });

  /// Cria uma instância a partir de JSON
  factory GoalModel.fromJson(Map<String, dynamic> json) {
    return GoalModel(
      id: json['id']?.toString() ?? '',
      titulo: json['titulo']?.toString() ?? '',
      descricao: json['descricao']?.toString() ?? '',
      valorMeta: (json['valorMeta'] as num?)?.toDouble() ?? 0.0,
      valorAtual: (json['valorAtual'] as num?)?.toDouble() ?? 0.0,
      dataInicio: json['dataInicio'] != null 
          ? DateTime.parse(json['dataInicio'].toString())
          : DateTime.now(),
      dataFim: json['dataFim'] != null 
          ? DateTime.parse(json['dataFim'].toString())
          : DateTime.now().add(const Duration(days: 365)),
      categoria: json['categoria']?.toString() ?? '',
      cor: json['cor']?.toString() ?? '#4ECDC4',
      ativa: json['ativa'] as bool? ?? true,
    );
  }

  /// Converte para JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'titulo': titulo,
      'descricao': descricao,
      'valorMeta': valorMeta,
      'valorAtual': valorAtual,
      'dataInicio': dataInicio.toIso8601String(),
      'dataFim': dataFim.toIso8601String(),
      'categoria': categoria,
      'cor': cor,
      'ativa': ativa,
    };
  }

  /// Calcula o progresso da meta (0.0 a 1.0)
  double get progresso {
    if (valorMeta <= 0) return 0.0;
    final progress = valorAtual / valorMeta;
    return progress > 1.0 ? 1.0 : progress;
  }

  /// Verifica se a meta foi concluída
  bool get isConcluida => valorAtual >= valorMeta;

  /// Calcula quantos dias restam para a meta
  int get diasRestantes {
    final agora = DateTime.now();
    if (dataFim.isBefore(agora)) return 0;
    return dataFim.difference(agora).inDays;
  }

  /// Verifica se a meta está vencida
  bool get isVencida => DateTime.now().isAfter(dataFim) && !isConcluida;

  /// Calcula quanto falta para atingir a meta
  double get valorRestante {
    final restante = valorMeta - valorAtual;
    return restante > 0 ? restante : 0.0;
  }

  /// Retorna o progresso em porcentagem
  double get progressoPorcentagem => progresso * 100;

  /// Retorna a cor da meta como objeto Color
  int get colorValue {
    try {
      // Remove o # se existir e converte para int
      String colorString = cor.replaceAll('#', '');
      if (colorString.length == 6) {
        colorString = 'FF$colorString'; // Adiciona alpha
      }
      return int.parse(colorString, radix: 16);
    } catch (e) {
      return 0xFF4ECDC4; // Cor padrão
    }
  }

  /// Retorna o valor atual formatado
  String get valorAtualFormatado {
    return 'R\$ ${valorAtual.toStringAsFixed(2).replaceAll('.', ',')}';
  }

  /// Retorna o valor da meta formatado
  String get valorMetaFormatado {
    return 'R\$ ${valorMeta.toStringAsFixed(2).replaceAll('.', ',')}';
  }

  /// Retorna o valor restante formatado
  String get valorRestanteFormatado {
    return 'R\$ ${valorRestante.toStringAsFixed(2).replaceAll('.', ',')}';
  }

  /// Retorna o status da meta
  String get status {
    if (isConcluida) return 'Concluída';
    if (isVencida) return 'Vencida';
    if (diasRestantes <= 30) return 'Urgente';
    return 'Em andamento';
  }

  /// Cria uma cópia com campos modificados
  GoalModel copyWith({
    String? id,
    String? titulo,
    String? descricao,
    double? valorMeta,
    double? valorAtual,
    DateTime? dataInicio,
    DateTime? dataFim,
    String? categoria,
    String? cor,
    bool? ativa,
  }) {
    return GoalModel(
      id: id ?? this.id,
      titulo: titulo ?? this.titulo,
      descricao: descricao ?? this.descricao,
      valorMeta: valorMeta ?? this.valorMeta,
      valorAtual: valorAtual ?? this.valorAtual,
      dataInicio: dataInicio ?? this.dataInicio,
      dataFim: dataFim ?? this.dataFim,
      categoria: categoria ?? this.categoria,
      cor: cor ?? this.cor,
      ativa: ativa ?? this.ativa,
    );
  }

  @override
  String toString() {
    return 'GoalModel(id: $id, titulo: $titulo, progresso: ${progressoPorcentagem.toStringAsFixed(1)}%)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GoalModel &&
        other.id == id &&
        other.titulo == titulo &&
        other.valorMeta == valorMeta &&
        other.valorAtual == valorAtual;
  }

  @override
  int get hashCode {
    return Object.hash(id, titulo, valorMeta, valorAtual);
  }
}
