/// Modelo de dados para cartão de crédito
class CartaoModel {
  final String id;
  final String nomeCartao;
  final String ultimosDigitos;
  final String bandeira;
  final bool ativo;

  const CartaoModel({
    required this.id,
    required this.nomeCartao,
    required this.ultimosDigitos,
    required this.bandeira,
    required this.ativo,
  });

  /// Cria uma instância a partir de JSON
  factory CartaoModel.fromJson(Map<String, dynamic> json) {
    return CartaoModel(
      id: json['id']?.toString() ?? '',
      nomeCartao: json['nomeCartao']?.toString() ?? '',
      ultimosDigitos: json['ultimosDigitos']?.toString() ?? '',
      bandeira: json['bandeira']?.toString() ?? '',
      ativo: json['ativo'] as bool? ?? true,
    );
  }

  /// Converte para JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nomeCartao': nomeCartao,
      'ultimosDigitos': ultimosDigitos,
      'bandeira': bandeira,
      'ativo': ativo,
    };
  }

  /// Cria uma cópia com campos modificados
  CartaoModel copyWith({
    String? id,
    String? nomeCartao,
    String? ultimosDigitos,
    String? bandeira,
    bool? ativo,
  }) {
    return CartaoModel(
      id: id ?? this.id,
      nomeCartao: nomeCartao ?? this.nomeCartao,
      ultimosDigitos: ultimosDigitos ?? this.ultimosDigitos,
      bandeira: bandeira ?? this.bandeira,
      ativo: ativo ?? this.ativo,
    );
  }

  @override
  String toString() {
    return 'CartaoModel(id: $id, nomeCartao: $nomeCartao, ultimosDigitos: $ultimosDigitos, bandeira: $bandeira, ativo: $ativo)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartaoModel &&
        other.id == id &&
        other.nomeCartao == nomeCartao &&
        other.ultimosDigitos == ultimosDigitos &&
        other.bandeira == bandeira &&
        other.ativo == ativo;
  }

  @override
  int get hashCode {
    return Object.hash(id, nomeCartao, ultimosDigitos, bandeira, ativo);
  }
}
