import '../../../core/services/api_service.dart';
import '../../../core/services/logger_service.dart';

/// Serviço para operações com transações
class TransactionsService {
  static const String _baseUrl = 'transferencia';

  /// Cria uma nova transação
  static Future<bool> createTransaction({
    required String description,
    required double amount,
    required String type,
    required String accountId,
    required String categoryId,
    required DateTime date,
  }) async {
    try {
      LoggerService.info('Criando nova transação: $description');

      // Estrutura de dados que corresponde ao TransferenciaViewModel da API
      final transactionData = {
        'descricao': description,
        'valor': amount,
        'dataTransferencia': date.toIso8601String(),
        'idContaOrigem': accountId,
        'idCategoria': categoryId,
        'tipo': type,
        'status': 1, // TransferenciaStatus.Concluida
        'origem': 1, // OrigemTransacao.Manual
        'formaPagamento': type == 'DESPESA' ? 'Débito' : 'Crédito',
        'flgPagamentoCredito': type == 'RECEITA',
      };

      LoggerService.debug('Dados da transação: ${transactionData.toString()}');

      await ApiService.post(_baseUrl, transactionData);
      LoggerService.info('Transação criada com sucesso na API');
      return true;
    } catch (e) {
      LoggerService.failure('Erro ao criar transação: $e');
      return false;
    }
  }

  /// Busca transações do usuário
  static Future<List<Map<String, dynamic>>> getTransactions({
    int page = 1,
    int limit = 20,
    String? accountId,
    String? categoryId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      LoggerService.info('Buscando transações - página $page...');

      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (accountId != null) queryParams['accountId'] = accountId;
      if (categoryId != null) queryParams['categoryId'] = categoryId;
      if (startDate != null) {
        queryParams['startDate'] = startDate.toIso8601String();
      }
      if (endDate != null) queryParams['endDate'] = endDate.toIso8601String();

      final url = queryParams.isEmpty
          ? _baseUrl
          : '$_baseUrl?${queryParams.entries.map((e) => '${e.key}=${e.value}').join('&')}';

      try {
        final response = await ApiService.get(url);
        LoggerService.info('Transações obtidas da API com sucesso');

        final List<Map<String, dynamic>> transactions = [];
        dynamic responseData = response;

        if (responseData is Map<String, dynamic> &&
            responseData.containsKey('data')) {
          responseData = responseData['data'];
        }

        if (responseData is List) {
          for (final item in responseData) {
            if (item is Map) {
              transactions.add(Map<String, dynamic>.from(item));
            }
          }
        } else {
          LoggerService.warning('Resposta da API não contém lista válida');
        }

        return transactions;
      } catch (apiError) {
        LoggerService.warning('Erro na API: $apiError');
        return [];
      }
    } catch (e) {
      LoggerService.failure('Erro ao buscar transações: $e');
      return [];
    }
  }

  /// Atualiza uma transação existente
  static Future<bool> updateTransaction(
    String id,
    Map<String, dynamic> transactionData,
  ) async {
    try {
      LoggerService.info('Atualizando transação: $id');

      try {
        await ApiService.put('$_baseUrl/$id', transactionData);
        LoggerService.info('Transação atualizada com sucesso na API');
        return true;
      } catch (apiError) {
        LoggerService.warning('Erro na API ao atualizar transação: $apiError');
        return true;
      }
    } catch (e) {
      LoggerService.failure('Erro ao atualizar transação: $e');
      return false;
    }
  }

  /// Exclui uma transação
  static Future<bool> deleteTransaction(String id) async {
    try {
      LoggerService.info('Excluindo transação: $id');

      try {
        await ApiService.delete('$_baseUrl/$id');
        LoggerService.info('Transação excluída com sucesso na API');
        return true;
      } catch (apiError) {
        LoggerService.warning('Erro na API ao excluir transação: $apiError');
        return true;
      }
    } catch (e) {
      LoggerService.failure('Erro ao excluir transação: $e');
      return false;
    }
  }
}
