using AutoMapper;
using Microsoft.AspNetCore.Http;
using RepositoryClient.Interfaces;
using ServiceClient.Interfaces;
using ServiceClient.Repository.Generic;
using Shared.Entities.Client;
using Shared.Enums;
using Shared.ViewModels.Client;

namespace ServiceClient.Service
{
    /// <summary>
    /// Serviço consolidado para Conta/Cartão no contexto Client (multi-tenant)
    /// </summary>
    public class ContaService : GenericClientService<ContaViewModel, Conta>, IContaService
    {
        private readonly IContaRepository _contaRepository;

        public ContaService(
            IContaRepository contaRepository,
            IHttpContextAccessor httpContextAccessor,
            IMapper mapper) : base(contaRepository, httpContextAccessor, mapper)
        {
            _contaRepository = contaRepository ?? throw new ArgumentNullException(nameof(contaRepository));
        }

        /// <summary>
        /// Busca contas por tipo
        /// </summary>
        /// <param name="tipo">Tipo da conta</param>
        /// <returns>Lista de contas do tipo especificado</returns>
        public async Task<IEnumerable<ContaViewModel>> BuscarPorTipoAsync(string tipo)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(tipo))
                    throw new ArgumentException("Tipo não pode ser nulo ou vazio", nameof(tipo));

                var contas = await BuscarTodosAsync();
                return contas.Where(c => c.TipoContaDescricao.Equals(tipo, StringComparison.OrdinalIgnoreCase));
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar contas por tipo: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca contas por tipo usando enum
        /// </summary>
        /// <param name="tipoConta">Tipo da conta</param>
        /// <returns>Lista de contas do tipo especificado</returns>
        public async Task<IEnumerable<ContaViewModel>> BuscarPorTipoAsync(AccountType tipoConta)
        {
            try
            {
                var contas = await BuscarTodosAsync();
                return contas.Where(c => c.TipoConta == tipoConta);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar contas por tipo: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca apenas cartões de crédito
        /// </summary>
        /// <returns>Lista de cartões de crédito</returns>
        public async Task<IEnumerable<ContaViewModel>> BuscarCartoesAsync()
        {
            return await BuscarPorTipoAsync(AccountType.CartaoCredito);
        }

        /// <summary>
        /// Busca apenas contas bancárias (corrente e poupança)
        /// </summary>
        /// <returns>Lista de contas bancárias</returns>
        public async Task<IEnumerable<ContaViewModel>> BuscarContasBancariasAsync()
        {
            try
            {
                var contas = await BuscarTodosAsync();
                return contas.Where(c => c.TipoConta == AccountType.ContaCorrente || c.TipoConta == AccountType.ContaPoupanca);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar contas bancárias: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca contas por nome
        /// </summary>
        /// <param name="nome">Nome da conta</param>
        /// <returns>Lista de contas com o nome especificado</returns>
        public async Task<IEnumerable<ContaViewModel>> BuscarPorNomeAsync(string nome)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(nome))
                    throw new ArgumentException("Nome não pode ser nulo ou vazio", nameof(nome));

                var contas = await BuscarTodosAsync();
                return contas.Where(c => c.Nome.Contains(nome, StringComparison.OrdinalIgnoreCase));
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar contas por nome: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca contas ativas
        /// </summary>
        /// <returns>Lista de contas ativas</returns>
        public async Task<IEnumerable<ContaViewModel>> BuscarAtivasAsync()
        {
            try
            {
                var contas = await BuscarTodosAsync();
                return contas.Where(c => c.Ativa);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar contas ativas: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca contas com filtros opcionais
        /// </summary>
        /// <param name="search">Filtro de busca por nome ou apelido</param>
        /// <param name="ativa">Filtro por status ativo/inativo (opcional)</param>
        /// <returns>Lista de contas filtradas</returns>
        public async Task<IEnumerable<ContaViewModel>> BuscarComFiltrosAsync(string? search = null, bool? ativa = null)
        {
            try
            {
                var contas = await BuscarTodosAsync();

                // Aplica filtro de busca por nome ou apelido
                if (!string.IsNullOrWhiteSpace(search))
                {
                    contas = contas.Where(c =>
                        c.Nome.Contains(search, StringComparison.OrdinalIgnoreCase) ||
                        c.ApelidoConta.Contains(search, StringComparison.OrdinalIgnoreCase));
                }

                // Aplica filtro de status ativo/inativo
                if (ativa.HasValue)
                {
                    contas = contas.Where(c => c.Ativa == ativa.Value);
                }

                return contas;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar contas com filtros: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Calcula o saldo total de todas as contas ativas
        /// </summary>
        /// <returns>Saldo total</returns>
        public async Task<decimal> CalcularSaldoTotalAsync()
        {
            try
            {
                var contasAtivas = await BuscarAtivasAsync();
                return contasAtivas.Sum(c => c.Saldo);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao calcular saldo total: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Atualiza o saldo de uma conta
        /// </summary>
        /// <param name="idConta">ID da conta</param>
        /// <param name="novoSaldo">Novo saldo</param>
        /// <returns>True se a atualização foi bem-sucedida</returns>
        public async Task<bool> AtualizarSaldoAsync(string idConta, decimal novoSaldo)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(idConta))
                    throw new ArgumentException("ID da conta não pode ser nulo ou vazio", nameof(idConta));

                var conta = await BuscarPorIdAsync(idConta);
                if (conta == null)
                    throw new ArgumentException("Conta não encontrada", nameof(idConta));

                conta.Saldo = novoSaldo;
                conta.DtaAlteracao = DateTime.UtcNow;

                var contaAtualizada = await EditarAsync(conta);
                return contaAtualizada != null;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao atualizar saldo da conta: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Atualiza uma conta existente
        /// </summary>
        /// <param name="conta">Dados atualizados da conta</param>
        /// <returns>Conta atualizada</returns>
        public async Task<ContaViewModel?> AtualizarAsync(ContaViewModel conta)
        {
            try
            {
                if (conta == null)
                    throw new ArgumentNullException(nameof(conta));

                conta.DtaAlteracao = DateTime.UtcNow;
                return await EditarAsync(conta);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao atualizar conta: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Inativa uma conta
        /// </summary>
        /// <param name="idConta">ID da conta</param>
        /// <returns>True se a inativação foi bem-sucedida</returns>
        public async Task<bool> InativarAsync(string idConta)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(idConta))
                    throw new ArgumentException("ID da conta não pode ser nulo ou vazio", nameof(idConta));

                var conta = await BuscarPorIdAsync(idConta);
                if (conta == null)
                    return false;

                conta.Ativa = false;
                conta.DtaAlteracao = DateTime.UtcNow;

                var contaAtualizada = await EditarAsync(conta);
                return contaAtualizada != null;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao inativar conta: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Atualiza o limite de um cartão de crédito
        /// </summary>
        /// <param name="idCartao">ID do cartão</param>
        /// <param name="novoLimite">Novo limite total</param>
        /// <returns>True se a atualização foi bem-sucedida</returns>
        public async Task<bool> AtualizarLimiteCartaoAsync(string idCartao, decimal novoLimite)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(idCartao))
                    throw new ArgumentException("ID do cartão não pode ser nulo ou vazio", nameof(idCartao));

                var conta = await BuscarPorIdAsync(idCartao);
                if (conta == null || !conta.IsCartaoCredito)
                    throw new ArgumentException("Cartão não encontrado", nameof(idCartao));

                conta.LimiteTotal = novoLimite;
                conta.DtaAlteracao = DateTime.UtcNow;

                var contaAtualizada = await EditarAsync(conta);
                return contaAtualizada != null;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao atualizar limite do cartão: {ex.Message}", ex);
            }
        }
    }
}
