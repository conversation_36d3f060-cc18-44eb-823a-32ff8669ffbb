using Shared.Enums;
using Shared.ViewModels.Base;
using System;
using System.ComponentModel.DataAnnotations;

namespace Shared.ViewModels.Client
{
    /// <summary>
    /// ViewModel para transferência recorrente
    /// </summary>
    public class TransferenciaRecorrenteViewModel : BaseViewModel
    {
        /// <summary>
        /// Nome da transferência recorrente
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Nome { get; set; } = "";

        /// <summary>
        /// Descrição da transferência recorrente
        /// </summary>
        [StringLength(500)]
        public string Descricao { get; set; } = "";

        #region Dados da Transação Modelo
        /// <summary>
        /// Valor da transferência
        /// </summary>
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "O valor deve ser maior que zero")]
        public decimal Valor { get; set; } = 0;

        /// <summary>
        /// ID da conta de origem
        /// </summary>
        [Required]
        public string IdContaOrigem { get; set; } = "";

        /// <summary>
        /// Nome da conta de origem (para exibição)
        /// </summary>
        public string? NomeContaOrigem { get; set; }

        /// <summary>
        /// ID da conta de destino
        /// </summary>
        public string? IdContaDestino { get; set; }

        /// <summary>
        /// Nome da conta de destino (para exibição)
        /// </summary>
        public string? NomeContaDestino { get; set; }

        /// <summary>
        /// ID da categoria
        /// </summary>
        [Required]
        public string IdCategoria { get; set; } = "";

        /// <summary>
        /// Nome da categoria (para exibição)
        /// </summary>
        public string? NomeCategoria { get; set; }

        /// <summary>
        /// Cor da categoria (para exibição)
        /// </summary>
        public string? CorCategoria { get; set; }

        /// <summary>
        /// Tipo de transação
        /// </summary>
        [Required]
        public string Tipo { get; set; } = "DESPESA";

        /// <summary>
        /// Forma de pagamento
        /// </summary>
        public string FormaPagamento { get; set; } = "";

        /// <summary>
        /// Observações padrão
        /// </summary>
        public string? ObservacoesModelo { get; set; }
        #endregion

        #region Configuração de Recorrência
        /// <summary>
        /// Tipo de recorrência
        /// </summary>
        public RecorrenciaType TipoRecorrencia { get; set; } = RecorrenciaType.Mensal;

        /// <summary>
        /// Descrição do tipo de recorrência
        /// </summary>
        public string TipoRecorrenciaDescricao => TipoRecorrencia.GetDescription();

        /// <summary>
        /// Dias personalizados
        /// </summary>
        public int? DiasPersonalizados { get; set; }

        /// <summary>
        /// Dia do mês para execução
        /// </summary>
        [Range(1, 31)]
        public int? DiaExecucao { get; set; }

        /// <summary>
        /// Data de início
        /// </summary>
        [Required]
        public DateTime DataInicio { get; set; } = DateTime.Now;

        /// <summary>
        /// Data de fim
        /// </summary>
        public DateTime? DataFim { get; set; }

        /// <summary>
        /// Próxima execução
        /// </summary>
        public DateTime ProximaExecucao { get; set; }

        /// <summary>
        /// Última execução
        /// </summary>
        public DateTime? UltimaExecucao { get; set; }
        #endregion

        #region Status e Controle
        /// <summary>
        /// Status da recorrência
        /// </summary>
        public RecorrenciaStatus Status { get; set; } = RecorrenciaStatus.Ativa;

        /// <summary>
        /// Descrição do status
        /// </summary>
        public string StatusDescricao => Status.GetDescription();

        /// <summary>
        /// Número máximo de execuções
        /// </summary>
        public int? MaximoExecucoes { get; set; }

        /// <summary>
        /// Execuções realizadas
        /// </summary>
        public int ExecucoesRealizadas { get; set; } = 0;

        /// <summary>
        /// Execuções falhadas
        /// </summary>
        public int ExecucoesFalhadas { get; set; } = 0;

        /// <summary>
        /// Última tentativa
        /// </summary>
        public DateTime? UltimaTentativa { get; set; }

        /// <summary>
        /// Motivo da última falha
        /// </summary>
        public string? MotivoUltimaFalha { get; set; }
        #endregion

        #region Propriedades Calculadas
        /// <summary>
        /// Verifica se está ativa
        /// </summary>
        public bool IsAtiva => Status == RecorrenciaStatus.Ativa;

        /// <summary>
        /// Verifica se deve executar hoje
        /// </summary>
        public bool DeveExecutarHoje => IsAtiva && ProximaExecucao.Date <= DateTime.Now.Date;

        /// <summary>
        /// Verifica se atingiu limite de execuções
        /// </summary>
        public bool AtingiuLimiteExecucoes => MaximoExecucoes.HasValue && ExecucoesRealizadas >= MaximoExecucoes.Value;

        /// <summary>
        /// Verifica se está vencida
        /// </summary>
        public bool IsVencida => DataFim.HasValue && DateTime.Now > DataFim.Value;

        /// <summary>
        /// Verifica se pode executar
        /// </summary>
        public bool PodeExecutar => IsAtiva && !AtingiuLimiteExecucoes && !IsVencida;

        /// <summary>
        /// Verifica se é uma receita
        /// </summary>
        public bool IsReceita => Tipo.ToUpper() == "RECEITA";

        /// <summary>
        /// Verifica se é uma despesa
        /// </summary>
        public bool IsDespesa => Tipo.ToUpper() == "DESPESA";

        /// <summary>
        /// Verifica se é transferência entre contas
        /// </summary>
        public bool IsTransferencia => !string.IsNullOrEmpty(IdContaDestino);

        /// <summary>
        /// Valor formatado para exibição
        /// </summary>
        public string ValorFormatado => $"R$ {Valor:N2}";

        /// <summary>
        /// Próxima execução formatada
        /// </summary>
        public string ProximaExecucaoFormatada => ProximaExecucao.ToString("dd/MM/yyyy");

        /// <summary>
        /// Dias até próxima execução
        /// </summary>
        public int DiasAteProximaExecucao => Math.Max((ProximaExecucao.Date - DateTime.Now.Date).Days, 0);

        /// <summary>
        /// Taxa de sucesso das execuções
        /// </summary>
        public decimal TaxaSucesso
        {
            get
            {
                var totalTentativas = ExecucoesRealizadas + ExecucoesFalhadas;
                return totalTentativas > 0 ? (decimal)ExecucoesRealizadas / totalTentativas * 100 : 100;
            }
        }

        /// <summary>
        /// Descrição do progresso
        /// </summary>
        public string DescricaoProgresso
        {
            get
            {
                if (MaximoExecucoes.HasValue)
                {
                    return $"{ExecucoesRealizadas}/{MaximoExecucoes.Value} execuções";
                }
                return $"{ExecucoesRealizadas} execuções realizadas";
            }
        }
        #endregion
    }
}
