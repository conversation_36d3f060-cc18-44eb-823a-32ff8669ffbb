import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../core/models/user_model.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/constants.dart';
import '../../../core/utils/navigation_helper.dart';
import '../../../features/auth/data/auth_service.dart';

/// Tela de perfil do usuário
class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final UserModel? user = AuthService.getCurrentUser();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Perfil'),
        backgroundColor: AppTheme.navyBlueColor,
        foregroundColor: AppTheme.snowWhiteColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppConstants.dashboardRoute),
        ),
      ),
      backgroundColor: AppTheme.navyBlueColor,
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              const SizedBox(height: 20),

              if (user != null) ...[
                // Avatar do usuário
                Stack(
                  alignment: Alignment.bottomRight,
                  children: [
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(color: AppTheme.goldColor, width: 2),
                      ),
                      child: Center(
                        child: Text(
                          user.nome.isNotEmpty
                              ? user.nome[0].toUpperCase()
                              : 'U',
                          style: const TextStyle(
                            fontSize: 60,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.goldColor,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: const BoxDecoration(
                        color: AppTheme.goldColor,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.camera_alt,
                        color: AppTheme.navyBlueColor,
                        size: 20,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Nome do usuário
                Text(
                  user.nome,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),

                const SizedBox(height: 4),

                // Email do usuário
                Text(
                  user.email,
                  style: const TextStyle(fontSize: 16, color: Colors.white70),
                ),

                const SizedBox(height: 8),

                // Perfil financeiro
                Text(
                  "Perfil financeiro",
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.goldColor,
                  ),
                ),

                const SizedBox(height: 40),
              ] else ...[
                // Mensagem de usuário não encontrado
                const Center(
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 30),
                    child: Text(
                      'Usuário não encontrado',
                      style: TextStyle(fontSize: 18, color: Colors.white),
                    ),
                  ),
                ),

                const SizedBox(height: 20),
              ],

              // Opções de menu - sempre mostradas
              _buildMenuOption(
                context,
                icon: Icons.person_outline,
                title: 'Editar Perfil',
                onTap: () => context.go(AppConstants.editProfileRoute),
                iconColor: AppTheme.goldColor,
              ),

              const SizedBox(height: 16),

              _buildMenuOption(
                context,
                icon: Icons.settings_outlined,
                title: 'Configurações',
                onTap: () => context.go(AppConstants.settingsRoute),
                iconColor: AppTheme.goldColor,
              ),

              const SizedBox(height: 16),

              _buildMenuOption(
                context,
                icon: Icons.shield_outlined,
                title: 'Segurança',
                onTap: () => context.go(AppConstants.securityRoute),
                iconColor: AppTheme.goldColor,
              ),

              const SizedBox(height: 16),

              // Botão de logout - sempre mostrado
              _buildMenuOption(
                context,
                icon: Icons.logout,
                title: 'Sair',
                onTap: () => _showLogoutDialog(context),
                iconColor: Colors.red,
                textColor: Colors.red,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Constrói uma opção de menu
  Widget _buildMenuOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color iconColor = AppTheme.goldColor,
    Color? textColor,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.navyBlueColor.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.goldColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: ListTile(
        leading: Icon(icon, color: iconColor),
        title: Text(
          title,
          style: TextStyle(
            color: textColor ?? Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
        trailing: const Icon(Icons.chevron_right, color: Colors.white54),
        onTap: onTap,
      ),
    );
  }

  /// Exibe diálogo de confirmação de logout
  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: AppTheme.navyBlueColor,
          title: const Text('Sair', style: TextStyle(color: Colors.white)),
          content: const Text(
            'Tem certeza que deseja sair?',
            style: TextStyle(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () => NavigationHelper.safeCloseDialog(context),
              child: const Text(
                'Cancelar',
                style: TextStyle(color: Colors.white70),
              ),
            ),
            TextButton(
              onPressed: () => _performLogout(context),
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Sair'),
            ),
          ],
        );
      },
    );
  }

  /// Realiza o logout e redireciona para o login
  Future<void> _performLogout(BuildContext context) async {
    NavigationHelper.safeCloseDialog(context); // Fecha o diálogo

    try {
      await AuthService.logout();

      if (context.mounted) {
        // Redireciona para a tela de login usando go_router
        context.go(AppConstants.loginRoute);
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao fazer logout: ${e.toString()}'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }
}
