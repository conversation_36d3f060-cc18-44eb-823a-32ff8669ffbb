using ServiceClient.Interfaces.Generic;
using Shared.Entities.Client;
using Shared.ViewModels.Client;
using System.Linq.Expressions;

namespace ServiceClient.Interfaces
{
    /// <summary>
    /// Interface específica para operações com Transferência/Transação financeira
    /// </summary>
    public interface ITransferenciaService : IGenericClientService<TransferenciaViewModel, Transferencia>
    {
        /// <summary>
        /// Busca transferências por conta de origem
        /// </summary>
        /// <param name="idContaOrigem">ID da conta de origem</param>
        /// <returns>Lista de transferências da conta</returns>
        Task<IEnumerable<TransferenciaViewModel>> BuscarPorContaOrigemAsync(string idContaOrigem);

        /// <summary>
        /// Busca transferências por conta de destino
        /// </summary>
        /// <param name="idContaDestino">ID da conta de destino</param>
        /// <returns>Lista de transferências para a conta</returns>
        Task<IEnumerable<TransferenciaViewModel>> BuscarPorContaDestinoAsync(string idContaDestino);

        /// <summary>
        /// Busca transferências por categoria
        /// </summary>
        /// <param name="idCategoria">ID da categoria</param>
        /// <returns>Lista de transferências da categoria</returns>
        Task<IEnumerable<TransferenciaViewModel>> BuscarPorCategoriaAsync(string idCategoria);

        /// <summary>
        /// Busca transferências por período
        /// </summary>
        /// <param name="dataInicio">Data de início</param>
        /// <param name="dataFim">Data de fim</param>
        /// <returns>Lista de transferências no período</returns>
        Task<IEnumerable<TransferenciaViewModel>> BuscarPorPeriodoAsync(DateTime dataInicio, DateTime dataFim);

        /// <summary>
        /// Busca transferências por status
        /// </summary>
        /// <param name="status">Status da transferência</param>
        /// <returns>Lista de transferências com o status especificado</returns>
        Task<IEnumerable<TransferenciaViewModel>> BuscarPorStatusAsync(int status);

        /// <summary>
        /// Calcula o total de transferências por período
        /// </summary>
        /// <param name="dataInicio">Data de início</param>
        /// <param name="dataFim">Data de fim</param>
        /// <param name="tipo">Tipo de transferência (opcional)</param>
        /// <returns>Valor total das transferências</returns>
        Task<decimal> CalcularTotalPorPeriodoAsync(DateTime dataInicio, DateTime dataFim, string? tipo = null);

        /// <summary>
        /// Busca transferências recentes
        /// </summary>
        /// <param name="limite">Número máximo de transferências</param>
        /// <returns>Lista das transferências mais recentes</returns>
        Task<IEnumerable<TransferenciaViewModel>> BuscarRecentesAsync(int limite = 10);

        /// <summary>
        /// Cancela uma transferência
        /// </summary>
        /// <param name="id">ID da transferência</param>
        /// <param name="motivo">Motivo do cancelamento</param>
        /// <returns>True se o cancelamento foi bem-sucedido</returns>
        Task<bool> CancelarAsync(string id, string? motivo = null);

        /// <summary>
        /// Confirma uma transferência pendente
        /// </summary>
        /// <param name="id">ID da transferência</param>
        /// <returns>True se a confirmação foi bem-sucedida</returns>
        Task<bool> ConfirmarAsync(string id);

        /// <summary>
        /// Remove uma transferência (soft delete)
        /// </summary>
        /// <param name="id">ID da transferência</param>
        /// <returns>True se a remoção foi bem-sucedida</returns>
        Task<bool> RemoverAsync(string id);

        /// <summary>
        /// Busca transferências por múltiplas contas
        /// </summary>
        /// <param name="idsContas">Lista de IDs das contas</param>
        /// <returns>Lista de transferências das contas especificadas</returns>
        Task<IEnumerable<TransferenciaViewModel>> BuscarPorContasAsync(IEnumerable<string> idsContas);

        /// <summary>
        /// Busca estatísticas de transferências por período
        /// </summary>
        /// <param name="dataInicio">Data de início</param>
        /// <param name="dataFim">Data de fim</param>
        /// <returns>Estatísticas das transferências</returns>
        Task<object> BuscarEstatisticasPorPeriodoAsync(DateTime dataInicio, DateTime dataFim);
    }
}
