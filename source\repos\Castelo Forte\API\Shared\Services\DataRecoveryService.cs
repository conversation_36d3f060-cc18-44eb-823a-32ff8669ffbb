using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Shared.Helpers.Encripty;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Shared.Services
{
    /// <summary>
    /// Serviço para recuperação de dados com tratamento de falhas de criptografia
    /// </summary>
    public class DataRecoveryService
    {
        private readonly ILogger<DataRecoveryService> _logger;
        private readonly IEncryptionService _encryptionService;

        public DataRecoveryService(ILogger<DataRecoveryService> logger, IEncryptionService encryptionService)
        {
            _logger = logger;
            _encryptionService = encryptionService;
        }

        /// <summary>
        /// Busca todos os registros de uma coleção com recuperação individual de falhas
        /// </summary>
        /// <typeparam name="T">Tipo da entidade</typeparam>
        /// <param name="collection">Coleção MongoDB</param>
        /// <param name="collectionName">Nome da coleção para logs</param>
        /// <returns>Lista de entidades válidas</returns>
        public async Task<List<T>> BuscarTodosComRecuperacaoAsync<T>(
            IMongoCollection<T> collection, 
            string collectionName) where T : class
        {
            var resultados = new List<T>();
            var registrosComErro = new List<string>();

            try
            {
                _logger.LogInformation("Iniciando busca com recuperação para coleção {CollectionName}", collectionName);

                // Primeiro, tenta buscar todos os registros de uma vez
                try
                {
                    var todosRegistros = await collection.Find(Builders<T>.Filter.Empty).ToListAsync();
                    _logger.LogInformation("Busca em lote bem-sucedida para {CollectionName}: {Count} registros", 
                        collectionName, todosRegistros.Count);
                    return todosRegistros;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning("Falha na busca em lote para {CollectionName}: {Error}. Tentando recuperação individual.", 
                        collectionName, ex.Message);
                }

                // Se a busca em lote falhar, busca registro por registro
                var cursor = await collection.Find(Builders<T>.Filter.Empty).ToCursorAsync();
                
                await cursor.ForEachAsync(documento =>
                {
                    try
                    {
                        // Tenta processar o documento
                        if (documento != null)
                        {
                            resultados.Add(documento);
                        }
                    }
                    catch (Exception ex)
                    {
                        var documentoId = TentarObterIdDocumento(documento);
                        registrosComErro.Add(documentoId);
                        
                        _logger.LogWarning("Erro ao processar documento {DocumentoId} da coleção {CollectionName}: {Error}", 
                            documentoId, collectionName, ex.Message);
                    }
                });

                _logger.LogInformation("Recuperação concluída para {CollectionName}: {ValidCount} válidos, {ErrorCount} com erro", 
                    collectionName, resultados.Count, registrosComErro.Count);

                if (registrosComErro.Any())
                {
                    _logger.LogWarning("Registros com erro de criptografia em {CollectionName}: {ErrorIds}", 
                        collectionName, string.Join(", ", registrosComErro));
                }

                return resultados;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro crítico na recuperação de dados para {CollectionName}", collectionName);
                return new List<T>();
            }
        }

        /// <summary>
        /// Valida e tenta reparar uma connection string criptografada
        /// </summary>
        /// <param name="connectionStringCriptografada">Connection string criptografada</param>
        /// <returns>Connection string descriptografada ou null se inválida</returns>
        public string? ValidarERepararConnectionString(string connectionStringCriptografada)
        {
            if (string.IsNullOrEmpty(connectionStringCriptografada))
            {
                _logger.LogWarning("Connection string vazia fornecida");
                return null;
            }

            try
            {
                // Tenta descriptografar normalmente
                var connectionString = _encryptionService.Decrypt(connectionStringCriptografada);
                
                if (string.IsNullOrEmpty(connectionString))
                {
                    _logger.LogWarning("Connection string descriptografada está vazia");
                    return null;
                }

                _logger.LogDebug("Connection string descriptografada com sucesso");
                return connectionString;
            }
            catch (FormatException ex)
            {
                _logger.LogWarning("Connection string não está em formato Base64 válido: {Error}", ex.Message);
                
                // Verifica se pode ser uma connection string não criptografada (dados legados)
                if (connectionStringCriptografada.StartsWith("mongodb"))
                {
                    _logger.LogInformation("Detectada connection string não criptografada (dados legados)");
                    return connectionStringCriptografada;
                }
                
                return null;
            }
            catch (System.Security.Cryptography.CryptographicException ex)
            {
                _logger.LogError("Erro de criptografia ao descriptografar connection string: {Error}", ex.Message);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro inesperado ao processar connection string");
                return null;
            }
        }

        /// <summary>
        /// Tenta obter o ID de um documento para logging
        /// </summary>
        /// <typeparam name="T">Tipo do documento</typeparam>
        /// <param name="documento">Documento</param>
        /// <returns>ID do documento ou "unknown"</returns>
        private string TentarObterIdDocumento<T>(T documento)
        {
            try
            {
                if (documento == null) return "null";

                var tipo = documento.GetType();
                var propriedadeId = tipo.GetProperty("Id") ?? tipo.GetProperty("_id");
                
                if (propriedadeId != null)
                {
                    var valor = propriedadeId.GetValue(documento);
                    return valor?.ToString() ?? "unknown";
                }

                return "unknown";
            }
            catch
            {
                return "unknown";
            }
        }

        /// <summary>
        /// Diagnóstica problemas de criptografia em uma coleção
        /// </summary>
        /// <typeparam name="T">Tipo da entidade</typeparam>
        /// <param name="collection">Coleção MongoDB</param>
        /// <param name="collectionName">Nome da coleção</param>
        /// <returns>Relatório de diagnóstico</returns>
        public async Task<DiagnosticoCriptografiaResult> DiagnosticarCriptografiaAsync<T>(
            IMongoCollection<T> collection, 
            string collectionName) where T : class
        {
            var resultado = new DiagnosticoCriptografiaResult
            {
                NomeColecao = collectionName,
                DataDiagnostico = DateTime.UtcNow
            };

            try
            {
                _logger.LogInformation("Iniciando diagnóstico de criptografia para {CollectionName}", collectionName);

                var totalDocumentos = await collection.CountDocumentsAsync(Builders<T>.Filter.Empty);
                resultado.TotalDocumentos = (int)totalDocumentos;

                if (totalDocumentos == 0)
                {
                    resultado.Status = "Coleção vazia";
                    return resultado;
                }

                // Tenta buscar em lote primeiro
                try
                {
                    await collection.Find(Builders<T>.Filter.Empty).Limit(1).ToListAsync();
                    resultado.Status = "Criptografia OK";
                    resultado.DocumentosValidos = (int)totalDocumentos;
                }
                catch (Exception ex)
                {
                    resultado.Status = "Problemas detectados";
                    resultado.ErroDetalhado = ex.Message;

                    // Conta documentos válidos individualmente
                    var cursor = await collection.Find(Builders<T>.Filter.Empty).ToCursorAsync();
                    
                    await cursor.ForEachAsync(documento =>
                    {
                        try
                        {
                            if (documento != null)
                            {
                                resultado.DocumentosValidos++;
                            }
                        }
                        catch
                        {
                            resultado.DocumentosComErro++;
                        }
                    });
                }

                _logger.LogInformation("Diagnóstico concluído para {CollectionName}: {Valid}/{Total} documentos válidos", 
                    collectionName, resultado.DocumentosValidos, resultado.TotalDocumentos);

                return resultado;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro no diagnóstico de criptografia para {CollectionName}", collectionName);
                resultado.Status = "Erro no diagnóstico";
                resultado.ErroDetalhado = ex.Message;
                return resultado;
            }
        }
    }

    /// <summary>
    /// Resultado do diagnóstico de criptografia
    /// </summary>
    public class DiagnosticoCriptografiaResult
    {
        public string NomeColecao { get; set; } = string.Empty;
        public DateTime DataDiagnostico { get; set; }
        public string Status { get; set; } = string.Empty;
        public int TotalDocumentos { get; set; }
        public int DocumentosValidos { get; set; }
        public int DocumentosComErro { get; set; }
        public string? ErroDetalhado { get; set; }
    }
}
