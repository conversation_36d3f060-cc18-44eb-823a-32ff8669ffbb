import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/constants.dart';
import '../data/auth_service.dart';

/// Tela de Cadastro completa com layout profissional
class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nomeController = TextEditingController();
  final _emailController = TextEditingController();
  final _cpfController = TextEditingController();
  final _telefoneController = TextEditingController();
  final _dataNascimentoController = TextEditingController();
  // Mantemos o controller mas não exibimos o campo
  final _loginController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  DateTime? _selectedDate;
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _isLoading = false;
  bool _acceptTerms = false;

  @override
  void initState() {
    super.initState();
    // Adiciona listener para sincronizar o email com o login
    _emailController.addListener(_updateLoginFromEmail);
  }

  // Atualiza o login com o valor do email
  void _updateLoginFromEmail() {
    _loginController.text = _emailController.text;
  }

  @override
  void dispose() {
    // Remove o listener antes de dispose
    _emailController.removeListener(_updateLoginFromEmail);

    _nomeController.dispose();
    _emailController.dispose();
    _cpfController.dispose();
    _telefoneController.dispose();
    _dataNascimentoController.dispose();
    _loginController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _togglePasswordVisibility() {
    setState(() {
      _isPasswordVisible = !_isPasswordVisible;
    });
  }

  void _toggleConfirmPasswordVisibility() {
    setState(() {
      _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
    });
  }

  Future<void> _handleRegister() async {
    if (_formKey.currentState?.validate() ?? false) {
      if (!_acceptTerms) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Você precisa aceitar os termos de uso'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
        return;
      }

      setState(() {
        _isLoading = true;
      });

      try {
        // Implementação do registro
        await AuthService.register(
          nome: _nomeController.text,
          email: _emailController.text,
          cpf: _cpfController.text,
          telefone: _telefoneController.text,
          password: _passwordController.text,
          dataNascimento: _selectedDate,
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Cadastro realizado com sucesso!'),
              backgroundColor: AppTheme.successColor,
            ),
          );

          // Redireciona para a tela de login após o cadastro bem-sucedido
          if (context.mounted) {
            context.go(AppConstants.loginRoute);
          }
        }
      } catch (e) {
        if (mounted) {
          String errorMessage = 'Erro no cadastro';

          // Trata diferentes tipos de erro
          final errorString = e.toString();
          if (errorString.contains('CPF')) {
            errorMessage = 'CPF inválido. Verifique o formato.';
          } else if (errorString.contains('email')) {
            errorMessage = 'Email inválido ou já cadastrado.';
          } else if (errorString.contains('conectar')) {
            errorMessage = 'Erro de conexão. Verifique sua internet.';
          } else if (errorString.contains('Usuário já existe')) {
            errorMessage = 'Este email ou CPF já está cadastrado.';
          } else if (errorString.contains('Dados inválidos')) {
            errorMessage = 'Dados inválidos. Verifique os campos.';
          } else {
            errorMessage = 'Erro no cadastro: ${e.toString()}';
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorMessage),
              backgroundColor: AppTheme.errorColor,
              duration: const Duration(seconds: 5),
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  void _goToLogin() {
    // Navega para a tela de login usando go_router
    context.go(AppConstants.loginRoute);
  }

  /// Abre o seletor de data para a data de nascimento
  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      locale: const Locale(
        'pt',
        'BR',
      ), // Define o locale para português brasileiro
      initialDate: _selectedDate ?? DateTime(2000),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
      helpText:
          'Selecione sua data de nascimento', // Texto de ajuda em português
      cancelText: 'Cancelar', // Botão cancelar em português
      confirmText: 'Confirmar', // Botão confirmar em português
      fieldLabelText: 'Data de nascimento', // Label do campo em português
      fieldHintText: 'dd/mm/aaaa', // Hint em formato brasileiro
      errorFormatText: 'Formato inválido', // Mensagem de erro em português
      errorInvalidText:
          'Data inválida', // Mensagem de data inválida em português
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.dark(
              primary: AppTheme.goldColor,
              onPrimary: AppTheme.navyBlueColor,
              surface: AppTheme.navyBlueColor,
              onSurface: AppTheme.snowWhiteColor,
            ),
            dialogTheme: const DialogThemeData(
              backgroundColor: AppTheme.navyBlueColor,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        // Formata a data para exibição no formato brasileiro (DD/MM/YYYY)
        final formatter = DateFormat('dd/MM/yyyy', 'pt_BR');
        _dataNascimentoController.text = formatter.format(picked);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.navyBlueColor,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 40.0),
          child: Column(
            children: [
              const SizedBox(height: 20),

              // Logo
              _buildLogo(),

              const SizedBox(height: 40),

              // Formulário de cadastro
              _buildRegisterForm(),

              const SizedBox(height: 24),

              // Link voltar para login
              _buildLoginLink(),

              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }

  /// Constrói a logo do Castelo Forte
  Widget _buildLogo() {
    return Image.asset(
      'assets/images/logo.png',
      width: 200,
      height: 200,
      fit: BoxFit.contain,
    );
  }

  /// Constrói o formulário de cadastro
  Widget _buildRegisterForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Campo de nome
          _buildNomeField(),

          const SizedBox(height: 16),

          // Campo de email
          _buildEmailField(),

          const SizedBox(height: 16),

          // Campo de CPF
          _buildCpfField(),

          const SizedBox(height: 16),

          // Campo de telefone
          _buildTelefoneField(),

          const SizedBox(height: 16),

          // Campo de data de nascimento
          _buildDataNascimentoField(),

          const SizedBox(height: 16),

          // Campo de senha
          _buildPasswordField(),

          const SizedBox(height: 16),

          // Campo de confirmação de senha
          _buildConfirmPasswordField(),

          const SizedBox(height: 24),

          // Checkbox de termos
          _buildTermsCheckbox(),

          const SizedBox(height: 24),

          // Botão de cadastro
          _buildRegisterButton(),
        ],
      ),
    );
  }

  /// Constrói o campo de nome
  Widget _buildNomeField() {
    return TextFormField(
      controller: _nomeController,
      keyboardType: TextInputType.name,
      textInputAction: TextInputAction.next,
      style: const TextStyle(color: AppTheme.snowWhiteColor),
      decoration: InputDecoration(
        labelText: 'Nome completo',
        labelStyle: const TextStyle(color: AppTheme.snowWhiteColor),
        hintText: 'Digite seu nome completo',
        hintStyle: TextStyle(
          color: AppTheme.snowWhiteColor.withAlpha(179),
        ), // ~0.7 opacity
        filled: true,
        fillColor: Colors.transparent,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.goldColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.errorColor, width: 1),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Por favor, digite seu nome completo';
        }
        if (value.trim().length < 2) {
          return 'O nome deve ter pelo menos 2 caracteres';
        }
        if (value.trim().length > 100) {
          return 'O nome deve ter no máximo 100 caracteres';
        }
        // Verifica se contém números
        if (RegExp(r'[0-9]').hasMatch(value)) {
          return 'O nome não deve conter números';
        }
        // Verifica se contém símbolos (exceto espaços e acentos)
        if (RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(value)) {
          return 'O nome não deve conter símbolos';
        }
        return null;
      },
      // Formata o texto para capitalizar as primeiras letras
      inputFormatters: [
        TextInputFormatter.withFunction((oldValue, newValue) {
          // Remove números e símbolos
          final filteredText = newValue.text.replaceAll(
            RegExp(r'[0-9!@#$%^&*(),.?":{}|<>]'),
            '',
          );
          return TextEditingValue(
            text: filteredText,
            selection: TextSelection.collapsed(offset: filteredText.length),
          );
        }),
      ],
    );
  }

  /// Constrói o campo de email
  Widget _buildEmailField() {
    return TextFormField(
      controller: _emailController,
      keyboardType: TextInputType.emailAddress,
      textInputAction: TextInputAction.next,
      style: const TextStyle(color: AppTheme.snowWhiteColor),
      decoration: InputDecoration(
        labelText: 'Email',
        labelStyle: const TextStyle(color: AppTheme.snowWhiteColor),
        hintText: 'Digite seu email',
        hintStyle: TextStyle(
          color: AppTheme.snowWhiteColor.withAlpha(179),
        ), // ~0.7 opacity
        filled: true,
        fillColor: Colors.transparent,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.goldColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.errorColor, width: 1),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Por favor, digite seu email';
        }
        if (value.length > 100) {
          return 'O email deve ter no máximo 100 caracteres';
        }
        // Validação mais rigorosa de email
        final emailRegex = RegExp(
          r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
        );
        if (!emailRegex.hasMatch(value)) {
          return 'Por favor, digite um email válido';
        }
        return null;
      },
    );
  }

  /// Constrói o campo de CPF
  Widget _buildCpfField() {
    return TextFormField(
      controller: _cpfController,
      keyboardType: TextInputType.number,
      textInputAction: TextInputAction.next,
      style: const TextStyle(color: AppTheme.snowWhiteColor),
      decoration: InputDecoration(
        labelText: 'CPF',
        labelStyle: const TextStyle(color: AppTheme.snowWhiteColor),
        hintText: '000.000.000-00',
        hintStyle: TextStyle(color: AppTheme.snowWhiteColor.withAlpha(179)),
        filled: true,
        fillColor: Colors.transparent,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.goldColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.errorColor, width: 1),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Por favor, digite seu CPF';
        }
        // Remove caracteres não numéricos para validação
        final numericValue = value.replaceAll(RegExp(r'[^0-9]'), '');

        // Verifica se tem exatamente 11 dígitos
        if (numericValue.length != 11) {
          return 'CPF deve ter 11 dígitos';
        }

        // Validação básica de CPF (verifica se não são todos iguais)
        if (RegExp(r'^(\d)\1{10}$').hasMatch(numericValue)) {
          return 'CPF inválido';
        }

        return null;
      },
      // Formata o CPF enquanto digita
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        TextInputFormatter.withFunction((oldValue, newValue) {
          final text = newValue.text;
          if (text.isEmpty) return newValue;

          // Formata o CPF
          String formatted = '';
          if (text.length <= 3) {
            formatted = text;
          } else if (text.length <= 6) {
            formatted = '${text.substring(0, 3)}.${text.substring(3)}';
          } else if (text.length <= 9) {
            formatted =
                '${text.substring(0, 3)}.${text.substring(3, 6)}.${text.substring(6)}';
          } else if (text.length <= 11) {
            formatted =
                '${text.substring(0, 3)}.${text.substring(3, 6)}.${text.substring(6, 9)}-${text.substring(9)}';
          } else {
            // Limita a 11 dígitos
            formatted =
                '${text.substring(0, 3)}.${text.substring(3, 6)}.${text.substring(6, 9)}-${text.substring(9, 11)}';
          }

          return TextEditingValue(
            text: formatted,
            selection: TextSelection.collapsed(offset: formatted.length),
          );
        }),
      ],
    );
  }

  /// Constrói o campo de telefone
  Widget _buildTelefoneField() {
    return TextFormField(
      controller: _telefoneController,
      keyboardType: TextInputType.phone,
      textInputAction: TextInputAction.next,
      style: const TextStyle(color: AppTheme.snowWhiteColor),
      decoration: InputDecoration(
        labelText: 'Telefone',
        labelStyle: const TextStyle(color: AppTheme.snowWhiteColor),
        hintText: '(00) 00000-0000',
        hintStyle: TextStyle(
          color: AppTheme.snowWhiteColor.withAlpha(179),
        ), // ~0.7 opacity
        filled: true,
        fillColor: Colors.transparent,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.goldColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.errorColor, width: 1),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Por favor, digite seu telefone';
        }
        // Remove caracteres não numéricos para validação
        final numericValue = value.replaceAll(RegExp(r'[^0-9]'), '');

        // Verifica se tem entre 10 e 11 dígitos (com ou sem DDD)
        if (numericValue.length < 10 || numericValue.length > 11) {
          return 'Telefone inválido. Use o formato (00) 00000-0000';
        }
        return null;
      },
      // Formata o telefone enquanto digita
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        TextInputFormatter.withFunction((oldValue, newValue) {
          final text = newValue.text;
          if (text.isEmpty) return newValue;

          // Formata o telefone
          String formatted = '';
          if (text.length <= 2) {
            // Apenas DDD
            formatted = '(${text.substring(0, text.length)}';
          } else if (text.length <= 7) {
            // DDD + primeiros dígitos
            formatted =
                '(${text.substring(0, 2)}) ${text.substring(2, text.length)}';
          } else if (text.length <= 11) {
            // Formato completo
            formatted =
                '(${text.substring(0, 2)}) ${text.substring(2, 7)}-${text.substring(7, text.length)}';
          } else {
            // Limita a 11 dígitos
            formatted =
                '(${text.substring(0, 2)}) ${text.substring(2, 7)}-${text.substring(7, 11)}';
          }

          return TextEditingValue(
            text: formatted,
            selection: TextSelection.collapsed(offset: formatted.length),
          );
        }),
      ],
    );
  }

  /// Constrói o campo de data de nascimento
  Widget _buildDataNascimentoField() {
    return TextFormField(
      controller: _dataNascimentoController,
      readOnly: true, // Impede a edição direta
      style: const TextStyle(color: AppTheme.snowWhiteColor),
      decoration: InputDecoration(
        labelText: 'Data de Nascimento',
        labelStyle: const TextStyle(color: AppTheme.snowWhiteColor),
        hintText: 'DD/MM/AAAA',
        hintStyle: TextStyle(
          color: AppTheme.snowWhiteColor.withAlpha(179),
        ), // ~0.7 opacity
        filled: true,
        fillColor: Colors.transparent,
        suffixIcon: IconButton(
          icon: const Icon(
            Icons.calendar_today,
            color: AppTheme.snowWhiteColor,
          ),
          onPressed: () => _selectDate(context),
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.goldColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.errorColor, width: 1),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Por favor, selecione sua data de nascimento';
        }
        if (_selectedDate == null) {
          return 'Por favor, selecione sua data de nascimento';
        }

        // Verifica se a pessoa tem pelo menos 18 anos
        final now = DateTime.now();
        final age = now.year - _selectedDate!.year;
        final hasHadBirthdayThisYear =
            now.month > _selectedDate!.month ||
            (now.month == _selectedDate!.month &&
                now.day >= _selectedDate!.day);

        final actualAge = hasHadBirthdayThisYear ? age : age - 1;

        if (actualAge < 18) {
          return 'Você deve ter pelo menos 18 anos';
        }

        return null;
      },
      onTap: () => _selectDate(context),
    );
  }

  /// Constrói o campo de senha
  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: !_isPasswordVisible,
      textInputAction: TextInputAction.next,
      style: const TextStyle(color: AppTheme.snowWhiteColor),
      decoration: InputDecoration(
        labelText: 'Senha',
        labelStyle: const TextStyle(color: AppTheme.snowWhiteColor),
        hintText: 'Digite sua senha',
        hintStyle: TextStyle(
          color: AppTheme.snowWhiteColor.withAlpha(179),
        ), // ~0.7 opacity
        filled: true,
        fillColor: Colors.transparent,
        suffixIcon: IconButton(
          icon: Icon(
            _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
            color: AppTheme.snowWhiteColor,
          ),
          onPressed: _togglePasswordVisibility,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.goldColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.errorColor, width: 1),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Por favor, digite sua senha';
        }
        if (value.length < 8) {
          return 'A senha deve ter pelo menos 8 caracteres';
        }
        if (value.length > 100) {
          return 'A senha deve ter no máximo 100 caracteres';
        }
        return null;
      },
    );
  }

  /// Constrói o campo de confirmação de senha
  Widget _buildConfirmPasswordField() {
    return TextFormField(
      controller: _confirmPasswordController,
      obscureText: !_isConfirmPasswordVisible,
      textInputAction: TextInputAction.done,
      style: const TextStyle(color: AppTheme.snowWhiteColor),
      decoration: InputDecoration(
        labelText: 'Confirmar Senha',
        labelStyle: const TextStyle(color: AppTheme.snowWhiteColor),
        hintText: 'Confirme sua senha',
        hintStyle: TextStyle(
          color: AppTheme.snowWhiteColor.withAlpha(179),
        ), // ~0.7 opacity
        filled: true,
        fillColor: Colors.transparent,
        suffixIcon: IconButton(
          icon: Icon(
            _isConfirmPasswordVisible ? Icons.visibility_off : Icons.visibility,
            color: AppTheme.snowWhiteColor,
          ),
          onPressed: _toggleConfirmPasswordVisibility,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.goldColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.errorColor, width: 1),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Por favor, confirme sua senha';
        }
        if (value != _passwordController.text) {
          return 'As senhas não coincidem';
        }
        return null;
      },
    );
  }

  /// Constrói o checkbox de termos
  Widget _buildTermsCheckbox() {
    return Row(
      children: [
        Checkbox(
          value: _acceptTerms,
          onChanged: (value) {
            setState(() {
              _acceptTerms = value ?? false;
            });
          },
          checkColor: AppTheme.navyBlueColor,
          fillColor: WidgetStateProperty.resolveWith<Color>((
            Set<WidgetState> states,
          ) {
            if (states.contains(WidgetState.disabled)) {
              return AppTheme.snowWhiteColor.withAlpha(128);
            }
            return AppTheme.goldColor;
          }),
        ),
        Expanded(
          child: Text(
            'Eu aceito os termos de uso e política de privacidade',
            style: TextStyle(color: AppTheme.snowWhiteColor, fontSize: 14),
          ),
        ),
      ],
    );
  }

  /// Constrói o botão de cadastro
  Widget _buildRegisterButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _handleRegister,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.goldColor,
          foregroundColor: AppTheme.navyBlueColor,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          elevation: 0,
        ),
        child: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppTheme.navyBlueColor,
                  ),
                ),
              )
            : const Text(
                'Cadastrar',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
      ),
    );
  }

  /// Constrói o link "Já tem uma conta? Faça login"
  Widget _buildLoginLink() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'Já tem uma conta? ',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: AppTheme.snowWhiteColor),
        ),
        TextButton(
          onPressed: _goToLogin,
          style: TextButton.styleFrom(
            padding: EdgeInsets.zero,
            minimumSize: Size.zero,
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          child: const Text(
            'Faça login',
            style: TextStyle(
              color: AppTheme.goldColor,
              fontWeight: FontWeight.bold,
              decoration: TextDecoration.underline,
              decorationColor: AppTheme.goldColor,
            ),
          ),
        ),
      ],
    );
  }
}
