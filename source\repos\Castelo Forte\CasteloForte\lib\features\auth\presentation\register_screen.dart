import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/constants.dart';
import '../../../core/utils/validators.dart';
import '../../../core/widgets/auth_widgets.dart';
import '../data/auth_service.dart';

/// Tela de Cadastro moderna e responsiva
class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nomeController = TextEditingController();
  final _emailController = TextEditingController();
  final _cpfController = TextEditingController();
  final _telefoneController = TextEditingController();
  final _dataNascimentoController = TextEditingController();
  final _loginController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  // Focus nodes para navegação entre campos
  final _nomeFocusNode = FocusNode();
  final _emailFocusNode = FocusNode();
  final _cpfFocusNode = FocusNode();
  final _telefoneFocusNode = FocusNode();
  final _dataNascimentoFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();
  final _confirmPasswordFocusNode = FocusNode();

  DateTime? _selectedDate;
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _isLoading = false;
  bool _acceptTerms = false;

  @override
  void initState() {
    super.initState();
    // Adiciona listener para sincronizar o email com o login
    _emailController.addListener(_updateLoginFromEmail);
  }

  // Atualiza o login com o valor do email
  void _updateLoginFromEmail() {
    _loginController.text = _emailController.text;
  }

  @override
  void dispose() {
    // Remove o listener antes de dispose
    _emailController.removeListener(_updateLoginFromEmail);

    // Dispose controllers
    _nomeController.dispose();
    _emailController.dispose();
    _cpfController.dispose();
    _telefoneController.dispose();
    _dataNascimentoController.dispose();
    _loginController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();

    // Dispose focus nodes
    _nomeFocusNode.dispose();
    _emailFocusNode.dispose();
    _cpfFocusNode.dispose();
    _telefoneFocusNode.dispose();
    _dataNascimentoFocusNode.dispose();
    _passwordFocusNode.dispose();
    _confirmPasswordFocusNode.dispose();

    super.dispose();
  }

  void _togglePasswordVisibility() {
    setState(() {
      _isPasswordVisible = !_isPasswordVisible;
    });
  }

  void _toggleConfirmPasswordVisibility() {
    setState(() {
      _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
    });
  }

  Future<void> _handleRegister() async {
    if (_formKey.currentState?.validate() ?? false) {
      if (!_acceptTerms) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Você precisa aceitar os termos de uso'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
        return;
      }

      setState(() {
        _isLoading = true;
      });

      try {
        // Implementação do registro
        await AuthService.register(
          nome: _nomeController.text,
          email: _emailController.text,
          cpf: _cpfController.text,
          telefone: _telefoneController.text,
          password: _passwordController.text,
          dataNascimento: _selectedDate,
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Cadastro realizado com sucesso!'),
              backgroundColor: AppTheme.successColor,
            ),
          );

          // Redireciona para a tela de login após o cadastro bem-sucedido
          if (context.mounted) {
            context.go(AppConstants.loginRoute);
          }
        }
      } catch (e) {
        if (mounted) {
          String errorMessage = 'Erro no cadastro';

          // Trata diferentes tipos de erro
          final errorString = e.toString();
          if (errorString.contains('CPF')) {
            errorMessage = 'CPF inválido. Verifique o formato.';
          } else if (errorString.contains('email')) {
            errorMessage = 'Email inválido ou já cadastrado.';
          } else if (errorString.contains('conectar')) {
            errorMessage = 'Erro de conexão. Verifique sua internet.';
          } else if (errorString.contains('Usuário já existe')) {
            errorMessage = 'Este email ou CPF já está cadastrado.';
          } else if (errorString.contains('Dados inválidos')) {
            errorMessage = 'Dados inválidos. Verifique os campos.';
          } else {
            errorMessage = 'Erro no cadastro: ${e.toString()}';
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorMessage),
              backgroundColor: AppTheme.errorColor,
              duration: const Duration(seconds: 5),
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  void _goToLogin() {
    // Navega para a tela de login usando go_router
    context.go(AppConstants.loginRoute);
  }

  /// Abre o seletor de data para a data de nascimento
  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      locale: const Locale(
        'pt',
        'BR',
      ), // Define o locale para português brasileiro
      initialDate: _selectedDate ?? DateTime(2000),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
      helpText:
          'Selecione sua data de nascimento', // Texto de ajuda em português
      cancelText: 'Cancelar', // Botão cancelar em português
      confirmText: 'Confirmar', // Botão confirmar em português
      fieldLabelText: 'Data de nascimento', // Label do campo em português
      fieldHintText: 'dd/mm/aaaa', // Hint em formato brasileiro
      errorFormatText: 'Formato inválido', // Mensagem de erro em português
      errorInvalidText:
          'Data inválida', // Mensagem de data inválida em português
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.dark(
              primary: AppTheme.goldColor,
              onPrimary: AppTheme.navyBlueColor,
              surface: AppTheme.navyBlueColor,
              onSurface: AppTheme.snowWhiteColor,
            ),
            dialogTheme: const DialogThemeData(
              backgroundColor: AppTheme.navyBlueColor,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        // Formata a data para exibição no formato brasileiro (DD/MM/YYYY)
        final formatter = DateFormat('dd/MM/yyyy', 'pt_BR');
        _dataNascimentoController.text = formatter.format(picked);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812), // iPhone X design size
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return Scaffold(
          backgroundColor: AppTheme.navyBlueColor,
          body: SafeArea(
            child: LayoutBuilder(
              builder: (context, constraints) {
                final isTablet = constraints.maxWidth > 600;
                final isDesktop = constraints.maxWidth > 1200;

                return Container(
                  width: double.infinity,
                  height: constraints.maxHeight,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        AppTheme.navyBlueColor,
                        AppTheme.navyBlueColor.withAlpha(230),
                      ],
                    ),
                  ),
                  child: SingleChildScrollView(
                    physics: const BouncingScrollPhysics(),
                    child: ResponsiveAuthContainer(
                      showBackground: isTablet || isDesktop,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          SizedBox(height: isDesktop ? 40.h : 20.h),

                          // Logo e título
                          _buildHeader(),

                          SizedBox(height: isDesktop ? 60.h : 40.h),

                          // Formulário de cadastro
                          _buildRegisterForm(),

                          SizedBox(height: 40.h),

                          // Link voltar para login
                          _buildLoginLink(),

                          SizedBox(height: 24.h),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  /// Constrói o cabeçalho com logo e título
  Widget _buildHeader() {
    return const ResponsiveAuthLogo(
      title: 'Crie sua conta',
      subtitle: 'Preencha os dados abaixo para começar',
    );
  }

  /// Constrói o formulário de cadastro
  Widget _buildRegisterForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Campo de nome
          ResponsiveAuthField(
            controller: _nomeController,
            focusNode: _nomeFocusNode,
            labelText: 'Nome completo',
            hintText: 'Digite seu nome completo',
            keyboardType: TextInputType.name,
            textInputAction: TextInputAction.next,
            validator: Validators.validateName,
            inputFormatters: [TextFormatters.nameFormatter],
          ),

          SizedBox(height: 16.h),

          // Campo de email
          ResponsiveAuthField(
            controller: _emailController,
            focusNode: _emailFocusNode,
            labelText: 'Email',
            hintText: 'Digite seu email',
            keyboardType: TextInputType.emailAddress,
            textInputAction: TextInputAction.next,
            validator: Validators.validateEmail,
          ),

          SizedBox(height: 16.h),

          // Campo de CPF
          ResponsiveAuthField(
            controller: _cpfController,
            focusNode: _cpfFocusNode,
            labelText: 'CPF',
            hintText: 'Digite seu CPF',
            keyboardType: TextInputType.number,
            textInputAction: TextInputAction.next,
            validator: Validators.validateCpf,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              TextFormatters.cpfFormatter,
            ],
          ),

          SizedBox(height: 16.h),

          // Campo de telefone
          ResponsiveAuthField(
            controller: _telefoneController,
            focusNode: _telefoneFocusNode,
            labelText: 'Telefone',
            hintText: 'Digite seu telefone',
            keyboardType: TextInputType.phone,
            textInputAction: TextInputAction.next,
            validator: Validators.validatePhone,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              TextFormatters.phoneFormatter,
            ],
          ),

          SizedBox(height: 16.h),

          // Campo de data de nascimento
          _buildDataNascimentoField(),

          SizedBox(height: 16.h),

          // Campo de senha
          ResponsiveAuthField(
            controller: _passwordController,
            focusNode: _passwordFocusNode,
            labelText: 'Senha',
            hintText: 'Digite sua senha',
            obscureText: !_isPasswordVisible,
            textInputAction: TextInputAction.next,
            validator: (value) => Validators.validatePassword(
              value,
              minLength: AppConstants.minPasswordLength,
            ),
            suffixIcon: IconButton(
              icon: Icon(
                _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                color: AppTheme.snowWhiteColor,
              ),
              onPressed: _togglePasswordVisibility,
            ),
          ),

          SizedBox(height: 16.h),

          // Campo de confirmação de senha
          ResponsiveAuthField(
            controller: _confirmPasswordController,
            focusNode: _confirmPasswordFocusNode,
            labelText: 'Confirmar senha',
            hintText: 'Digite novamente sua senha',
            obscureText: !_isConfirmPasswordVisible,
            textInputAction: TextInputAction.done,
            validator: (value) => Validators.validatePasswordConfirmation(
              value,
              _passwordController.text,
            ),
            suffixIcon: IconButton(
              icon: Icon(
                _isConfirmPasswordVisible
                    ? Icons.visibility_off
                    : Icons.visibility,
                color: AppTheme.snowWhiteColor,
              ),
              onPressed: _toggleConfirmPasswordVisibility,
            ),
          ),

          SizedBox(height: 24.h),

          // Checkbox de termos
          ResponsiveAuthCheckbox(
            value: _acceptTerms,
            onChanged: (value) {
              setState(() {
                _acceptTerms = value ?? false;
              });
            },
            text: 'Aceito os termos de uso e política de privacidade',
          ),

          SizedBox(height: 24.h),

          // Botão de cadastro
          ResponsiveAuthButton(
            text: 'Criar conta',
            onPressed: _handleRegister,
            isLoading: _isLoading,
          ),
        ],
      ),
    );
  }

  /// Constrói o campo de nome
  Widget _buildNomeField() {
    return TextFormField(
      controller: _nomeController,
      keyboardType: TextInputType.name,
      textInputAction: TextInputAction.next,
      style: const TextStyle(color: AppTheme.snowWhiteColor),
      decoration: InputDecoration(
        labelText: 'Nome completo',
        labelStyle: const TextStyle(color: AppTheme.snowWhiteColor),
        hintText: 'Digite seu nome completo',
        hintStyle: TextStyle(
          color: AppTheme.snowWhiteColor.withAlpha(179),
        ), // ~0.7 opacity
        filled: true,
        fillColor: Colors.transparent,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.goldColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.errorColor, width: 1),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Por favor, digite seu nome completo';
        }
        if (value.trim().length < 2) {
          return 'O nome deve ter pelo menos 2 caracteres';
        }
        if (value.trim().length > 100) {
          return 'O nome deve ter no máximo 100 caracteres';
        }
        // Verifica se contém números
        if (RegExp(r'[0-9]').hasMatch(value)) {
          return 'O nome não deve conter números';
        }
        // Verifica se contém símbolos (exceto espaços e acentos)
        if (RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(value)) {
          return 'O nome não deve conter símbolos';
        }
        return null;
      },
      // Formata o texto para capitalizar as primeiras letras
      inputFormatters: [
        TextInputFormatter.withFunction((oldValue, newValue) {
          // Remove números e símbolos
          final filteredText = newValue.text.replaceAll(
            RegExp(r'[0-9!@#$%^&*(),.?":{}|<>]'),
            '',
          );
          return TextEditingValue(
            text: filteredText,
            selection: TextSelection.collapsed(offset: filteredText.length),
          );
        }),
      ],
    );
  }

  /// Constrói o campo de email
  Widget _buildEmailField() {
    return TextFormField(
      controller: _emailController,
      keyboardType: TextInputType.emailAddress,
      textInputAction: TextInputAction.next,
      style: const TextStyle(color: AppTheme.snowWhiteColor),
      decoration: InputDecoration(
        labelText: 'Email',
        labelStyle: const TextStyle(color: AppTheme.snowWhiteColor),
        hintText: 'Digite seu email',
        hintStyle: TextStyle(
          color: AppTheme.snowWhiteColor.withAlpha(179),
        ), // ~0.7 opacity
        filled: true,
        fillColor: Colors.transparent,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.goldColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.errorColor, width: 1),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Por favor, digite seu email';
        }
        if (value.length > 100) {
          return 'O email deve ter no máximo 100 caracteres';
        }
        // Validação mais rigorosa de email
        final emailRegex = RegExp(
          r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
        );
        if (!emailRegex.hasMatch(value)) {
          return 'Por favor, digite um email válido';
        }
        return null;
      },
    );
  }

  /// Constrói o campo de CPF
  Widget _buildCpfField() {
    return TextFormField(
      controller: _cpfController,
      keyboardType: TextInputType.number,
      textInputAction: TextInputAction.next,
      style: const TextStyle(color: AppTheme.snowWhiteColor),
      decoration: InputDecoration(
        labelText: 'CPF',
        labelStyle: const TextStyle(color: AppTheme.snowWhiteColor),
        hintText: '000.000.000-00',
        hintStyle: TextStyle(color: AppTheme.snowWhiteColor.withAlpha(179)),
        filled: true,
        fillColor: Colors.transparent,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.goldColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.errorColor, width: 1),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Por favor, digite seu CPF';
        }
        // Remove caracteres não numéricos para validação
        final numericValue = value.replaceAll(RegExp(r'[^0-9]'), '');

        // Verifica se tem exatamente 11 dígitos
        if (numericValue.length != 11) {
          return 'CPF deve ter 11 dígitos';
        }

        // Validação básica de CPF (verifica se não são todos iguais)
        if (RegExp(r'^(\d)\1{10}$').hasMatch(numericValue)) {
          return 'CPF inválido';
        }

        return null;
      },
      // Formata o CPF enquanto digita
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        TextInputFormatter.withFunction((oldValue, newValue) {
          final text = newValue.text;
          if (text.isEmpty) return newValue;

          // Formata o CPF
          String formatted = '';
          if (text.length <= 3) {
            formatted = text;
          } else if (text.length <= 6) {
            formatted = '${text.substring(0, 3)}.${text.substring(3)}';
          } else if (text.length <= 9) {
            formatted =
                '${text.substring(0, 3)}.${text.substring(3, 6)}.${text.substring(6)}';
          } else if (text.length <= 11) {
            formatted =
                '${text.substring(0, 3)}.${text.substring(3, 6)}.${text.substring(6, 9)}-${text.substring(9)}';
          } else {
            // Limita a 11 dígitos
            formatted =
                '${text.substring(0, 3)}.${text.substring(3, 6)}.${text.substring(6, 9)}-${text.substring(9, 11)}';
          }

          return TextEditingValue(
            text: formatted,
            selection: TextSelection.collapsed(offset: formatted.length),
          );
        }),
      ],
    );
  }

  /// Constrói o campo de telefone
  Widget _buildTelefoneField() {
    return TextFormField(
      controller: _telefoneController,
      keyboardType: TextInputType.phone,
      textInputAction: TextInputAction.next,
      style: const TextStyle(color: AppTheme.snowWhiteColor),
      decoration: InputDecoration(
        labelText: 'Telefone',
        labelStyle: const TextStyle(color: AppTheme.snowWhiteColor),
        hintText: '(00) 00000-0000',
        hintStyle: TextStyle(
          color: AppTheme.snowWhiteColor.withAlpha(179),
        ), // ~0.7 opacity
        filled: true,
        fillColor: Colors.transparent,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.goldColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.errorColor, width: 1),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Por favor, digite seu telefone';
        }
        // Remove caracteres não numéricos para validação
        final numericValue = value.replaceAll(RegExp(r'[^0-9]'), '');

        // Verifica se tem entre 10 e 11 dígitos (com ou sem DDD)
        if (numericValue.length < 10 || numericValue.length > 11) {
          return 'Telefone inválido. Use o formato (00) 00000-0000';
        }
        return null;
      },
      // Formata o telefone enquanto digita
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        TextInputFormatter.withFunction((oldValue, newValue) {
          final text = newValue.text;
          if (text.isEmpty) return newValue;

          // Formata o telefone
          String formatted = '';
          if (text.length <= 2) {
            // Apenas DDD
            formatted = '(${text.substring(0, text.length)}';
          } else if (text.length <= 7) {
            // DDD + primeiros dígitos
            formatted =
                '(${text.substring(0, 2)}) ${text.substring(2, text.length)}';
          } else if (text.length <= 11) {
            // Formato completo
            formatted =
                '(${text.substring(0, 2)}) ${text.substring(2, 7)}-${text.substring(7, text.length)}';
          } else {
            // Limita a 11 dígitos
            formatted =
                '(${text.substring(0, 2)}) ${text.substring(2, 7)}-${text.substring(7, 11)}';
          }

          return TextEditingValue(
            text: formatted,
            selection: TextSelection.collapsed(offset: formatted.length),
          );
        }),
      ],
    );
  }

  /// Constrói o campo de data de nascimento
  Widget _buildDataNascimentoField() {
    return ResponsiveAuthField(
      controller: _dataNascimentoController,
      focusNode: _dataNascimentoFocusNode,
      labelText: 'Data de nascimento',
      hintText: 'DD/MM/AAAA',
      readOnly: true,
      onTap: () => _selectDate(context),
      validator: (value) => Validators.validateBirthDate(_selectedDate),
      suffixIcon: IconButton(
        icon: const Icon(Icons.calendar_today, color: AppTheme.snowWhiteColor),
        onPressed: () => _selectDate(context),
      ),
    );
  }

  /// Constrói o campo de senha
  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: !_isPasswordVisible,
      textInputAction: TextInputAction.next,
      style: const TextStyle(color: AppTheme.snowWhiteColor),
      decoration: InputDecoration(
        labelText: 'Senha',
        labelStyle: const TextStyle(color: AppTheme.snowWhiteColor),
        hintText: 'Digite sua senha',
        hintStyle: TextStyle(
          color: AppTheme.snowWhiteColor.withAlpha(179),
        ), // ~0.7 opacity
        filled: true,
        fillColor: Colors.transparent,
        suffixIcon: IconButton(
          icon: Icon(
            _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
            color: AppTheme.snowWhiteColor,
          ),
          onPressed: _togglePasswordVisibility,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.goldColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.errorColor, width: 1),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Por favor, digite sua senha';
        }
        if (value.length < 8) {
          return 'A senha deve ter pelo menos 8 caracteres';
        }
        if (value.length > 100) {
          return 'A senha deve ter no máximo 100 caracteres';
        }
        return null;
      },
    );
  }

  /// Constrói o campo de confirmação de senha
  Widget _buildConfirmPasswordField() {
    return TextFormField(
      controller: _confirmPasswordController,
      obscureText: !_isConfirmPasswordVisible,
      textInputAction: TextInputAction.done,
      style: const TextStyle(color: AppTheme.snowWhiteColor),
      decoration: InputDecoration(
        labelText: 'Confirmar Senha',
        labelStyle: const TextStyle(color: AppTheme.snowWhiteColor),
        hintText: 'Confirme sua senha',
        hintStyle: TextStyle(
          color: AppTheme.snowWhiteColor.withAlpha(179),
        ), // ~0.7 opacity
        filled: true,
        fillColor: Colors.transparent,
        suffixIcon: IconButton(
          icon: Icon(
            _isConfirmPasswordVisible ? Icons.visibility_off : Icons.visibility,
            color: AppTheme.snowWhiteColor,
          ),
          onPressed: _toggleConfirmPasswordVisibility,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppTheme.snowWhiteColor,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.goldColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.errorColor, width: 1),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Por favor, confirme sua senha';
        }
        if (value != _passwordController.text) {
          return 'As senhas não coincidem';
        }
        return null;
      },
    );
  }

  /// Constrói o checkbox de termos
  Widget _buildTermsCheckbox() {
    return Row(
      children: [
        Checkbox(
          value: _acceptTerms,
          onChanged: (value) {
            setState(() {
              _acceptTerms = value ?? false;
            });
          },
          checkColor: AppTheme.navyBlueColor,
          fillColor: WidgetStateProperty.resolveWith<Color>((
            Set<WidgetState> states,
          ) {
            if (states.contains(WidgetState.disabled)) {
              return AppTheme.snowWhiteColor.withAlpha(128);
            }
            return AppTheme.goldColor;
          }),
        ),
        Expanded(
          child: Text(
            'Eu aceito os termos de uso e política de privacidade',
            style: TextStyle(color: AppTheme.snowWhiteColor, fontSize: 14),
          ),
        ),
      ],
    );
  }

  /// Constrói o botão de cadastro
  Widget _buildRegisterButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _handleRegister,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.goldColor,
          foregroundColor: AppTheme.navyBlueColor,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          elevation: 0,
        ),
        child: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppTheme.navyBlueColor,
                  ),
                ),
              )
            : const Text(
                'Cadastrar',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
      ),
    );
  }

  /// Constrói o link "Já tem uma conta? Faça login"
  Widget _buildLoginLink() {
    return AuthNavigationLink(
      text: 'Já tem uma conta? ',
      linkText: 'Faça login',
      onPressed: _goToLogin,
    );
  }
}
