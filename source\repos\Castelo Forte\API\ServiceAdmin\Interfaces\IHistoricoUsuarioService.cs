﻿using ServiceAdmin.Interfaces.Generic;
using Shared.Entities.Admin;
using Shared.ViewModels.Admin;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServiceAdmin.Interfaces
{
    public interface IHistoricoUsuarioService : IGenericAdminService<HistoricoUsuarioViewModel, HistoricoUsuario>
    {
        Task RegistrarAcao(string metodo, string action, string dadoAntigo, string dadoNovo);

        /// <summary>
        /// Busca histórico de ações por usuário
        /// </summary>
        /// <param name="idUsuario">ID do usuário</param>
        /// <returns>Lista de histórico de ações do usuário</returns>
        Task<IEnumerable<HistoricoUsuarioViewModel>> BuscarPorUsuarioAsync(string idUsuario);

        /// <summary>
        /// Busca histórico de ações por período
        /// </summary>
        /// <param name="dataInicio">Data de início</param>
        /// <param name="dataFim">Data de fim</param>
        /// <returns>Lista de histórico de ações no período</returns>
        Task<IEnumerable<HistoricoUsuarioViewModel>> BuscarPorPeriodoAsync(DateTime dataInicio, DateTime dataFim);

        /// <summary>
        /// Busca histórico com filtros e paginação
        /// </summary>
        /// <param name="filtro">Filtros para busca</param>
        /// <returns>Resultado paginado de histórico</returns>
        Task<ResultadoPaginadoViewModel<HistoricoUsuarioViewModel>> BuscarHistoricoFiltradoAsync(FiltroHistoricoViewModel filtro);

        /// <summary>
        /// Busca histórico por ação específica
        /// </summary>
        /// <param name="acao">Nome da ação</param>
        /// <returns>Lista de histórico da ação</returns>
        Task<IEnumerable<HistoricoUsuarioViewModel>> BuscarPorAcaoAsync(string acao);

        /// <summary>
        /// Obtém estatísticas do histórico
        /// </summary>
        /// <returns>Estatísticas do histórico</returns>
        Task<object> ObterEstatisticasHistoricoAsync();
    }
}
