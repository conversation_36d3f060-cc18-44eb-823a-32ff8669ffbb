using AutoMapper;
using Microsoft.AspNetCore.Http;
using RepositoryClient.Interfaces;
using ServiceClient.Interfaces;
using ServiceClient.Repository.Generic;
using Shared.Entities.Client;
using Shared.Enums;
using Shared.ViewModels.Client;
using System.Linq.Expressions;

namespace ServiceClient.Service
{
    /// <summary>
    /// Serviço para operações com Transferência/Transação financeira
    /// </summary>
    public class TransferenciaService : GenericClientService<TransferenciaViewModel, Transferencia>, ITransferenciaService
    {
        private readonly ITransferenciaRepository _transferenciaRepository;

        public TransferenciaService(
            ITransferenciaRepository transferenciaRepository,
            IHttpContextAccessor httpContextAccessor,
            IMapper mapper)
            : base(transferenciaRepository, httpContextAccessor, mapper)
        {
            _transferenciaRepository = transferenciaRepository ?? throw new ArgumentNullException(nameof(transferenciaRepository));
        }

        #region Buscar por Conta

        /// <summary>
        /// Busca transferências por conta de origem
        /// </summary>
        public async Task<IEnumerable<TransferenciaViewModel>> BuscarPorContaOrigemAsync(string idContaOrigem)
        {
            if (string.IsNullOrWhiteSpace(idContaOrigem))
                throw new ArgumentException("ID da conta de origem é obrigatório", nameof(idContaOrigem));

            var transferencias = await BuscarPorFiltroAsync(t => t.IdContaOrigem == idContaOrigem);
            return transferencias.Where(t => t != null).Cast<TransferenciaViewModel>();
        }

        /// <summary>
        /// Busca transferências por conta de destino
        /// </summary>
        public async Task<IEnumerable<TransferenciaViewModel>> BuscarPorContaDestinoAsync(string idContaDestino)
        {
            if (string.IsNullOrWhiteSpace(idContaDestino))
                throw new ArgumentException("ID da conta de destino é obrigatório", nameof(idContaDestino));

            var transferencias = await BuscarPorFiltroAsync(t => t.IdContaDestino == idContaDestino);
            return transferencias.Where(t => t != null).Cast<TransferenciaViewModel>();
        }

        /// <summary>
        /// Busca transferências por múltiplas contas
        /// </summary>
        public async Task<IEnumerable<TransferenciaViewModel>> BuscarPorContasAsync(IEnumerable<string> idsContas)
        {
            if (idsContas == null || !idsContas.Any())
                throw new ArgumentException("Lista de IDs das contas é obrigatória", nameof(idsContas));

            var listaIds = idsContas.ToList();
            var transferencias = await BuscarPorFiltroAsync(t => 
                listaIds.Contains(t.IdContaOrigem) || 
                (t.IdContaDestino != null && listaIds.Contains(t.IdContaDestino)));
            
            return transferencias.Where(t => t != null).Cast<TransferenciaViewModel>();
        }

        #endregion

        #region Buscar por Categoria e Período

        /// <summary>
        /// Busca transferências por categoria
        /// </summary>
        public async Task<IEnumerable<TransferenciaViewModel>> BuscarPorCategoriaAsync(string idCategoria)
        {
            if (string.IsNullOrWhiteSpace(idCategoria))
                throw new ArgumentException("ID da categoria é obrigatório", nameof(idCategoria));

            var transferencias = await BuscarPorFiltroAsync(t => t.IdCategoria == idCategoria);
            return transferencias.Where(t => t != null).Cast<TransferenciaViewModel>();
        }

        /// <summary>
        /// Busca transferências por período
        /// </summary>
        public async Task<IEnumerable<TransferenciaViewModel>> BuscarPorPeriodoAsync(DateTime dataInicio, DateTime dataFim)
        {
            if (dataInicio > dataFim)
                throw new ArgumentException("Data de início deve ser menor ou igual à data de fim");

            var transferencias = await BuscarPorFiltroAsync(t => 
                t.DataTransferencia >= dataInicio && t.DataTransferencia <= dataFim);
            
            return transferencias.Where(t => t != null).Cast<TransferenciaViewModel>();
        }

        /// <summary>
        /// Busca transferências por status
        /// </summary>
        public async Task<IEnumerable<TransferenciaViewModel>> BuscarPorStatusAsync(int status)
        {
            var transferencias = await BuscarPorFiltroAsync(t => (int)t.Status == status);
            return transferencias.Where(t => t != null).Cast<TransferenciaViewModel>();
        }

        #endregion

        #region Cálculos e Estatísticas

        /// <summary>
        /// Calcula o total de transferências por período
        /// </summary>
        public async Task<decimal> CalcularTotalPorPeriodoAsync(DateTime dataInicio, DateTime dataFim, string? tipo = null)
        {
            if (dataInicio > dataFim)
                throw new ArgumentException("Data de início deve ser menor ou igual à data de fim");

            Expression<Func<Transferencia, bool>> filtro = t => 
                t.DataTransferencia >= dataInicio && t.DataTransferencia <= dataFim;

            if (!string.IsNullOrWhiteSpace(tipo))
            {
                filtro = t => t.DataTransferencia >= dataInicio && 
                             t.DataTransferencia <= dataFim && 
                             t.Tipo == tipo;
            }

            var transferencias = await BuscarPorFiltroAsync(filtro);
            return transferencias.Where(t => t != null).Sum(t => t!.Valor);
        }

        /// <summary>
        /// Busca estatísticas de transferências por período
        /// </summary>
        public async Task<object> BuscarEstatisticasPorPeriodoAsync(DateTime dataInicio, DateTime dataFim)
        {
            if (dataInicio > dataFim)
                throw new ArgumentException("Data de início deve ser menor ou igual à data de fim");

            var transferencias = await BuscarPorPeriodoAsync(dataInicio, dataFim);
            var lista = transferencias.ToList();

            var totalReceitas = lista.Where(t => t.Tipo == "RECEITA").Sum(t => t.Valor);
            var totalDespesas = lista.Where(t => t.Tipo == "DESPESA").Sum(t => t.Valor);
            var saldoLiquido = totalReceitas - totalDespesas;

            return new
            {
                TotalTransferencias = lista.Count,
                TotalReceitas = totalReceitas,
                TotalDespesas = totalDespesas,
                SaldoLiquido = saldoLiquido,
                MediaValor = lista.Any() ? lista.Average(t => t.Valor) : 0,
                MaiorValor = lista.Any() ? lista.Max(t => t.Valor) : 0,
                MenorValor = lista.Any() ? lista.Min(t => t.Valor) : 0
            };
        }

        #endregion

        #region Operações Especiais

        /// <summary>
        /// Busca transferências recentes
        /// </summary>
        public async Task<IEnumerable<TransferenciaViewModel>> BuscarRecentesAsync(int limite = 10)
        {
            if (limite <= 0)
                throw new ArgumentException("Limite deve ser maior que zero", nameof(limite));

            var todasTransferencias = await BuscarTodosAsync();
            return todasTransferencias
                .Where(t => t != null)
                .Cast<TransferenciaViewModel>()
                .OrderByDescending(t => t.DataTransferencia)
                .Take(limite);
        }

        /// <summary>
        /// Cancela uma transferência
        /// </summary>
        public async Task<bool> CancelarAsync(string id, string? motivo = null)
        {
            try
            {
                var transferencia = await BuscarPorIdAsync(id);
                if (transferencia == null)
                    return false;

                transferencia.Status = TransferenciaStatus.Cancelada;
                if (!string.IsNullOrWhiteSpace(motivo))
                {
                    transferencia.Observacoes = string.IsNullOrEmpty(transferencia.Observacoes) 
                        ? motivo 
                        : $"{transferencia.Observacoes}\nCancelamento: {motivo}";
                }

                var resultado = await EditarAsync(transferencia);
                return resultado != null;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Confirma uma transferência pendente
        /// </summary>
        public async Task<bool> ConfirmarAsync(string id)
        {
            try
            {
                var transferencia = await BuscarPorIdAsync(id);
                if (transferencia == null || transferencia.Status != TransferenciaStatus.Pendente)
                    return false;

                transferencia.Status = TransferenciaStatus.Concluida;
                var resultado = await EditarAsync(transferencia);
                return resultado != null;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Remove uma transferência (soft delete)
        /// </summary>
        public async Task<bool> RemoverAsync(string id)
        {
            try
            {
                var transferencia = await BuscarPorIdAsync(id);
                if (transferencia == null)
                    return false;

                await ExcluirAsync(transferencia);
                return true;
            }
            catch
            {
                return false;
            }
        }

        #endregion
    }
}
