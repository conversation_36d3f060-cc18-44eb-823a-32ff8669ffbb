using AutoMapper;
using Microsoft.AspNetCore.Http;
using RepositoryClient.Interfaces;
using ServiceClient.Interfaces;
using ServiceClient.Repository.Generic;
using Shared.Entities.Client;
using Shared.ViewModels.Client;

namespace ServiceClient.Service
{
    /// <summary>
    /// Serviço para Plano Financeiro no contexto Client (multi-tenant)
    /// </summary>
    public class PlanoFinanceiroService : GenericClientService<PlanoFinanceiroViewModel, PlanoFinanceiro>, IPlanoFinanceiroService
    {
        private readonly IPlanoFinanceiroRepository _planoFinanceiroRepository;

        public PlanoFinanceiroService(
            IPlanoFinanceiroRepository planoFinanceiroRepository,
            IHttpContextAccessor httpContextAccessor,
            IMapper mapper) : base(planoFinanceiroRepository, httpContextAccessor, mapper)
        {
            _planoFinanceiroRepository = planoFinanceiroRepository ?? throw new ArgumentNullException(nameof(planoFinanceiroRepository));
        }

        /// <summary>
        /// Busca planos financeiros por título
        /// </summary>
        /// <param name="titulo">Título do plano</param>
        /// <returns>Lista de planos com o título especificado</returns>
        public async Task<IEnumerable<PlanoFinanceiroViewModel>> BuscarPorTituloAsync(string titulo)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(titulo))
                    throw new ArgumentException("Título não pode ser nulo ou vazio", nameof(titulo));

                var planos = await BuscarTodosAsync();
                return planos.Where(p => p.Titulo.Contains(titulo, StringComparison.OrdinalIgnoreCase));
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar planos por título: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca planos financeiros ativos
        /// </summary>
        /// <returns>Lista de planos ativos</returns>
        public async Task<IEnumerable<PlanoFinanceiroViewModel>> BuscarAtivosAsync()
        {
            try
            {
                var planos = await BuscarTodosAsync();
                return planos.Where(p => p.FlgAtivo);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar planos ativos: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca planos financeiros por período de criação
        /// </summary>
        /// <param name="dataInicio">Data de início</param>
        /// <param name="dataFim">Data de fim</param>
        /// <returns>Lista de planos criados no período</returns>
        public async Task<IEnumerable<PlanoFinanceiroViewModel>> BuscarPorPeriodoCriacaoAsync(DateTime dataInicio, DateTime dataFim)
        {
            try
            {
                if (dataInicio > dataFim)
                    throw new ArgumentException("Data de início não pode ser maior que data de fim");

                var planos = await BuscarTodosAsync();
                return planos.Where(p => p.DtaCadastro >= dataInicio && p.DtaCadastro <= dataFim);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar planos por período de criação: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Duplica um plano financeiro existente
        /// </summary>
        /// <param name="idPlano">ID do plano a ser duplicado</param>
        /// <param name="novoTitulo">Novo título para o plano duplicado</param>
        /// <returns>Plano financeiro duplicado</returns>
        public async Task<PlanoFinanceiroViewModel?> DuplicarPlanoAsync(string idPlano, string novoTitulo)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(idPlano))
                    throw new ArgumentException("ID do plano não pode ser nulo ou vazio", nameof(idPlano));

                if (string.IsNullOrWhiteSpace(novoTitulo))
                    throw new ArgumentException("Novo título não pode ser nulo ou vazio", nameof(novoTitulo));

                var planoOriginal = await BuscarPorIdAsync(idPlano);
                if (planoOriginal == null)
                    throw new ArgumentException("Plano não encontrado", nameof(idPlano));

                var novoPlano = new PlanoFinanceiroViewModel
                {
                    Titulo = novoTitulo,
                    Descricao = $"Cópia de: {planoOriginal.Descricao}",
                    FlgAtivo = true,
                    DtaCadastro = DateTime.UtcNow
                };

                return await AdicionarAsync(novoPlano);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao duplicar plano financeiro: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Arquiva um plano financeiro
        /// </summary>
        /// <param name="idPlano">ID do plano</param>
        /// <returns>True se a operação foi bem-sucedida</returns>
        public async Task<bool> ArquivarPlanoAsync(string idPlano)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(idPlano))
                    throw new ArgumentException("ID do plano não pode ser nulo ou vazio", nameof(idPlano));

                var plano = await BuscarPorIdAsync(idPlano);
                if (plano == null)
                    throw new ArgumentException("Plano não encontrado", nameof(idPlano));

                plano.FlgAtivo = false;
                plano.DtaAlteracao = DateTime.UtcNow;

                var planoAtualizado = await EditarAsync(plano);
                return planoAtualizado != null;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao arquivar plano financeiro: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Restaura um plano financeiro arquivado
        /// </summary>
        /// <param name="idPlano">ID do plano</param>
        /// <returns>True se a operação foi bem-sucedida</returns>
        public async Task<bool> RestaurarPlanoAsync(string idPlano)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(idPlano))
                    throw new ArgumentException("ID do plano não pode ser nulo ou vazio", nameof(idPlano));

                var plano = await BuscarPorIdAsync(idPlano);
                if (plano == null)
                    throw new ArgumentException("Plano não encontrado", nameof(idPlano));

                plano.FlgAtivo = true;
                plano.DtaAlteracao = DateTime.UtcNow;

                var planoAtualizado = await EditarAsync(plano);
                return planoAtualizado != null;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao restaurar plano financeiro: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Atualiza um plano financeiro existente
        /// </summary>
        /// <param name="plano">Dados atualizados do plano</param>
        /// <returns>Plano atualizado</returns>
        public async Task<PlanoFinanceiroViewModel?> AtualizarAsync(PlanoFinanceiroViewModel plano)
        {
            try
            {
                if (plano == null)
                    throw new ArgumentNullException(nameof(plano));

                plano.DtaAlteracao = DateTime.UtcNow;
                return await EditarAsync(plano);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao atualizar plano financeiro: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Inativa um plano financeiro
        /// </summary>
        /// <param name="idPlano">ID do plano</param>
        /// <returns>True se a inativação foi bem-sucedida</returns>
        public async Task<bool> InativarAsync(string idPlano)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(idPlano))
                    throw new ArgumentException("ID do plano não pode ser nulo ou vazio", nameof(idPlano));

                var plano = await BuscarPorIdAsync(idPlano);
                if (plano == null)
                    return false;

                plano.FlgAtivo = false;
                plano.DtaAlteracao = DateTime.UtcNow;

                var planoAtualizado = await EditarAsync(plano);
                return planoAtualizado != null;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao inativar plano financeiro: {ex.Message}", ex);
            }
        }
    }
}
