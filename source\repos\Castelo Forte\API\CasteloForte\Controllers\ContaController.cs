using CasteloForte.Controllers.BaseComplemento;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceClient.Interfaces;
using Shared.ViewModels.Client;
using System.Text.Json;

namespace CasteloForte.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ContaController : ControllerBaseComplemento<ContaController>
    {
        private readonly string _controllerName = "ContaController";
        private readonly IContaService _contaService;

        public ContaController(
            IContaService contaService,
            ILogger<ContaController> logger,
            ILogErroClientService? logErroClientService = null,
            IHistoricoUsuarioClientService? historicoUsuarioClientService = null) 
            : base(logger, logErroClientService, historicoUsuarioClientService)
        {
            _contaService = contaService ?? throw new ArgumentNullException(nameof(contaService));
        }

        #region Buscar

        /// <summary>
        /// Busca todas as contas do usuário com filtros opcionais
        /// </summary>
        /// <param name="search">Filtro de busca por nome da conta ou apelido</param>
        /// <param name="ativa">Filtro por status ativo/inativo (opcional)</param>
        /// <returns>Lista de contas filtradas</returns>
        [HttpGet]
        public async Task<IActionResult> BuscarTodas(
            [FromQuery] string? search = null,
            [FromQuery] bool? ativa = null)
        {
            string variaveis = JsonSerializer.Serialize(new { search, ativa });
            try
            {
                string metodo = _controllerName + " BuscarTodas";
                await RegistraAcao(metodo, "Busca de contas com filtros", "", variaveis);

                LogInfo($"Iniciando busca de contas com filtros - Search: {search}, Ativa: {ativa}", nameof(BuscarTodas), _controllerName);

                var contas = await _contaService.BuscarComFiltrosAsync(search, ativa);

                LogInfo($"Busca concluída. {contas.Count()} contas encontradas", nameof(BuscarTodas), _controllerName);
                return CriarRespostaSucesso(contas, "Contas recuperadas com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        /// <summary>
        /// Busca uma conta por ID
        /// </summary>
        /// <param name="id">ID da conta</param>
        /// <returns>Conta encontrada</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> BuscarPorId(string id)
        {
            string variaveis = JsonSerializer.Serialize(new { id });
            try
            {
                string metodo = _controllerName + " BuscarPorId";
                await RegistraAcao(metodo, "Busca de conta por ID", "", variaveis);

                if (string.IsNullOrWhiteSpace(id))
                    return CriarRespostaErro("ID é obrigatório", "INVALID_ID");

                LogInfo($"Iniciando busca da conta com ID: {id}", nameof(BuscarPorId), _controllerName);
                
                var conta = await _contaService.BuscarPorIdAsync(id);
                
                if (conta == null)
                {
                    LogInfo($"Conta com ID {id} não encontrada", nameof(BuscarPorId), _controllerName);
                    return CriarRespostaNaoEncontrado("Conta não encontrada");
                }

                LogInfo($"Conta com ID {id} encontrada", nameof(BuscarPorId), _controllerName);
                return CriarRespostaSucesso(conta, "Conta recuperada com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        /// <summary>
        /// Busca contas por tipo
        /// </summary>
        /// <param name="tipo">Tipo da conta</param>
        /// <returns>Lista de contas do tipo especificado</returns>
        [HttpGet("tipo/{tipo}")]
        public async Task<IActionResult> BuscarPorTipo(string tipo)
        {
            string variaveis = JsonSerializer.Serialize(new { tipo });
            try
            {
                string metodo = _controllerName + " BuscarPorTipo";
                await RegistraAcao(metodo, "Busca de contas por tipo", "", variaveis);

                if (string.IsNullOrWhiteSpace(tipo))
                    return CriarRespostaErro("Tipo é obrigatório", "INVALID_TIPO");

                LogInfo($"Iniciando busca de contas por tipo: {tipo}", nameof(BuscarPorTipo), _controllerName);
                
                var contas = await _contaService.BuscarPorTipoAsync(tipo);
                
                LogInfo($"Busca concluída. {contas.Count()} contas encontradas para o tipo {tipo}", nameof(BuscarPorTipo), _controllerName);
                return CriarRespostaSucesso(contas, "Contas por tipo recuperadas com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        /// <summary>
        /// Busca contas por nome
        /// </summary>
        /// <param name="nome">Nome da conta</param>
        /// <returns>Lista de contas com o nome especificado</returns>
        [HttpGet("nome/{nome}")]
        public async Task<IActionResult> BuscarPorNome(string nome)
        {
            string variaveis = JsonSerializer.Serialize(new { nome });
            try
            {
                string metodo = _controllerName + " BuscarPorNome";
                await RegistraAcao(metodo, "Busca de contas por nome", "", variaveis);

                if (string.IsNullOrWhiteSpace(nome))
                    return CriarRespostaErro("Nome é obrigatório", "INVALID_NOME");

                LogInfo($"Iniciando busca de contas por nome: {nome}", nameof(BuscarPorNome), _controllerName);
                
                var contas = await _contaService.BuscarPorNomeAsync(nome);
                
                LogInfo($"Busca concluída. {contas.Count()} contas encontradas para o nome {nome}", nameof(BuscarPorNome), _controllerName);
                return CriarRespostaSucesso(contas, "Contas por nome recuperadas com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        /// <summary>
        /// Calcula o saldo total de todas as contas ativas
        /// </summary>
        /// <returns>Saldo total</returns>
        [HttpGet("saldo-total")]
        public async Task<IActionResult> CalcularSaldoTotal()
        {
            string variaveis = "";
            try
            {
                string metodo = _controllerName + " CalcularSaldoTotal";
                await RegistraAcao(metodo, "Cálculo do saldo total das contas", "", variaveis);

                LogInfo("Iniciando cálculo do saldo total", nameof(CalcularSaldoTotal), _controllerName);
                
                var saldoTotal = await _contaService.CalcularSaldoTotalAsync();
                
                LogInfo($"Cálculo concluído. Saldo total: {saldoTotal:C}", nameof(CalcularSaldoTotal), _controllerName);
                return CriarRespostaSucesso(new { saldoTotal }, "Saldo total calculado com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        #endregion

        #region Criar

        /// <summary>
        /// Cria uma nova conta
        /// </summary>
        /// <param name="conta">Dados da conta</param>
        /// <returns>Conta criada</returns>
        [HttpPost]
        public async Task<IActionResult> Criar([FromBody] ContaViewModel conta)
        {
            string variaveis = JsonSerializer.Serialize(conta);
            try
            {
                string metodo = _controllerName + " Criar";
                await RegistraAcao(metodo, "Criação de nova conta", "", variaveis);

                if (conta == null)
                    return CriarRespostaErro("Dados da conta são obrigatórios", "INVALID_DATA");

                if (!ModelState.IsValid)
                    return CriarRespostaErro("Dados inválidos", "VALIDATION_ERROR");

                LogInfo($"Iniciando criação de conta: {conta.Nome}", nameof(Criar), _controllerName);
                
                var contaCriada = await _contaService.AdicionarAsync(conta);
                
                if (contaCriada == null)
                    return CriarRespostaErro("Falha ao criar conta", "CREATE_ERROR");

                LogInfo($"Conta criada com sucesso. ID: {contaCriada.Id}", nameof(Criar), _controllerName);
                return CriarRespostaSucesso(contaCriada, "Conta criada com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        #endregion

        #region Atualizar
        /// <summary>
        /// Atualiza uma conta existente
        /// </summary>
        /// <param name="id">ID da conta</param>
        /// <param name="conta">Dados atualizados da conta</param>
        /// <returns>Conta atualizada</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> Atualizar(string id, [FromBody] ContaViewModel conta)
        {
            string variaveis = JsonSerializer.Serialize(new { id, conta });
            try
            {
                string metodo = _controllerName + " Atualizar";
                await RegistraAcao(metodo, "Atualização de conta", "", variaveis);

                if (string.IsNullOrWhiteSpace(id))
                    return CriarRespostaErro("ID é obrigatório", "INVALID_ID");

                if (conta == null)
                    return CriarRespostaErro("Dados da conta são obrigatórios", "INVALID_DATA");

                if (!ModelState.IsValid)
                    return CriarRespostaErro("Dados inválidos", "VALIDATION_ERROR");

                if (id != conta.Id)
                    return CriarRespostaErro("ID da URL não confere com ID do objeto", "ID_MISMATCH");

                LogInfo($"Iniciando atualização da conta com ID: {id}", nameof(Atualizar), _controllerName);
                
                var contaAtualizada = await _contaService.AtualizarAsync(conta);
                
                if (contaAtualizada == null)
                    return CriarRespostaNaoEncontrado("Conta não encontrada");

                LogInfo($"Conta atualizada com sucesso. ID: {id}", nameof(Atualizar), _controllerName);
                return CriarRespostaSucesso(contaAtualizada, "Conta atualizada com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        /// <summary>
        /// Atualiza o saldo de uma conta
        /// </summary>
        /// <param name="id">ID da conta</param>
        /// <param name="novoSaldo">Novo saldo</param>
        /// <returns>Resultado da operação</returns>
        [HttpPatch("{id}/saldo")]
        public async Task<IActionResult> AtualizarSaldo(string id, [FromBody] decimal novoSaldo)
        {
            string variaveis = JsonSerializer.Serialize(new { id, novoSaldo });
            try
            {
                string metodo = _controllerName + " AtualizarSaldo";
                await RegistraAcao(metodo, "Atualização de saldo da conta", "", variaveis);

                if (string.IsNullOrWhiteSpace(id))
                    return CriarRespostaErro("ID é obrigatório", "INVALID_ID");

                LogInfo($"Iniciando atualização de saldo da conta {id} para {novoSaldo:C}", nameof(AtualizarSaldo), _controllerName);
                
                var sucesso = await _contaService.AtualizarSaldoAsync(id, novoSaldo);
                
                if (!sucesso)
                    return CriarRespostaNaoEncontrado("Conta não encontrada");

                LogInfo($"Saldo atualizado com sucesso para a conta {id}", nameof(AtualizarSaldo), _controllerName);
                return CriarRespostaSucesso(new { id, novoSaldo }, "Saldo atualizado com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        #endregion

        #region Inativar/Reativar
        /// <summary>
        /// Inativa uma conta
        /// </summary>
        /// <param name="id">ID da conta</param>
        /// <returns>Resultado da operação</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> Inativar(string id)
        {
            string variaveis = JsonSerializer.Serialize(new { id });
            try
            {
                string metodo = _controllerName + " Inativar";
                await RegistraAcao(metodo, "Inativação de conta", "", variaveis);

                if (string.IsNullOrWhiteSpace(id))
                    return CriarRespostaErro("ID é obrigatório", "INVALID_ID");

                LogInfo($"Iniciando inativação da conta com ID: {id}", nameof(Inativar), _controllerName);
                
                var sucesso = await _contaService.InativarAsync(id);
                
                if (!sucesso)
                    return CriarRespostaNaoEncontrado("Conta não encontrada");

                LogInfo($"Conta inativada com sucesso. ID: {id}", nameof(Inativar), _controllerName);
                return CriarRespostaSucesso(new { id }, "Conta inativada com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        #endregion
    }
}
