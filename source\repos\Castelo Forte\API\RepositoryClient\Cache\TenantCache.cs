﻿using MongoDB.Driver;
using RepositoryClient.Cache.Interface;
using Shared.Models;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepositoryClient.Cache
{
    public class TenantCache : ITenantCache
    {
        private readonly ConcurrentDictionary<string, TenantInfo> _tenants = new();
        private readonly Timer _cleanupTimer;

        public TenantCache()
        {
            _cleanupTimer = new Timer(CleanupExpiredTenants, null,
                TimeSpan.FromMinutes(30), TimeSpan.FromMinutes(30));
        }

        public void SetTenant(string IdTenant, string connectionString, string nomeBaseDados)
        {
            var mongoClientSettings = MongoClientSettings.FromConnectionString(connectionString);
            ConfigurarSettingsMongoClient(mongoClientSettings);

            var tenantInfo = new TenantInfo
            {
                IdTenant = IdTenant,
                ConnectionString = connectionString,
                NomeBaseDados = nomeBaseDados,
                MongoClient = new MongoClient(mongoClientSettings),
                LastAccess = DateTime.UtcNow
            };

            _tenants.AddOrUpdate(IdTenant, tenantInfo, (key, old) =>
            {
                old.LastAccess = DateTime.UtcNow;
                return old;
            });
        }

        public TenantInfo? GetTenant(string IdTenant)
        {
            if (_tenants.TryGetValue(IdTenant, out var tenant))
            {
                tenant.LastAccess = DateTime.UtcNow;
                return tenant;
            }
            return null;
        }

        public bool HasTenant(string IdTenant)
        {
            return _tenants.ContainsKey(IdTenant);
        }

        public void RemoveTenant(string IdTenant)
        {
            _tenants.TryRemove(IdTenant, out _);
        }

        private void CleanupExpiredTenants(object? state)
        {
            var expiredTime = DateTime.UtcNow.AddHours(-2);
            var expiredTenants = _tenants.Where(kvp => kvp.Value.LastAccess < expiredTime)
                                       .Select(kvp => kvp.Key)
                                       .ToList();

            foreach (var IdTenant in expiredTenants)
            {
                _tenants.TryRemove(IdTenant, out _);
            }
        }

        private static void ConfigurarSettingsMongoClient(MongoClientSettings settings)
        {
            settings.ConnectTimeout = TimeSpan.FromSeconds(30);
            settings.SocketTimeout = TimeSpan.FromSeconds(30);
            settings.ServerSelectionTimeout = TimeSpan.FromSeconds(30);
            settings.MaxConnectionPoolSize = 50;
            settings.MinConnectionPoolSize = 5;
            settings.WaitQueueTimeout = TimeSpan.FromSeconds(30);
            settings.RetryWrites = true;
            settings.RetryReads = true;
            settings.HeartbeatInterval = TimeSpan.FromSeconds(10);
            settings.HeartbeatTimeout = TimeSpan.FromSeconds(20);
        }

        public void Dispose()
        {
            _cleanupTimer?.Dispose();
        }
    }
}
