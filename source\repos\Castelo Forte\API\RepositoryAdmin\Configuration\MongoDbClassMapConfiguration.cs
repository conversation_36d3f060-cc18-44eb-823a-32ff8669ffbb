﻿using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Conventions;
using Shared.Entities.Admin;
using Shared.Entities.Base;

namespace RepositoryAdmin.Configuration
{
    public class MongoDbClassMapConfiguration
    {
        private static bool _isConfigured = false;
        private static readonly object _lock = new object();

        public static void Configure()
        {
            if (_isConfigured) return;

            lock (_lock)
            {
                if (_isConfigured) return;

                try
                {
                    ConfigureSimpleConventions();
                    ConfigureEssentialClassMaps();
                    _isConfigured = true;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Erro ao configurar MongoDB ClassMap: {ex.Message}");
                }
            }
        }

        private static void ConfigureSimpleConventions()
        {
            var conventionPack = new ConventionPack
            {
                new IgnoreExtraElementsConvention(true)
            };
            ConventionRegistry.Register("SimpleConventions", conventionPack, t => true);
        }

        private static void ConfigureEssentialClassMaps()
        {
            if (!BsonClassMap.IsClassMapRegistered(typeof(BaseEntidade)))
            {
                BsonClassMap.RegisterClassMap<BaseEntidade>(cm =>
                {
                    cm.AutoMap();
                    cm.SetIgnoreExtraElements(true);
                });
            }

            RegisterEntityIfNeeded<Usuario>();
            RegisterEntityIfNeeded<LogErroAdmin>();
            RegisterEntityIfNeeded<HistoricoUsuario>();
        }

        private static void RegisterEntityIfNeeded<T>() where T : class
        {
            if (!BsonClassMap.IsClassMapRegistered(typeof(T)))
            {
                try
                {
                    BsonClassMap.RegisterClassMap<T>(cm =>
                    {
                        cm.AutoMap();
                        cm.SetIgnoreExtraElements(true);
                    });
                }
                catch
                {
                    throw;
                }
            }
        }
    }
}
