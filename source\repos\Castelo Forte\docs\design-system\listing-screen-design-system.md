# Design System - Telas de Listagem
## Padrão Baseado em AccountsListScreen

### 📋 ÍNDICE
1. [Estrutura de Layout](#estrutura-de-layout)
2. [Especificações de Cores](#especificações-de-cores)
3. [Tipografia](#tipografia)
4. [Componentes Reutilizáveis](#componentes-reutilizáveis)
5. [Padrões de Comportamento](#padrões-de-comportamento)
6. [Especificações Técnicas](#especificações-técnicas)
7. [Guia de Implementação](#guia-de-implementação)

---

## 1. ESTRUTURA DE LAYOUT

### 1.1 Hierarquia de Widgets
```
Scaffold
├── AppBar (transparente)
├── Body
│   ├── Loading/Error State OU
│   └── Column
│       ├── SearchBar + ActionButton (Container)
│       ├── HorizontalFilterChips (SingleChildScrollView)
│       ├── SizedBox(height: 20)
│       └── Expanded(ListView/EmptyState)
└── FloatingActionButton
```

### 1.2 Dimensões e Espaçamentos

#### Margens e Paddings Padrão
- **Container principal**: `margin: EdgeInsets.all(20)`
- **Cards de lista**: `margin: EdgeInsets.only(bottom: 12)`
- **Padding interno cards**: `padding: EdgeInsets.all(16)`
- **Espaçamento entre elementos**: `SizedBox(width/height: 8, 12, 16, 20)`

#### Dimensões Específicas
- **Border radius padrão**: `15px`
- **Ícones pequenos**: `16px` (filtros)
- **Ícones médios**: `24px` (cards)
- **Ícones grandes**: `80px` (estados vazios/erro)
- **Avatar circular**: `50x50px` com `borderRadius: 25px`

### 1.3 Elevações e Bordas
- **Cards ativos**: `elevation: 2`
- **Cards inativos**: `elevation: 1`
- **Border width padrão**: `1px`
- **Border radius padrão**: `15px`

---

## 2. ESPECIFICAÇÕES DE CORES

### 2.1 Paleta Principal
```dart
// Cores de Fundo
static const Color backgroundPrimary = Color(0xFF1A1A2E);
static const Color backgroundSecondary = Color(0xFF16213E);

// Cor de Destaque
static const Color accent = Color(0xFF4ECDC4);

// Cores de Texto
static const Color textPrimary = Colors.white;
static const Color textSecondary = Colors.white70; // alpha: 0.7
static const Color textTertiary = Colors.white54;  // alpha: 0.5
static const Color textDisabled = Colors.white30;  // alpha: 0.3

// Cores de Status
static const Color success = Colors.green;
static const Color error = Colors.red;
static const Color warning = Colors.orange;
static const Color inactive = Colors.grey;
```

### 2.2 Aplicação de Cores por Componente

#### AppBar
- **Background**: `Colors.transparent`
- **Elevation**: `0`
- **Título**: `Colors.white` + `FontWeight.bold`
- **Ícones**: `Colors.white`

#### SearchBar
- **Background**: `Color(0xFF16213E)`
- **Border**: `Color(0xFF4ECDC4).withValues(alpha: 0.3)`
- **Texto**: `Colors.white`
- **Hint**: `Colors.white70`
- **Ícone**: `Colors.white70`

#### ActionButton (Carregar)
- **Background**: `Color(0xFF4ECDC4)`
- **Ícone**: `Colors.white`

#### FilterChips
- **Background não selecionado**: `Colors.transparent`
- **Background selecionado**: `filterColor` (dinâmica)
- **Border**: `filterColor`
- **Texto não selecionado**: `filterColor`
- **Texto selecionado**: `Colors.white`

---

## 3. TIPOGRAFIA

### 3.1 Hierarquia de Texto
```dart
// Títulos
static const TextStyle titleLarge = TextStyle(
  fontSize: 18,
  fontWeight: FontWeight.w500,
  color: Colors.white,
);

// Títulos de Cards
static const TextStyle cardTitle = TextStyle(
  fontSize: 16,
  fontWeight: FontWeight.w600,
  color: Colors.white,
);

// Texto Secundário
static const TextStyle bodyMedium = TextStyle(
  fontSize: 14,
  fontWeight: FontWeight.normal,
  color: Colors.white70,
);

// Texto Pequeno/Labels
static const TextStyle labelSmall = TextStyle(
  fontSize: 12,
  fontWeight: FontWeight.w500,
);

// Texto Muito Pequeno
static const TextStyle captionSmall = TextStyle(
  fontSize: 10,
  fontWeight: FontWeight.bold,
  color: Colors.white,
);
```

### 4.2 HorizontalFilterChips
```dart
Widget buildHorizontalFilterChips<T>({
  required List<T> options,
  required T selectedOption,
  required Function(T) onOptionSelected,
  required String Function(T) getLabel,
  required Color Function(T) getColor,
  required IconData Function(T) getIcon,
}) {
  return SingleChildScrollView(
    scrollDirection: Axis.horizontal,
    child: Row(
      children: options.map((option) {
        final isSelected = option == selectedOption;
        final color = getColor(option);
        final icon = getIcon(option);
        final label = getLabel(option);

        return Container(
          margin: EdgeInsets.only(
            left: option == options.first ? 20 : 8,
            right: option == options.last ? 20 : 0,
          ),
          child: FilterChip(
            selected: isSelected,
            label: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  size: 16,
                  color: isSelected ? Colors.white : color,
                ),
                const SizedBox(width: 6),
                Text(
                  label,
                  style: TextStyle(
                    color: isSelected ? Colors.white : color,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            onSelected: (_) => onOptionSelected(option),
            backgroundColor: Colors.transparent,
            selectedColor: color,
            side: BorderSide(color: color),
          ),
        );
      }).toList(),
    ),
  );
}
```

### 4.3 Estados de Loading, Erro e Vazio

#### Loading State
```dart
Widget buildLoadingState() {
  return const Center(
    child: CircularProgressIndicator(
      valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4ECDC4)),
    ),
  );
}
```

#### Error State
```dart
Widget buildErrorState({
  required String title,
  required String message,
  required VoidCallback onRetry,
}) {
  return Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Icon(Icons.error_outline, size: 80, color: Colors.red),
        const SizedBox(height: 20),
        Text(
          title,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.7),
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          message,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.5),
            fontSize: 14,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 30),
        ElevatedButton.icon(
          onPressed: onRetry,
          icon: const Icon(Icons.refresh, color: Colors.white),
          label: const Text(
            'Tentar Novamente',
            style: TextStyle(color: Colors.white),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF4ECDC4),
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
        ),
      ],
    ),
  );
}
```

#### Empty State
```dart
Widget buildEmptyState({
  required IconData icon,
  required String title,
  required String subtitle,
  VoidCallback? onAction,
  String? actionLabel,
  IconData? actionIcon,
}) {
  return Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          icon,
          size: 80,
          color: Colors.white.withValues(alpha: 0.3),
        ),
        const SizedBox(height: 20),
        Text(
          title,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.7),
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          subtitle,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.5),
            fontSize: 14,
          ),
        ),
        if (onAction != null && actionLabel != null) ...[
          const SizedBox(height: 30),
          ElevatedButton.icon(
            onPressed: onAction,
            icon: Icon(actionIcon ?? Icons.add, color: Colors.white),
            label: Text(
              actionLabel,
              style: const TextStyle(color: Colors.white),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4ECDC4),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ],
    ),
  );
}
```

### 4.4 Card de Lista Padrão
```dart
Widget buildListCard({
  required Widget leading,
  required Widget content,
  required Widget trailing,
  required VoidCallback onTap,
  bool isActive = true,
  Color? accentColor,
}) {
  final cardOpacity = isActive ? 1.0 : 0.6;
  final cardColor = isActive
      ? const Color(0xFF16213E)
      : const Color(0xFF16213E).withValues(alpha: 0.7);
  final borderColor = accentColor ?? const Color(0xFF4ECDC4);

  return Container(
    margin: const EdgeInsets.only(bottom: 12),
    child: Opacity(
      opacity: cardOpacity,
      child: Card(
        color: cardColor,
        elevation: isActive ? 2 : 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
          side: BorderSide(
            color: borderColor.withValues(alpha: isActive ? 0.3 : 0.2),
            width: 1,
          ),
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(15),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                leading,
                const SizedBox(width: 16),
                Expanded(child: content),
                trailing,
              ],
            ),
          ),
        ),
      ),
    ),
  );
}
```

### 4.5 Avatar Circular com Ícone
```dart
Widget buildCircularAvatar({
  required IconData icon,
  required Color color,
  double size = 50,
}) {
  return Container(
    width: size,
    height: size,
    decoration: BoxDecoration(
      color: color.withValues(alpha: 0.2),
      borderRadius: BorderRadius.circular(size / 2),
    ),
    child: Icon(icon, color: color, size: size * 0.48),
  );
}
```

### 4.6 Modal de Detalhes
```dart
void showDetailsModal({
  required BuildContext context,
  required String title,
  required IconData titleIcon,
  required Color titleColor,
  required List<MapEntry<String, String>> details,
}) {
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      backgroundColor: const Color(0xFF16213E),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      title: Row(
        children: [
          Icon(titleIcon, color: titleColor),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: details.map((detail) =>
          _buildDetailRow(detail.key, detail.value)
        ).toList(),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text(
            'Fechar',
            style: TextStyle(color: Color(0xFF4ECDC4)),
          ),
        ),
      ],
    ),
  );
}

Widget _buildDetailRow(String label, String value) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 4),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 60,
          child: Text(
            '$label:',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          child: Text(value, style: const TextStyle(color: Colors.white)),
        ),
      ],
    ),
  );
}
```

### 4.7 Modal de Confirmação
```dart
void showConfirmationModal({
  required BuildContext context,
  required String title,
  required String message,
  required String confirmLabel,
  required VoidCallback onConfirm,
  String cancelLabel = 'Cancelar',
  Color confirmColor = Colors.red,
}) {
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      backgroundColor: const Color(0xFF16213E),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      title: Text(
        title,
        style: const TextStyle(color: Colors.white),
      ),
      content: Text(
        message,
        style: TextStyle(color: Colors.white.withValues(alpha: 0.7)),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            cancelLabel,
            style: const TextStyle(color: Color(0xFF4ECDC4)),
          ),
        ),
        TextButton(
          onPressed: () {
            Navigator.pop(context);
            onConfirm();
          },
          style: TextButton.styleFrom(foregroundColor: confirmColor),
          child: Text(confirmLabel),
        ),
      ],
    ),
  );
}
```

---

## 5. PADRÕES DE COMPORTAMENTO

### 5.1 Fluxo de Busca Manual
```dart
// PADRÃO: Busca só executa quando usuário clica no botão ou pressiona Enter
class SearchBehavior {
  static void setupManualSearch({
    required TextEditingController controller,
    required VoidCallback onSearch,
  }) {
    // NÃO adicionar listener automático:
    // controller.addListener(onSearch); // ❌ EVITAR

    // Busca manual apenas:
    // 1. Botão de ação
    // 2. onSubmitted do TextField
  }
}
```

### 5.2 Interações de Filtros
```dart
// PADRÃO: Filtros executam busca automaticamente
void onFilterChanged(String newFilter) {
  setState(() {
    _selectedFilter = newFilter;
  });
  // Executa busca imediatamente quando filtro muda
  _performSearch();
}
```

### 5.3 Gerenciamento de Estados
```dart
// PADRÃO: Estados mutuamente exclusivos
enum ListState { loading, error, empty, populated }

class ListScreenState {
  bool _isLoading = true;
  String? _error;
  List<T> _items = [];

  ListState get currentState {
    if (_isLoading) return ListState.loading;
    if (_error != null) return ListState.error;
    if (_items.isEmpty) return ListState.empty;
    return ListState.populated;
  }
}
```

### 5.4 Navegação e Refresh
```dart
// PADRÃO: Refresh após navegação
Future<void> navigateAndRefresh(Widget destination) async {
  final result = await Navigator.push(
    context,
    MaterialPageRoute(builder: (context) => destination),
  );
  if (result == true) {
    _loadData(); // Sempre recarregar após mudanças
  }
}

// PADRÃO: Refresh automático no resume
@override
void didChangeAppLifecycleState(AppLifecycleState state) {
  super.didChangeAppLifecycleState(state);
  if (state == AppLifecycleState.resumed && _hasBeenInitialized) {
    _loadData();
  }
}
```

### 5.5 Feedback Visual
```dart
// PADRÃO: SnackBar para feedback de ações
void showFeedback({
  required String message,
  bool isSuccess = true,
  Duration duration = const Duration(seconds: 3),
}) {
  if (!mounted) return;

  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text(message),
      backgroundColor: isSuccess ? Colors.green : Colors.red,
      duration: duration,
    ),
  );
}
```

---

## 6. ESPECIFICAÇÕES TÉCNICAS

### 6.1 Estrutura de Classes Padrão
```dart
class ListScreen extends StatefulWidget {
  const ListScreen({super.key});

  @override
  State<ListScreen> createState() => _ListScreenState();
}

class _ListScreenState extends State<ListScreen>
    with WidgetsBindingObserver {

  // Controllers e Estado
  final TextEditingController _searchController = TextEditingController();
  List<T> _items = [];
  bool _isLoading = true;
  String? _error;
  String _selectedFilter = 'TODAS';
  bool _hasBeenInitialized = false;

  // Opções de filtro
  final List<String> _filterOptions = ['TODAS', 'ATIVAS', 'INATIVAS'];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    AppLifecycleManager.instance.setCurrentRoute(routeName);
    _loadData();
    _hasBeenInitialized = true;
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _searchController.dispose();
    super.dispose();
  }

  // Métodos obrigatórios
  Future<void> _loadData() async { /* implementar */ }
  void _performSearch() { _loadData(); }
  Future<void> _forceReload() async { await _loadData(); }
}
```

### 6.2 Convenções de Nomenclatura
```dart
// Variáveis de Estado
bool _isLoading = true;           // Estado de carregamento
String? _error;                   // Mensagem de erro
List<T> _filteredItems = [];      // Lista filtrada
String _selectedFilter = 'TODAS'; // Filtro selecionado
bool _hasBeenInitialized = false; // Controle de inicialização

// Controllers
final TextEditingController _searchController = TextEditingController();

// Métodos Privados
Future<void> _loadData() async { }      // Carrega dados da API
void _performSearch() { }               // Executa busca manual
Future<void> _forceReload() async { }   // Força recarregamento
void _navigateToCreate() async { }      // Navega para criação
void _navigateToEdit(T item) async { }  // Navega para edição
void _showDetails(T item) { }           // Mostra detalhes
void _showDeleteConfirmation(T item) { } // Confirma exclusão
Future<void> _deleteItem(T item) async { } // Exclui item

// Métodos de Build
Widget _buildLoadingState() { }         // Estado de loading
Widget _buildErrorState() { }           // Estado de erro
Widget _buildEmptyState() { }           // Estado vazio
Widget _buildItemCard(T item) { }       // Card do item
Widget _buildDetailRow(String label, String value) { } // Linha de detalhe
```

### 6.3 Padrões de Gerenciamento de Estado
```dart
// PADRÃO: setState para mudanças locais
void _updateLocalState() {
  setState(() {
    _selectedFilter = newFilter;
    _isLoading = true;
    _error = null;
  });
}

// PADRÃO: Tratamento de erros
try {
  final result = await Service.getData();
  setState(() {
    _items = result.items;
    _isLoading = false;
  });

  // Avisos não críticos
  if (result.errorMessage != null && result.items.isNotEmpty) {
    showFeedback(message: result.errorMessage!, isSuccess: false);
  }
} catch (e) {
  setState(() {
    _error = e.toString();
    _isLoading = false;
    _items = [];
  });
}
```

### 6.4 Integração com APIs e Serviços
```dart
// PADRÃO: Chamadas de API com filtros server-side
Future<void> _loadData() async {
  setState(() {
    _isLoading = true;
    _error = null;
  });

  try {
    // Determinar filtros baseado na seleção
    bool? activeFilter;
    if (_selectedFilter == 'ATIVAS') {
      activeFilter = true;
    } else if (_selectedFilter == 'INATIVAS') {
      activeFilter = false;
    }

    // Busca com filtros server-side
    final result = await Service.getData(
      search: _searchController.text.isNotEmpty
          ? _searchController.text
          : null,
      activeFilter: activeFilter,
    );

    setState(() {
      _items = result.items;
      _isLoading = false;
    });
  } catch (e) {
    setState(() {
      _error = e.toString();
      _isLoading = false;
      _items = [];
    });
  }
}
```

---

## 7. GUIA DE IMPLEMENTAÇÃO

### 7.1 Checklist Passo-a-Passo

#### ✅ Estrutura Básica
- [ ] Criar StatefulWidget com WidgetsBindingObserver
- [ ] Definir variáveis de estado padrão
- [ ] Implementar initState() com lifecycle manager
- [ ] Implementar dispose() com cleanup
- [ ] Implementar didChangeAppLifecycleState()

#### ✅ Interface do Usuário
- [ ] Scaffold com backgroundColor: Color(0xFF1A1A2E)
- [ ] AppBar transparente com SafeBackButton
- [ ] SearchBar + ActionButton container
- [ ] HorizontalFilterChips com SingleChildScrollView
- [ ] Expanded ListView com RefreshIndicator
- [ ] FloatingActionButton com cor padrão

#### ✅ Estados da Tela
- [ ] Loading state com CircularProgressIndicator
- [ ] Error state com ícone, mensagem e botão retry
- [ ] Empty state com ícone, mensagem e ação opcional
- [ ] Populated state com ListView.builder

#### ✅ Componentes de Lista
- [ ] Cards com Container + Opacity + Card
- [ ] Leading: CircularAvatar com ícone
- [ ] Content: Column com informações
- [ ] Trailing: Column com valor e PopupMenuButton

#### ✅ Interações
- [ ] Busca manual (botão + Enter)
- [ ] Filtros automáticos
- [ ] Tap para detalhes
- [ ] PopupMenu para ações
- [ ] Pull-to-refresh

#### ✅ Navegação
- [ ] Navegação para criação/edição
- [ ] Refresh após retorno
- [ ] Modais de detalhes e confirmação

### 7.2 Template de Implementação
```dart
// lib/features/[feature]/presentation/[feature]_list_screen.dart
import 'package:flutter/material.dart';
import '../../../core/utils/constants.dart';
import '../../../core/widgets/safe_back_button.dart';
import '../../../core/services/app_lifecycle_manager.dart';
import '../data/[feature]_service.dart';
import '../data/models/[feature]_model.dart';

class [Feature]ListScreen extends StatefulWidget {
  const [Feature]ListScreen({super.key});

  @override
  State<[Feature]ListScreen> createState() => _[Feature]ListScreenState();
}

class _[Feature]ListScreenState extends State<[Feature]ListScreen>
    with WidgetsBindingObserver {

  // TODO: Implementar seguindo o padrão AccountsListScreen
  // 1. Definir variáveis de estado
  // 2. Implementar métodos de lifecycle
  // 3. Implementar _loadData() com filtros server-side
  // 4. Implementar _performSearch() para busca manual
  // 5. Implementar build() com componentes padrão
  // 6. Implementar estados (loading, error, empty)
  // 7. Implementar _buildItemCard() personalizado
  // 8. Implementar navegação e modais
}
```

### 7.3 Exemplos de Customização

#### Cores Personalizadas por Contexto
```dart
// Para diferentes tipos de listagem
class ListColors {
  static const Color accounts = Color(0xFF4ECDC4);    // Azul-verde
  static const Color categories = Color(0xFF4ECDC4);  // Azul-verde
  static const Color transactions = Color(0xFF9B59B6); // Roxo
  static const Color reports = Color(0xFFE67E22);     // Laranja
}
```

#### Filtros Específicos por Contexto
```dart
// Contas
final List<String> _accountFilters = ['TODAS', 'ATIVAS', 'INATIVAS'];

// Transações
final List<String> _transactionFilters = ['TODAS', 'RECEITAS', 'DESPESAS'];

// Categorias
final List<String> _categoryFilters = ['TODAS', 'RECEITA', 'DESPESA'];
```

#### Ícones por Contexto
```dart
// Mapeamento de ícones por tipo
Map<String, IconData> getContextIcons(String context) {
  switch (context) {
    case 'accounts':
      return {
        'default': Icons.account_balance_wallet,
        'active': Icons.check_circle,
        'inactive': Icons.cancel,
      };
    case 'categories':
      return {
        'default': Icons.category,
        'income': Icons.trending_up,
        'expense': Icons.trending_down,
      };
    case 'transactions':
      return {
        'default': Icons.receipt,
        'income': Icons.add_circle,
        'expense': Icons.remove_circle,
      };
    default:
      return {'default': Icons.list};
  }
}
```

### 7.4 Variações Permitidas

#### Busca Automática (Exceção)
```dart
// APENAS para casos específicos onde busca automática é necessária
@override
void initState() {
  super.initState();
  // ... setup padrão

  // EXCEÇÃO: Busca automática com debounce
  _searchController.addListener(_onSearchChanged);
}

Timer? _debounceTimer;
void _onSearchChanged() {
  _debounceTimer?.cancel();
  _debounceTimer = Timer(const Duration(milliseconds: 500), () {
    _performSearch();
  });
}
```

#### Filtros Adicionais
```dart
// Adicionar filtros extras mantendo o padrão
final List<String> _statusFilters = ['TODAS', 'ATIVAS', 'INATIVAS'];
final List<String> _typeFilters = ['TODOS', 'CORRENTE', 'POUPANÇA'];

// Implementar múltiplas linhas de filtros
Widget _buildFilters() {
  return Column(
    children: [
      _buildStatusFilters(),
      const SizedBox(height: 12),
      _buildTypeFilters(),
    ],
  );
}
```

#### Cards Personalizados
```dart
// Manter estrutura base, personalizar conteúdo
Widget _buildCustomCard(CustomModel item) {
  return buildListCard(
    leading: buildCircularAvatar(
      icon: _getCustomIcon(item),
      color: _getCustomColor(item),
    ),
    content: _buildCustomContent(item),
    trailing: _buildCustomTrailing(item),
    onTap: () => _showCustomDetails(item),
    isActive: item.isActive,
    accentColor: _getCustomColor(item),
  );
}
```

---

## 📝 RESUMO EXECUTIVO

Este design system estabelece um padrão consistente para todas as telas de listagem do aplicativo Castelo Forte, baseado na implementação da tela de contas (AccountsListScreen).

### Principais Características:
- **Busca Manual**: Input + botão de carregar (não automática)
- **Filtros Automáticos**: Chips horizontais com busca imediata
- **Estados Bem Definidos**: Loading, Error, Empty, Populated
- **Componentes Reutilizáveis**: SearchBar, FilterChips, Cards, Modals
- **Padrão Visual Consistente**: Cores, tipografia e espaçamentos padronizados
- **Comportamento Previsível**: Navegação, refresh e feedback uniformes

### Benefícios:
- ✅ Consistência visual em todo o app
- ✅ Experiência do usuário padronizada
- ✅ Desenvolvimento mais rápido com componentes reutilizáveis
- ✅ Manutenção simplificada
- ✅ Onboarding facilitado para novos desenvolvedores

**Uso obrigatório para todas as novas telas de listagem no projeto.**

### 3.2 Estados de Texto
- **Ativo**: Cor normal
- **Inativo**: Cor com `alpha: 0.6`
- **Desabilitado**: Cor com `alpha: 0.3`

---

## 4. COMPONENTES REUTILIZÁVEIS

### 4.1 SearchBar com ActionButton
```dart
Widget buildSearchBarWithAction({
  required TextEditingController controller,
  required VoidCallback onSearch,
  required String hintText,
  IconData searchIcon = Icons.search,
  IconData actionIcon = Icons.refresh,
  String actionTooltip = 'Carregar resultados',
}) {
  return Container(
    margin: const EdgeInsets.all(20),
    child: Row(
      children: [
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: const Color(0xFF16213E),
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: const Color(0xFF4ECDC4).withValues(alpha: 0.3),
              ),
            ),
            child: TextField(
              controller: controller,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                hintText: hintText,
                hintStyle: const TextStyle(color: Colors.white70),
                prefixIcon: Icon(searchIcon, color: Colors.white70),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 15,
                ),
              ),
              onSubmitted: (_) => onSearch(),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFF4ECDC4),
            borderRadius: BorderRadius.circular(15),
          ),
          child: IconButton(
            onPressed: onSearch,
            icon: Icon(actionIcon, color: Colors.white),
            tooltip: actionTooltip,
          ),
        ),
      ],
    ),
  );
}
```
