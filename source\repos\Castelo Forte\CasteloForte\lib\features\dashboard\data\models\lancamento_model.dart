/// Modelo de dados para lançamento financeiro
class LancamentoModel {
  final String id;
  final String descricao;
  final double valor;
  final DateTime data;
  final String tipo; // "RECEITA" ou "DESPESA"
  final String categoria;

  const LancamentoModel({
    required this.id,
    required this.descricao,
    required this.valor,
    required this.data,
    required this.tipo,
    required this.categoria,
  });

  /// Cria uma instância a partir de JSON
  factory LancamentoModel.fromJson(Map<String, dynamic> json) {
    return LancamentoModel(
      id: json['id']?.toString() ?? '',
      descricao: json['descricao']?.toString() ?? '',
      valor: (json['valor'] as num?)?.toDouble() ?? 0.0,
      data: json['data'] != null 
          ? DateTime.parse(json['data'].toString())
          : DateTime.now(),
      tipo: json['tipo']?.toString() ?? 'DESPESA',
      categoria: json['categoria']?.toString() ?? '',
    );
  }

  /// Converte para JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'descricao': descricao,
      'valor': valor,
      'data': data.toIso8601String(),
      'tipo': tipo,
      'categoria': categoria,
    };
  }

  /// Verifica se é uma receita
  bool get isReceita => tipo.toUpperCase() == 'RECEITA';

  /// Verifica se é uma despesa
  bool get isDespesa => tipo.toUpperCase() == 'DESPESA';

  /// Retorna o valor com sinal (positivo para receita, negativo para despesa)
  double get valorComSinal => isReceita ? valor : -valor;

  /// Cria uma cópia com campos modificados
  LancamentoModel copyWith({
    String? id,
    String? descricao,
    double? valor,
    DateTime? data,
    String? tipo,
    String? categoria,
  }) {
    return LancamentoModel(
      id: id ?? this.id,
      descricao: descricao ?? this.descricao,
      valor: valor ?? this.valor,
      data: data ?? this.data,
      tipo: tipo ?? this.tipo,
      categoria: categoria ?? this.categoria,
    );
  }

  @override
  String toString() {
    return 'LancamentoModel(id: $id, descricao: $descricao, valor: $valor, data: $data, tipo: $tipo, categoria: $categoria)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LancamentoModel &&
        other.id == id &&
        other.descricao == descricao &&
        other.valor == valor &&
        other.data == data &&
        other.tipo == tipo &&
        other.categoria == categoria;
  }

  @override
  int get hashCode {
    return Object.hash(id, descricao, valor, data, tipo, categoria);
  }
}
