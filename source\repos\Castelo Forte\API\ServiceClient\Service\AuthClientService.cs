using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using RepositoryAdmin.Interfaces;
using RepositoryClient.Configuration.Interfaces;
using ServiceClient.Interfaces;
using Shared.Entities.Admin;
using Shared.Entities.Client;
using Shared.Helpers.Encripty;
using Shared.Models.Configuration;
using Shared.Utils;
using Shared.ViewModels.Client;
using System.Text.RegularExpressions;

namespace ServiceClient.Service
{
    /// <summary>
    /// Service para autenticação no sistema Client
    /// </summary>
    public class AuthClientService : IAuthClientService
    {
        private readonly IUsuarioRepository _usuarioRepository;
        private readonly JwtTokenService _jwtTokenService;
        private readonly IEncryptionService _encryptionService;
        private readonly IMapper _mapper;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly MongoDBSettings _mongoSettings;
        private readonly IContextoMultiTenantService _contextoMultiTenant;

        public AuthClientService(
            IUsuarioRepository usuarioRepository,
            JwtTokenService jwtTokenService,
            IEncryptionService encryptionService,
            IMapper mapper,
            IHttpContextAccessor httpContextAccessor,
            IOptions<MongoDBSettings> mongoSettings,
            IContextoMultiTenantService contextoMultiTenant)
        {
            _usuarioRepository = usuarioRepository ?? throw new ArgumentNullException(nameof(usuarioRepository));
            _jwtTokenService = jwtTokenService ?? throw new ArgumentNullException(nameof(jwtTokenService));
            _encryptionService = encryptionService ?? throw new ArgumentNullException(nameof(encryptionService));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
            _mongoSettings = mongoSettings.Value ?? throw new ArgumentNullException(nameof(mongoSettings));
            _contextoMultiTenant = contextoMultiTenant ?? throw new ArgumentNullException(nameof(contextoMultiTenant));
        }

        /// <summary>
        /// Realiza a autenticação do usuário
        /// </summary>
        public async Task<LoginResponseViewModel> AutenticarAsync(LoginRequestViewModel loginRequest)
        {
            try
            {
                if (loginRequest == null)
                    throw new ArgumentNullException(nameof(loginRequest), "Dados de login são obrigatórios");

                // Valida se CPF ou email foi fornecido
                if (string.IsNullOrWhiteSpace(loginRequest.CPF) && string.IsNullOrWhiteSpace(loginRequest.Email))
                    throw new ArgumentException("CPF ou Email deve ser fornecido");

                if (string.IsNullOrWhiteSpace(loginRequest.Senha))
                    throw new ArgumentException("Senha é obrigatória");

                // Busca o usuário por CPF ou email
                Usuario? usuario = null;
                if (!string.IsNullOrWhiteSpace(loginRequest.CPF))
                {
                    var cpfLimpo = LimparCPF(loginRequest.CPF);
                    usuario = await _usuarioRepository.BuscarPrimeiroPorFiltroAsync(u => u.Cpf == cpfLimpo);
                }
                else if (!string.IsNullOrWhiteSpace(loginRequest.Email))
                {
                    usuario = await _usuarioRepository.BuscarPrimeiroPorFiltroAsync(u => u.Email == loginRequest.Email.ToLower());
                }

                if (usuario == null)
                    throw new UnauthorizedAccessException("Credenciais inválidas");

                // Verifica se o usuário está ativo
                if (!usuario.FlgAtivo)
                    throw new UnauthorizedAccessException("Usuário inativo");

                // Valida a senha
                var senhaCriptografada = Criptografias.Encriptar(loginRequest.Senha, false);
                if (usuario.Senha != senhaCriptografada)
                    throw new UnauthorizedAccessException("Credenciais inválidas");

                // Verifica se possui connection string, se não, gera uma
                var primeiroAcesso = false;
                if (string.IsNullOrEmpty(usuario.ConnectionString))
                {
                    await GerarConnectionStringUsuarioAsync(usuario.Id!);
                    // Recarrega o usuário com a connection string atualizada
                    usuario = await _usuarioRepository.BuscarPorIdAsync(usuario.Id!);
                    primeiroAcesso = true;
                }

                // Configura o contexto multi-tenant imediatamente após garantir que há connection string
                if (!string.IsNullOrEmpty(usuario.ConnectionString))
                {
                    var connectionStringDescriptografada = ObterConnectionStringDescriptografada(usuario.ConnectionString);
                    if (!string.IsNullOrEmpty(connectionStringDescriptografada))
                    {
                        _contextoMultiTenant.ConfigurarTenant(usuario.Id!, connectionStringDescriptografada, usuario.NomeBaseDados);
                    }
                }

                // Gera o token JWT
                var (token, expiresAt) = _jwtTokenService.GerarToken(usuario!);

                // Atualiza o último acesso
                await AtualizarUltimoAcessoAsync(usuario!.Id!);

                // Descriptografa a connection string para retornar na resposta
                var connectionStringParaResposta = ObterConnectionStringDescriptografada(usuario!.ConnectionString);

                // Monta a resposta
                var response = new LoginResponseViewModel
                {
                    Token = token,
                    TokenType = "Bearer",
                    ExpiresAt = expiresAt,
                    Usuario = MapearUsuarioParaViewModel(usuario!),
                    ConnectionString = connectionStringParaResposta,
                    NomeBaseDados = usuario!.NomeBaseDados ?? string.Empty,
                    PrimeiroAcesso = primeiroAcesso,
                    Mensagem = primeiroAcesso ? "Bem-vindo! Este é seu primeiro acesso." : $"Bem-vindo de volta, {usuario.Nome}!"
                };

                return response;
            }
            catch (UnauthorizedAccessException)
            {
                throw;
            }
            catch (ArgumentException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro interno durante autenticação: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Valida um token JWT
        /// </summary>
        public async Task<ValidateTokenResponseViewModel> ValidarTokenAsync(string token)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(token))
                {
                    return new ValidateTokenResponseViewModel
                    {
                        IsValid = false,
                        MensagemErro = "Token não fornecido"
                    };
                }

                var principal = _jwtTokenService.ValidarToken(token);
                if (principal == null)
                {
                    return new ValidateTokenResponseViewModel
                    {
                        IsValid = false,
                        MensagemErro = "Token inválido"
                    };
                }

                var usuarioId = principal.FindFirst("UsuarioId")?.Value;
                if (string.IsNullOrEmpty(usuarioId))
                {
                    return new ValidateTokenResponseViewModel
                    {
                        IsValid = false,
                        MensagemErro = "Token não contém informações de usuário válidas"
                    };
                }

                // Verifica se o usuário ainda existe e está ativo
                var usuario = await _usuarioRepository.BuscarPorIdAsync(usuarioId);
                if (usuario == null || !usuario.FlgAtivo)
                {
                    return new ValidateTokenResponseViewModel
                    {
                        IsValid = false,
                        MensagemErro = "Usuário não encontrado ou inativo"
                    };
                }

                var expiresAt = _jwtTokenService.ObterDataExpiracao(token);

                return new ValidateTokenResponseViewModel
                {
                    IsValid = true,
                    Usuario = MapearUsuarioParaViewModel(usuario),
                    ExpiresAt = expiresAt
                };
            }
            catch (Exception ex)
            {
                return new ValidateTokenResponseViewModel
                {
                    IsValid = false,
                    MensagemErro = $"Erro ao validar token: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Renova um token JWT
        /// </summary>
        public async Task<RefreshTokenResponseViewModel> RenovarTokenAsync(string tokenAtual)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(tokenAtual))
                    throw new ArgumentException("Token atual é obrigatório");

                var informacoesUsuario = _jwtTokenService.ExtrairInformacoesUsuario(tokenAtual);
                if (informacoesUsuario == null)
                    throw new UnauthorizedAccessException("Token inválido");

                var usuario = await _usuarioRepository.BuscarPorIdAsync(informacoesUsuario.Value.usuarioId);
                if (usuario == null || !usuario.FlgAtivo)
                    throw new UnauthorizedAccessException("Usuário não encontrado ou inativo");

                var (novoToken, expiresAt) = _jwtTokenService.GerarToken(usuario);

                return new RefreshTokenResponseViewModel
                {
                    Token = novoToken,
                    TokenType = "Bearer",
                    ExpiresAt = expiresAt
                };
            }
            catch (UnauthorizedAccessException)
            {
                throw;
            }
            catch (ArgumentException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao renovar token: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Realiza o logout do usuário
        /// </summary>
        public async Task<bool> LogoutAsync(string? token = null)
        {
            try
            {
                // Por enquanto, apenas retorna true pois o JWT é stateless
                // Em uma implementação mais robusta, poderia adicionar o token a uma blacklist
                await Task.CompletedTask;
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Verifica se o usuário possui connection string configurada
        /// </summary>
        public async Task<bool> UsuarioPossuiConnectionStringAsync(string usuarioId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(usuarioId))
                    return false;

                var usuario = await _usuarioRepository.BuscarPorIdAsync(usuarioId);
                return usuario != null && !string.IsNullOrEmpty(usuario.ConnectionString);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Gera uma nova connection string para o usuário
        /// </summary>
        public async Task<bool> GerarConnectionStringUsuarioAsync(string usuarioId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(usuarioId))
                    throw new ArgumentException("ID do usuário é obrigatório");

                var usuario = await _usuarioRepository.BuscarPorIdAsync(usuarioId);
                if (usuario == null)
                    throw new KeyNotFoundException("Usuário não encontrado");

                // Gera o nome da base de dados se não existir
                if (string.IsNullOrEmpty(usuario.NomeBaseDados))
                {
                    usuario.NomeBaseDados = Utils.GerarNomeBaseDados(usuario.Cpf);
                }

                // Usa a connection string base do MongoDB e adiciona o nome da base específica
                var connectionStringBase = _mongoSettings.ConnectionString;
                if (string.IsNullOrEmpty(connectionStringBase))
                    throw new InvalidOperationException("Connection string base não configurada");

                // A connection string já inclui todas as configurações necessárias
                // Apenas criptografa para armazenar
                var connectionStringCriptografada = _encryptionService.Encrypt(connectionStringBase);
                
                usuario.ConnectionString = connectionStringCriptografada;
                
                await _usuarioRepository.EditarAsync(usuario);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Atualiza o último acesso do usuário
        /// </summary>
        public async Task<bool> AtualizarUltimoAcessoAsync(string usuarioId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(usuarioId))
                    return false;

                var usuario = await _usuarioRepository.BuscarPorIdAsync(usuarioId);
                if (usuario == null)
                    return false;

                usuario.DtaUltimoAcesso = DateTime.UtcNow;
                await _usuarioRepository.EditarAsync(usuario);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Verifica se o usuário está ativo
        /// </summary>
        public async Task<bool> UsuarioAtivoAsync(string usuarioId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(usuarioId))
                    return false;

                var usuario = await _usuarioRepository.BuscarPorIdAsync(usuarioId);
                return usuario?.FlgAtivo == true;
            }
            catch
            {
                return false;
            }
        }

        #region Métodos Auxiliares

        /// <summary>
        /// Remove formatação do CPF
        /// </summary>
        private static string LimparCPF(string cpf)
        {
            if (string.IsNullOrWhiteSpace(cpf))
                return string.Empty;

            return Regex.Replace(cpf, @"[^\d]", "");
        }

        /// <summary>
        /// Mapeia entidade Usuario para UsuarioAutenticadoViewModel
        /// </summary>
        private static UsuarioAutenticadoViewModel MapearUsuarioParaViewModel(Usuario usuario)
        {
            return new UsuarioAutenticadoViewModel
            {
                Id = usuario.Id ?? string.Empty,
                Nome = usuario.Nome ?? string.Empty,
                Email = usuario.Email ?? string.Empty,
                CPF = MascararCPF(usuario.Cpf ?? string.Empty),
                Telefone = usuario.Celular,
                DataNascimento = usuario.DtaNascimento,
                UltimoAcesso = usuario.DtaUltimoAcesso,
                DoisFatoresHabilitado = usuario.FlgDoisFatores,
                Perfil = "Usuario",
                DataCadastro = usuario.DtaCadastro ?? DateTime.MinValue
            };
        }

        /// <summary>
        /// Mascara o CPF para exibição segura
        /// </summary>
        private static string MascararCPF(string cpf)
        {
            if (string.IsNullOrWhiteSpace(cpf) || cpf.Length != 11)
                return "***.***.***-**";

            return $"{cpf.Substring(0, 3)}.***.***.{cpf.Substring(9, 2)}";
        }

        /// <summary>
        /// Obtém a connection string descriptografada, tratando casos especiais como placeholders
        /// </summary>
        /// <param name="connectionString">Connection string que pode estar criptografada ou ser um placeholder</param>
        /// <returns>Connection string descriptografada ou string vazia se for um placeholder</returns>
        private string ObterConnectionStringDescriptografada(string? connectionString)
        {
            if (string.IsNullOrEmpty(connectionString))
                return string.Empty;

            // Verifica se é um placeholder que não deve ser descriptografado
            var placeholders = new[] { "PENDING_GENERATION", "PERFIL_PADRAO", "NOT_CONFIGURED" };
            if (placeholders.Contains(connectionString))
            {
                return string.Empty; // Retorna vazio para placeholders
            }

            try
            {
                // Tenta descriptografar a connection string
                return _encryptionService.Decrypt(connectionString);
            }
            catch (FormatException)
            {
                // Se não conseguir descriptografar (não é Base64 válido), assume que é texto plano
                // Isso pode acontecer com dados legados ou em desenvolvimento
                return string.Empty;
            }
            catch (Exception ex)
            {
                // Log do erro para debug, mas não quebra o fluxo
                // Em produção, você pode querer logar isso
                Console.WriteLine($"Erro ao descriptografar connection string: {ex.Message}");
                return string.Empty;
            }
        }

        #endregion
    }
}
