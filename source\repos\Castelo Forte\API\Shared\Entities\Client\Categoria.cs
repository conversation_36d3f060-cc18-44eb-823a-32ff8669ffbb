using MongoDB.Bson.Serialization.Attributes;
using Shared.Entities.Base;
using Shared.Enums;
using System.ComponentModel.DataAnnotations;

namespace Shared.Entities.Client
{
    /// <summary>
    /// Entidade que representa uma categoria de transação/meta
    /// </summary>
    public class Categoria : BaseEntidade
    {
        /// <summary>
        /// Nome da categoria
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Nome { get; set; } = "";

        /// <summary>
        /// Descrição detalhada da categoria
        /// </summary>
        [StringLength(500)]
        public string Descricao { get; set; } = "";

        /// <summary>
        /// Tipo da categoria (Receita, Despesa, Meta)
        /// </summary>
        [Required]
        public TipoCategoria Tipo { get; set; } = TipoCategoria.Despesa;

        /// <summary>
        /// Cor da categoria em formato hexadecimal (ex: "#FFFFFF")
        /// </summary>
        [Required]
        [StringLength(7)]
        public string Cor { get; set; } = "#FFFFFF"; // Branco padrão

        /// <summary>
        /// Código do ícone da categoria
        /// </summary>
        public int Icone { get; set; } = 0xE7FD; // category icon

        /// <summary>
        /// Indica se a categoria está ativa
        /// </summary>
        public bool Ativa { get; set; } = true;

        /// <summary>
        /// Data de criação da categoria
        /// </summary>
        [DataType(DataType.DateTime)]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime DataCriacao { get; set; } = DateTime.Now;

        /// <summary>
        /// Data da última alteração
        /// </summary>
        [DataType(DataType.DateTime)]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime? DataAlteracao { get; set; }

        /// <summary>
        /// Ordem de exibição da categoria
        /// </summary>
        public int Ordem { get; set; } = 0;



        /// <summary>
        /// Limite de gastos para esta categoria (opcional)
        /// </summary>
        public decimal? LimiteGastos { get; set; }

        /// <summary>
        /// Período do limite (Mensal, Semanal, etc.)
        /// </summary>
        public PeriodoLimite? PeriodoLimite { get; set; }

        /// <summary>
        /// Observações adicionais sobre a categoria
        /// </summary>
        public string? Observacoes { get; set; }
    }
}
