﻿using Microsoft.AspNetCore.Http;
using RepositoryAdmin.Interfaces;
using RepositoryClient.Configuration.Interfaces;
using RepositoryClient.Interfaces;
using RepositoryClient.Repositories.Generic;
using Shared.Entities.Client;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepositoryClient.Repositories
{
    public class TransferenciasRecorrentesRepository(
        IContextoMultiTenantService contextoMultiTenant,
        IHttpContextAccessor httpContextAccessor,
         IUsuarioRepository usuarioRepository) : GenericClientRepository<TransferenciasRecorrentes>(contextoMultiTenant, httpContextAccessor, usuarioRepository, "TransferenciasRecorrentes"), ITransferenciasRecorrentesRepository
    {
    }
}
