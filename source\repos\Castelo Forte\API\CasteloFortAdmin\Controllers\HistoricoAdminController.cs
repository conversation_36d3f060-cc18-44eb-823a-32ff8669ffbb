using CasteloForteAdmin.Attributes;
using CasteloForteAdmin.Controllers.ControllerBaseComplemento;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceAdmin.Interfaces;
using Shared.ViewModels.Admin;
using System.Text.Json;

namespace CasteloForteAdmin.Controllers
{
    /// <summary>
    /// Controller dedicado ao gerenciamento de histórico de ações do sistema administrativo
    /// </summary>
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class HistoricoAdminController : ControllerBaseComplemento<HistoricoAdminController>
    {
        private readonly string _controller = "HistoricoAdminController";
        private readonly IHistoricoUsuarioService _historicoUsuarioService;
        private readonly ILogErroAdminService _logErroAdminService;

        public HistoricoAdminController(
            IHistoricoUsuarioService historicoUsuarioService,
            ILogErroAdminService logErroAdminService,
            ILogger<HistoricoAdminController> logger
            ) : base(logErroAdminService, historicoUsuarioService, logger)
        {
            _historicoUsuarioService = historicoUsuarioService ?? throw new ArgumentNullException(nameof(historicoUsuarioService));
            _logErroAdminService = logErroAdminService ?? throw new ArgumentNullException(nameof(logErroAdminService));
        }

        #region Consultas Avançadas de Histórico

        /// <summary>
        /// Busca histórico com filtros avançados e paginação
        /// </summary>
        /// <param name="filtro">Filtros para busca</param>
        /// <returns>Resultado paginado de histórico</returns>
        [HttpPost("filtrados")]
        [AdminAuthorize]
        public async Task<IActionResult> BuscarHistoricoFiltrado([FromBody] FiltroHistoricoViewModel filtro)
        {
            string variaveis = JsonSerializer.Serialize(filtro);
            try
            {
                string metodo = _controller + " BuscarHistoricoFiltrado";
                await RegistraAcao(metodo, "Busca de histórico com filtros avançados", "", variaveis);

                if (filtro == null)
                    return BadRequest(new { error = "Filtros são obrigatórios" });

                LogInfo($"Iniciando busca filtrada de histórico - Página: {filtro.Pagina}", nameof(BuscarHistoricoFiltrado), _controller);

                var resultado = await _historicoUsuarioService.BuscarHistoricoFiltradoAsync(filtro);

                LogInfo($"Busca filtrada concluída. {resultado.TotalRegistros} registros encontrados", nameof(BuscarHistoricoFiltrado), _controller);
                return Ok(new
                {
                    success = true,
                    message = "Histórico filtrado recuperado com sucesso",
                    data = resultado
                });
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controller, variaveis);
            }
        }

        /// <summary>
        /// Busca histórico por ação específica
        /// </summary>
        /// <param name="acao">Nome da ação</param>
        /// <returns>Lista de histórico da ação</returns>
        [HttpGet("acao/{acao}")]
        public async Task<IActionResult> BuscarPorAcao(string acao)
        {
            string variaveis = JsonSerializer.Serialize(new { acao });
            try
            {
                string metodo = _controller + " BuscarPorAcao";
                await RegistraAcao(metodo, "Busca de histórico por ação", "", variaveis);

                if (string.IsNullOrWhiteSpace(acao))
                    return BadRequest(new { error = "Nome da ação é obrigatório" });

                LogInfo($"Iniciando busca de histórico para ação: {acao}", nameof(BuscarPorAcao), _controller);

                var historico = await _historicoUsuarioService.BuscarPorAcaoAsync(acao);

                LogInfo($"Busca concluída. {historico.Count()} registros encontrados para a ação", nameof(BuscarPorAcao), _controller);
                return Ok(new
                {
                    success = true,
                    message = "Histórico da ação recuperado com sucesso",
                    data = historico,
                    total = historico.Count()
                });
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controller, variaveis);
            }
        }

        /// <summary>
        /// Busca histórico por usuário específico
        /// </summary>
        /// <param name="idUsuario">ID do usuário</param>
        /// <returns>Lista de histórico do usuário</returns>
        [HttpGet("usuario/{idUsuario}")]
        public async Task<IActionResult> BuscarPorUsuario(string idUsuario)
        {
            string variaveis = JsonSerializer.Serialize(new { idUsuario });
            try
            {
                string metodo = _controller + " BuscarPorUsuario";
                await RegistraAcao(metodo, "Busca de histórico por usuário", "", variaveis);

                if (string.IsNullOrWhiteSpace(idUsuario))
                    return BadRequest(new { error = "ID do usuário é obrigatório" });

                LogInfo($"Iniciando busca de histórico para usuário: {idUsuario}", nameof(BuscarPorUsuario), _controller);

                var historico = await _historicoUsuarioService.BuscarPorUsuarioAsync(idUsuario);

                LogInfo($"Busca concluída. {historico.Count()} registros encontrados para o usuário", nameof(BuscarPorUsuario), _controller);
                return Ok(new
                {
                    success = true,
                    message = "Histórico do usuário recuperado com sucesso",
                    data = historico,
                    total = historico.Count()
                });
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controller, variaveis);
            }
        }

        #endregion

        #region Estatísticas e Relatórios

        /// <summary>
        /// Obtém estatísticas detalhadas do histórico
        /// </summary>
        /// <returns>Estatísticas do histórico</returns>
        [HttpGet("estatisticas")]
        [AdminAuthorize]
        public async Task<IActionResult> ObterEstatisticas()
        {
            string variaveis = "";
            try
            {
                string metodo = _controller + " ObterEstatisticas";
                await RegistraAcao(metodo, "Obtenção de estatísticas do histórico", "", variaveis);

                LogInfo("Iniciando obtenção de estatísticas do histórico", nameof(ObterEstatisticas), _controller);

                var estatisticas = await _historicoUsuarioService.ObterEstatisticasHistoricoAsync();

                LogInfo("Estatísticas do histórico obtidas com sucesso", nameof(ObterEstatisticas), _controller);
                return Ok(new
                {
                    success = true,
                    message = "Estatísticas do histórico obtidas com sucesso",
                    data = estatisticas
                });
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controller, variaveis);
            }
        }

        #endregion

        #region Consultas Simples (Para compatibilidade)

        /// <summary>
        /// Busca todo o histórico (sem filtros)
        /// NOTA: Use BuscarHistoricoFiltrado para melhor performance
        /// </summary>
        /// <returns>Lista de todo o histórico</returns>
        [HttpGet("todos")]
        public async Task<IActionResult> BuscarTodos()
        {
            string variaveis = "";
            try
            {
                string metodo = _controller + " BuscarTodos";
                await RegistraAcao(metodo, "Busca de todo o histórico", "", variaveis);

                LogInfo("Iniciando busca de todo o histórico", nameof(BuscarTodos), _controller);

                var historico = await _historicoUsuarioService.BuscarTodosAsync();

                LogInfo($"Busca concluída. {historico.Count()} registros encontrados", nameof(BuscarTodos), _controller);
                return Ok(new
                {
                    success = true,
                    message = "Histórico recuperado com sucesso",
                    data = historico,
                    total = historico.Count(),
                    warning = "Para melhor performance, use o endpoint /filtrados"
                });
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controller, variaveis);
            }
        }

        /// <summary>
        /// Busca histórico por período
        /// NOTA: Use BuscarHistoricoFiltrado para melhor flexibilidade
        /// </summary>
        /// <param name="dataInicio">Data de início</param>
        /// <param name="dataFim">Data de fim</param>
        /// <returns>Lista de histórico no período</returns>
        [HttpGet("periodo")]
        public async Task<IActionResult> BuscarPorPeriodo([FromQuery] DateTime dataInicio, [FromQuery] DateTime dataFim)
        {
            string variaveis = JsonSerializer.Serialize(new { dataInicio, dataFim });
            try
            {
                string metodo = _controller + " BuscarPorPeriodo";
                await RegistraAcao(metodo, "Busca de histórico por período", "", variaveis);

                LogInfo($"Iniciando busca de histórico por período: {dataInicio:yyyy-MM-dd} a {dataFim:yyyy-MM-dd}", nameof(BuscarPorPeriodo), _controller);

                var historico = await _historicoUsuarioService.BuscarPorPeriodoAsync(dataInicio, dataFim);

                LogInfo($"Busca concluída. {historico.Count()} registros encontrados no período", nameof(BuscarPorPeriodo), _controller);
                return Ok(new
                {
                    success = true,
                    message = "Histórico do período recuperado com sucesso",
                    data = historico,
                    total = historico.Count(),
                    periodo = new { dataInicio, dataFim },
                    warning = "Para melhor flexibilidade, use o endpoint /filtrados"
                });
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controller, variaveis);
            }
        }

        #endregion
    }
}
