﻿using ServiceAdmin.Interfaces.Generic;
using Shared.Entities.Admin;
using Shared.ViewModels.Admin;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServiceAdmin.Interfaces
{
    public interface IUsuarioService : IGenericAdminService<UsuarioViewModel, Usuario>
    {
        Task<bool> CadastroUsuario(UsuarioViewModel model);
        Task<bool> CadastroUsuarioNovo(CadastroUsuarioViewModel cadastroModel);
        Task<UsuarioViewModel?> CadastroUsuarioNovoComRetorno(CadastroUsuarioViewModel cadastroModel);

        /// <summary>
        /// Busca todos os usuários com seus dados completos
        /// </summary>
        /// <returns>Lista de todos os usuários</returns>
        Task<IEnumerable<UsuarioViewModel>> BuscarTodosUsuariosComDadosAsync();

        /// <summary>
        /// Busca usuários com filtros e paginação
        /// </summary>
        /// <param name="filtro">Filtros para busca</param>
        /// <returns>Resultado paginado de usuários</returns>
        Task<ResultadoPaginadoViewModel<UsuarioViewModel>> BuscarUsuariosFiltradosAsync(FiltroUsuarioViewModel filtro);

        /// <summary>
        /// Busca usuários ativos
        /// </summary>
        /// <returns>Lista de usuários ativos</returns>
        Task<IEnumerable<UsuarioViewModel>> BuscarUsuariosAtivosAsync();

        /// <summary>
        /// Busca usuários inativos
        /// </summary>
        /// <returns>Lista de usuários inativos</returns>
        Task<IEnumerable<UsuarioViewModel>> BuscarUsuariosInativosAsync();

        /// <summary>
        /// Obtém estatísticas dos usuários
        /// </summary>
        /// <returns>Estatísticas dos usuários</returns>
        Task<object> ObterEstatisticasUsuariosAsync();
    }
}
