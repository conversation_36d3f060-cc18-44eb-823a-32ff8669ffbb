﻿using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json.Linq;
using Shared.Entities.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shared.Entities.Admin
{
    public class Usuario : BaseEntidade
    {
        #region DadosBase
        public string Nome { get; set; } = "";
        public string Email { get; set; } = "";
        public string Cpf { get; set; } = "";
        public string Celular { get; set; } = "";

        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime DtaNascimento { get; set; }
        #endregion

        #region TermosECondições
        public bool? FlgTermosECondicoes { get; set; }

        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime? DtaTermosECondicoes { get; set; }
        #endregion

        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime? DtaUltimoAcesso { get; set; }

        #region Token
        public string TokenAcesso { get; set; } = "";
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime? DtaTokenAcessoGerado { get; set; }
        #endregion

        public string Senha { get; set; } = "";
        public bool FlgDoisFatores { get; set; } = false;

        /// <summary>
        /// Flag que indica se o usuário tem privilégios de administrador do sistema
        /// </summary>
        public bool FlgAdministrador { get; set; } = false;

        #region BaseDeDados
        public string ConnectionString { get; set; } = "";
        public string NomeBaseDados { get; set; } = "";
        #endregion

        public string IdPerfilFinanceiro { get; set; } = "";

        #region PorcentagemPerfil
        public int CoracaoInquieto { get; set; } = 0;
        public int ConstrutorAnalitico { get; set; } = 0;
        public int VisionarioOusado { get; set; } = 0;
        public int ExploradorGeneroso { get; set; } = 0;
        public int EstrategistaConsciente { get; set; } = 0;
        #endregion
    }
}
