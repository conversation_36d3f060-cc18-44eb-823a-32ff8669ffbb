﻿using MongoDB.Bson.Serialization.Attributes;
using Shared.Entities.Base;
using Shared.Enums;
using System;
using System.ComponentModel.DataAnnotations;

namespace Shared.Entities.Client
{
    /// <summary>
    /// Entidade que representa uma transferência/transação financeira
    /// </summary>
    public class Transferencia : BaseEntidade
    {
        /// <summary>
        /// Descrição da transferência
        /// </summary>
        [Required]
        [StringLength(500)]
        public string Descricao { get; set; } = "";

        /// <summary>
        /// Valor da transferência
        /// </summary>
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "O valor deve ser maior que zero")]
        public decimal Valor { get; set; } = 0;

        /// <summary>
        /// Data da transferência
        /// </summary>
        [Required]
        [DataType(DataType.Date)]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime DataTransferencia { get; set; } = DateTime.Now;

        /// <summary>
        /// Data de processamento da transferência
        /// </summary>
        [DataType(DataType.DateTime)]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime? DataProcessamento { get; set; }

        #region Contas
        /// <summary>
        /// ID da conta de origem (obrigatório)
        /// </summary>
        [Required]
        public string IdContaOrigem { get; set; } = "";

        /// <summary>
        /// ID da conta de destino (opcional para despesas)
        /// </summary>
        public string? IdContaDestino { get; set; }
        #endregion

        #region Categoria
        /// <summary>
        /// ID da categoria da transferência
        /// </summary>
        [Required]
        public string IdCategoria { get; set; } = "";
        #endregion

        #region Status e Controle
        /// <summary>
        /// Status atual da transferência
        /// </summary>
        public TransferenciaStatus Status { get; set; } = TransferenciaStatus.Concluida;

        /// <summary>
        /// Origem da transação
        /// </summary>
        public OrigemTransacao Origem { get; set; } = OrigemTransacao.Manual;

        /// <summary>
        /// Tipo de transação (RECEITA/DESPESA)
        /// </summary>
        [Required]
        public string Tipo { get; set; } = "DESPESA";
        #endregion

        #region Metadados
        /// <summary>
        /// ID de referência externa (para transações de APIs externas)
        /// </summary>
        public string? IdReferenciaExterna { get; set; }

        /// <summary>
        /// ID do registro Open Finance (para integração bancária)
        /// </summary>
        public string? IdRegistroOpenFinance { get; set; }

        /// <summary>
        /// Hash único para evitar duplicação de transações externas
        /// </summary>
        public string? HashTransacao { get; set; }

        /// <summary>
        /// Observações adicionais
        /// </summary>
        public string? Observacoes { get; set; }

        /// <summary>
        /// Dados JSON adicionais para metadados específicos
        /// </summary>
        public string? MetadadosJson { get; set; }
        #endregion

        #region Pagamento
        /// <summary>
        /// Forma de pagamento
        /// </summary>
        public string FormaPagamento { get; set; } = "";

        /// <summary>
        /// Indica se é pagamento no crédito
        /// </summary>
        public bool FlgPagamentoCredito { get; set; } = false;
        #endregion

        #region Recorrência (DEPRECATED - usar TransferenciaRecorrente)
        /// <summary>
        /// DEPRECATED: Use TransferenciaRecorrente
        /// </summary>
        [Obsolete("Use TransferenciaRecorrente")]
        public bool FlgLancamentoFixo { get; set; } = false;

        /// <summary>
        /// DEPRECATED: Use TransferenciaRecorrente
        /// </summary>
        [Obsolete("Use TransferenciaRecorrente")]
        public int? QtdMesesEntreLancamentos { get; set; } = 0;

        /// <summary>
        /// DEPRECATED: Use TransferenciaRecorrente
        /// </summary>
        [Obsolete("Use TransferenciaRecorrente")]
        public int? DiaMesLancamento { get; set; } = 0;

        /// <summary>
        /// ID da transferência recorrente que gerou esta transação
        /// </summary>
        public string? IdTransferenciaRecorrente { get; set; }
        #endregion

        #region Propriedades Calculadas
        /// <summary>
        /// Verifica se é uma receita
        /// </summary>
        public bool IsReceita => Tipo.ToUpper() == "RECEITA";

        /// <summary>
        /// Verifica se é uma despesa
        /// </summary>
        public bool IsDespesa => Tipo.ToUpper() == "DESPESA";

        /// <summary>
        /// Verifica se é uma transferência entre contas
        /// </summary>
        public bool IsTransferencia => !string.IsNullOrEmpty(IdContaDestino);

        /// <summary>
        /// Verifica se a transação foi processada com sucesso
        /// </summary>
        public bool IsProcessada => Status == TransferenciaStatus.Concluida;

        /// <summary>
        /// Verifica se a transação pode ser editada
        /// </summary>
        public bool PodeEditar => Status == TransferenciaStatus.Pendente ||
                                  (Status == TransferenciaStatus.Concluida && Origem == OrigemTransacao.Manual);

        /// <summary>
        /// Verifica se a transação pode ser cancelada
        /// </summary>
        public bool PodeCancelar => Status.PodeCancelar();
        #endregion

        #region Métodos de Negócio
        /// <summary>
        /// Marca a transferência como processada
        /// </summary>
        public void MarcarComoProcessada()
        {
            Status = TransferenciaStatus.Concluida;
            DataProcessamento = DateTime.Now;
        }

        /// <summary>
        /// Cancela a transferência
        /// </summary>
        /// <param name="motivo">Motivo do cancelamento</param>
        public void Cancelar(string? motivo = null)
        {
            if (PodeCancelar)
            {
                Status = TransferenciaStatus.Cancelada;
                if (!string.IsNullOrEmpty(motivo))
                {
                    Observacoes = string.IsNullOrEmpty(Observacoes) ? motivo : $"{Observacoes}\nCancelamento: {motivo}";
                }
            }
        }

        /// <summary>
        /// Gera hash único para a transação
        /// </summary>
        public void GerarHashTransacao()
        {
            var dados = $"{IdContaOrigem}|{Valor}|{DataTransferencia:yyyyMMddHHmmss}|{Descricao}";
            HashTransacao = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(dados));
        }
        #endregion
    }
}
