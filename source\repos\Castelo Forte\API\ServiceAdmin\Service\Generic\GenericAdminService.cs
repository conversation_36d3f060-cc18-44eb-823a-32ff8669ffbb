﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using RepositoryAdmin.Interfaces.Generic;
using ServiceAdmin.Interfaces.Generic;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace ServiceAdmin.Repository.Generic
{
    public abstract class GenericAdminService<TViewModel, TEntity> : IGenericAdminService<TViewModel, TEntity>
        where TViewModel : class
        where TEntity : class
    {
        protected readonly IGenericAdminRepository<TEntity> _repository;
        protected readonly IHttpContextAccessor _httpContextAccessor;
        protected readonly IMapper _mapper;

        protected string? IdUsuarioLogado =>
            _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;

        protected string? NomeUsuarioLogado =>
            _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.Name)?.Value;

        protected string? EmailUsuarioLogado =>
            _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.Email)?.Value;

        public string? IdEmpresaUsuarioLogado =>
            _httpContextAccessor.HttpContext?.User.FindFirst("EmpresaId")?.Value;

        protected GenericAdminService(
            IGenericAdminRepository<TEntity> repository,
            IHttpContextAccessor httpContextAccessor,
            IMapper mapper)
        {
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        public virtual async Task<IEnumerable<TViewModel?>> BuscarTodosAsync()
        {
            var entities = await _repository.BuscarTodosAsync();
            return entities.Select(ConverteEntidadeParaViewModel);
        }

        public virtual async Task<IEnumerable<TViewModel?>> BuscarPorFiltroAsync(Expression<Func<TEntity?, bool>> expression)
        {
            ArgumentNullException.ThrowIfNull(expression);

            var entities = await _repository.BuscarPorFiltroAsync(expression);
            return entities.Select(ConverteEntidadeParaViewModel);
        }

        public virtual async Task<IEnumerable<TViewModel?>> BuscarPorFiltroPaginadoAsync(
            Expression<Func<TEntity?, bool>> expression, int page, int pageSize)
        {
            ArgumentNullException.ThrowIfNull(expression);

            if (page <= 0) throw new ArgumentException("Page deve ser maior que zero", nameof(page));
            if (pageSize <= 0) throw new ArgumentException("PageSize deve ser maior que zero", nameof(pageSize));

            var entities = await _repository.BuscarPorFiltroPaginadoAsync(expression, page, pageSize);
            return entities.Select(ConverteEntidadeParaViewModel);
        }

        public virtual async Task<TViewModel?> BuscarPorIdAsync(string id)
        {
            if (string.IsNullOrWhiteSpace(id))
                throw new ArgumentException("ID não pode ser nulo ou vazio", nameof(id));

            var entity = await _repository.BuscarPorIdAsync(id);
            return entity != null ? ConverteEntidadeParaViewModel(entity) : null;
        }

        public virtual async Task<TViewModel?> BuscarPrimeiroPorFiltroAsync(Expression<Func<TEntity?, bool>> expression)
        {
            ArgumentNullException.ThrowIfNull(expression);

            var entities = await _repository.BuscarPorFiltroAsync(expression);
            var firstEntity = entities.FirstOrDefault();
            return firstEntity != null ? ConverteEntidadeParaViewModel(firstEntity) : null;
        }

        public virtual async Task<int> BuscarContagemTotalPorFiltroAsync(Expression<Func<TEntity?, bool>> expression)
        {
            ArgumentNullException.ThrowIfNull(expression);
            return await _repository.BuscarContagemTotalPorFiltroAsync(expression);
        }

        public virtual async Task<int> BuscarContagemTotalAsync()
        {
            return await _repository.BuscarContagemTotalAsync();
        }

        public virtual async Task<TViewModel?> AdicionarAsync(TViewModel viewModel)
        {
            ArgumentNullException.ThrowIfNull(viewModel);

            var entity = ConverteViewModelParaEntidade(viewModel);
            ConfigurarDadosAuditoriaCriacao(entity);

            var addedEntity = await _repository.AdicionarAsync(entity);
            return addedEntity != null ? ConverteEntidadeParaViewModel(addedEntity) : null;
        }

        public virtual async Task<TViewModel?> EditarAsync(TViewModel viewModel)
        {
            ArgumentNullException.ThrowIfNull(viewModel);

            var entity = ConverteViewModelParaEntidade(viewModel);
            ConfigurarDadosAuditoriaEdicao(entity);

            var editedEntity = await _repository.EditarAsync(entity);
            return editedEntity != null ? ConverteEntidadeParaViewModel(editedEntity) : null;
        }

        public virtual async Task ExcluirAsync(TViewModel viewModel)
        {
            ArgumentNullException.ThrowIfNull(viewModel);

            var entity = ConverteViewModelParaEntidade(viewModel);
            await _repository.ExcluirAsync(entity);
        }

        public virtual async Task ExcluirPorIdAsync(string id)
        {
            if (string.IsNullOrWhiteSpace(id))
                throw new ArgumentException("ID não pode ser nulo ou vazio", nameof(id));

            var entity = await _repository.BuscarPorIdAsync(id);
            if (entity != null)
            {
                await _repository.ExcluirAsync(entity);
            }
        }

        public virtual TViewModel ConverteEntidadeParaViewModel(TEntity entity)
        {
            ArgumentNullException.ThrowIfNull(entity);
            return _mapper.Map<TViewModel>(entity);
        }

        public virtual TEntity ConverteViewModelParaEntidade(TViewModel viewModel)
        {
            ArgumentNullException.ThrowIfNull(viewModel);
            return _mapper.Map<TEntity>(viewModel);
        }

        protected virtual void ConfigurarDadosAuditoriaCriacao(TEntity entity)
        {
            var entityType = typeof(TEntity);

            SetPropertyValue(entity, entityType, "DtaCadastro", DateTime.Now);
            SetPropertyValue(entity, entityType, "IdUsuarioGerador", IdUsuarioLogado);
            SetPropertyValue(entity, entityType, "FlgAtivo", true);
        }

        protected virtual void ConfigurarDadosAuditoriaEdicao(TEntity entity)
        {
            var entityType = typeof(TEntity);

            SetPropertyValue(entity, entityType, "DtaAlteracao", DateTime.Now);
            SetPropertyValue(entity, entityType, "IdUsuarioAlteracao", IdUsuarioLogado);
        }

        private static void SetPropertyValue(TEntity entity, Type entityType, string propertyName, object? value)
        {
            if (value == null) return;

            var property = entityType.GetProperty(propertyName);
            if (property != null && property.CanWrite)
            {
                property.SetValue(entity, value);
            }
        }
    }
}
