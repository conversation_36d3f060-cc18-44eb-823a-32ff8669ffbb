using ServiceClient.Interfaces.Generic;
using Shared.Entities.Client;
using Shared.ViewModels.Client;

namespace ServiceClient.Interfaces
{
    /// <summary>
    /// Interface para serviço de histórico de usuário no contexto Client (multi-tenant)
    /// </summary>
    public interface IHistoricoUsuarioClientService : IGenericClientService<HistoricoUsuarioClientViewModel, HistoricoUsuarioClient>
    {
        /// <summary>
        /// Registra uma ação no histórico do usuário
        /// </summary>
        /// <param name="metodo">Método que executou a ação</param>
        /// <param name="acao">Descrição da ação realizada</param>
        /// <param name="dadoAntigo">Dados antes da alteração</param>
        /// <param name="dadoNovo">Dados após a alteração</param>
        /// <param name="tipoOperacao">Tipo da operação (CREATE, READ, UPDATE, DELETE, etc.)</param>
        /// <param name="controller">Controller onde a ação foi executada</param>
        /// <returns>Task</returns>
        Task RegistrarAcao(string metodo, string acao, string dadoAntigo, string dadoNovo, 
            string tipoOperacao = "", string controller = "");

        /// <summary>
        /// Registra uma ação no histórico do usuário com informações de contexto HTTP
        /// </summary>
        /// <param name="metodo">Método que executou a ação</param>
        /// <param name="acao">Descrição da ação realizada</param>
        /// <param name="dadoAntigo">Dados antes da alteração</param>
        /// <param name="dadoNovo">Dados após a alteração</param>
        /// <param name="tipoOperacao">Tipo da operação</param>
        /// <param name="controller">Controller onde a ação foi executada</param>
        /// <param name="ipCliente">IP do cliente</param>
        /// <param name="userAgent">User Agent do cliente</param>
        /// <param name="duracaoMs">Duração da operação em milissegundos</param>
        /// <param name="status">Status da operação</param>
        /// <param name="informacoesAdicionais">Informações adicionais</param>
        /// <returns>Task</returns>
        Task RegistrarAcaoComContexto(string metodo, string acao, string dadoAntigo, string dadoNovo,
            string tipoOperacao = "", string controller = "", string? ipCliente = null, 
            string? userAgent = null, long? duracaoMs = null, string status = "SUCCESS", 
            string? informacoesAdicionais = null);

        /// <summary>
        /// Busca histórico por usuário
        /// </summary>
        /// <param name="idUsuario">ID do usuário</param>
        /// <returns>Lista de histórico do usuário</returns>
        Task<IEnumerable<HistoricoUsuarioClientViewModel>> BuscarPorUsuarioAsync(string idUsuario);

        /// <summary>
        /// Busca histórico por período
        /// </summary>
        /// <param name="dataInicio">Data de início</param>
        /// <param name="dataFim">Data de fim</param>
        /// <returns>Lista de histórico no período</returns>
        Task<IEnumerable<HistoricoUsuarioClientViewModel>> BuscarPorPeriodoAsync(DateTime dataInicio, DateTime dataFim);

        /// <summary>
        /// Busca histórico por tipo de operação
        /// </summary>
        /// <param name="tipoOperacao">Tipo da operação</param>
        /// <returns>Lista de histórico do tipo especificado</returns>
        Task<IEnumerable<HistoricoUsuarioClientViewModel>> BuscarPorTipoOperacaoAsync(string tipoOperacao);

        /// <summary>
        /// Busca histórico por controller
        /// </summary>
        /// <param name="controller">Nome do controller</param>
        /// <returns>Lista de histórico do controller</returns>
        Task<IEnumerable<HistoricoUsuarioClientViewModel>> BuscarPorControllerAsync(string controller);
    }
}
