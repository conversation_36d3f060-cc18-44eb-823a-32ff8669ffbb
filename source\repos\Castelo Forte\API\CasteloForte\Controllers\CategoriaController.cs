using Microsoft.AspNetCore.Mvc;
using ServiceClient.Interfaces;
using Shared.Enums;
using Shared.ViewModels.Client;
using System.ComponentModel.DataAnnotations;

namespace CasteloForte.Controllers
{
    /// <summary>
    /// Controller para operações com categorias
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class CategoriaController : ControllerBase
    {
        private readonly ICategoriaService _categoriaService;
        private readonly ILogger<CategoriaController> _logger;

        public CategoriaController(
            ICategoriaService categoriaService,
            ILogger<CategoriaController> logger)
        {
            _categoriaService = categoriaService;
            _logger = logger;
        }

        /// <summary>
        /// Busca todas as categorias com filtros opcionais
        /// </summary>
        /// <param name="ativa">Filtrar apenas categorias ativas (padrão: true)</param>
        /// <param name="tipo">Filtrar por tipo de categoria (opcional)</param>
        /// <param name="search">Buscar por nome (opcional)</param>
        /// <returns>Lista de categorias</returns>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<CategoriaViewModel?>>> BuscarTodos(
            [FromQuery] bool ativa = true,
            [FromQuery] TipoCategoria? tipo = null,
            [FromQuery] string? search = null)
        {
            try
            {
                _logger.LogInformation("Buscando categorias - Ativa: {Ativa}, Tipo: {Tipo}, Search: {Search}", ativa, tipo, search);

                var categorias = await _categoriaService.BuscarTodosAsync();

                // Aplicar filtros
                if (ativa)
                {
                    categorias = categorias.Where(c => c.Ativa);
                }

                if (tipo.HasValue)
                {
                    categorias = categorias.Where(c => c.Tipo == tipo.Value);
                }

                if (!string.IsNullOrWhiteSpace(search))
                {
                    categorias = categorias.Where(c => c.Nome.Contains(search, StringComparison.OrdinalIgnoreCase));
                }
                var ret = categorias.ToList();
                return Ok(ret);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao buscar categorias");
                return StatusCode(500, new { message = "Erro interno do servidor", details = ex.Message });
            }
        }

        /// <summary>
        /// Busca categoria por ID
        /// </summary>
        /// <param name="id">ID da categoria</param>
        /// <returns>Categoria encontrada</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<CategoriaViewModel?>> BuscarPorId(string id)
        {
            try
            {
                _logger.LogInformation("Buscando categoria por ID: {Id}", id);
                
                if (string.IsNullOrWhiteSpace(id))
                {
                    return BadRequest(new { message = "ID é obrigatório" });
                }

                var categoria = await _categoriaService.BuscarPorIdAsync(id);
                
                if (categoria == null)
                {
                    return NotFound(new { message = "Categoria não encontrada" });
                }

                return Ok(categoria);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao buscar categoria por ID: {Id}", id);
                return StatusCode(500, new { message = "Erro interno do servidor", details = ex.Message });
            }
        }

        /// <summary>
        /// Busca lista simplificada de categorias para dropdowns
        /// </summary>
        /// <param name="ativa">Filtrar apenas categorias ativas (padrão: true)</param>
        /// <param name="tipo">Filtrar por tipo de categoria (opcional)</param>
        /// <returns>Lista simplificada com id e nome</returns>
        [HttpGet("select")]
        public async Task<ActionResult<IEnumerable<object>>> BuscarParaSelect(
            [FromQuery] bool ativa = true,
            [FromQuery] TipoCategoria? tipo = null)
        {
            try
            {
                _logger.LogInformation("Buscando categorias para select - Ativa: {Ativa}, Tipo: {Tipo}", ativa, tipo);

                var categorias = await _categoriaService.BuscarTodosAsync();

                // Aplicar filtros
                if (ativa)
                {
                    categorias = categorias.Where(c => c.Ativa);
                }

                if (tipo.HasValue)
                {
                    categorias = categorias.Where(c => c.Tipo == tipo.Value);
                }

                // Retornar apenas id e nome
                var resultado = categorias.Select(c => new { id = c.Id, nome = c.Nome });

                return Ok(resultado);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao buscar categorias para select");
                return StatusCode(500, new { message = "Erro interno do servidor", details = ex.Message });
            }
        }

        /// <summary>
        /// Cria uma nova categoria
        /// </summary>
        /// <param name="viewModel">Dados da categoria</param>
        /// <returns>Categoria criada</returns>
        [HttpPost]
        public async Task<ActionResult<CategoriaViewModel?>> Criar([FromBody] CategoriaCreateUpdateViewModel viewModel)
        {
            try
            {
                _logger.LogInformation("Criando nova categoria: {Nome}", viewModel.Nome);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var categoria = await _categoriaService.CriarAsync(viewModel);
                return CreatedAtAction(nameof(BuscarPorId), new { id = categoria.Id }, categoria);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Dados inválidos ao criar categoria");
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao criar categoria");
                return StatusCode(500, new { message = "Erro interno do servidor", details = ex.Message });
            }
        }

        /// <summary>
        /// Atualiza uma categoria existente
        /// </summary>
        /// <param name="id">ID da categoria</param>
        /// <param name="viewModel">Dados atualizados</param>
        /// <returns>Categoria atualizada</returns>
        [HttpPut("{id}")]
        public async Task<ActionResult<CategoriaViewModel?>> Atualizar(string id, [FromBody] CategoriaCreateUpdateViewModel viewModel)
        {
            try
            {
                _logger.LogInformation("Atualizando categoria: {Id}", id);

                if (string.IsNullOrWhiteSpace(id))
                {
                    return BadRequest(new { message = "ID é obrigatório" });
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var categoria = await _categoriaService.AtualizarAsync(id, viewModel);
                return Ok(categoria);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Dados inválidos ao atualizar categoria: {Id}", id);
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao atualizar categoria: {Id}", id);
                return StatusCode(500, new { message = "Erro interno do servidor", details = ex.Message });
            }
        }


    }
}
