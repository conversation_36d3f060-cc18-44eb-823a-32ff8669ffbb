import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/models/user_model.dart';
import '../../../core/services/api_service.dart';
import '../../../core/services/logger_service.dart';
import '../../../core/services/secure_storage_service.dart';

/// Serviço de autenticação
class AuthService {
  static const String _userKey = 'user_data';
  static const String _tokenKey = 'auth_token';
  static UserModel? _currentUser;
  static String? _authToken;

  /// Obtém o usuário atual
  static UserModel? getCurrentUser() {
    return _currentUser;
  }

  /// Obtém o token de autenticação atual
  static String? getAuthToken() {
    return _authToken;
  }

  /// Inicializa o serviço de autenticação
  static Future<void> init() async {
    final prefs = await SharedPreferences.getInstance();
    final userData = prefs.getString(_userKey);

    if (userData != null) {
      try {
        _currentUser = UserModel.fromJson(json.decode(userData));
        LoggerService.success(
          'Usuário carregado com sucesso: ${_currentUser?.nome}',
        );
      } catch (e) {
        LoggerService.error('Erro ao carregar usuário: $e');
        // Erro ao decodificar os dados do usuário
        await logout();
      }
    } else {
      LoggerService.info(
        'Nenhum dado de usuário encontrado no armazenamento local',
      );
    }
  }

  /// Realiza o login do usuário
  static Future<UserModel> login(String cpf, String password) async {
    try {
      LoggerService.auth('Iniciando login para CPF: ${cpf.substring(0, 3)}***');

      // Testa a conectividade primeiro
      LoggerService.connectivity('Testando conectividade com a API...');
      final isConnected = await ApiService.testConnection();
      if (!isConnected) {
        throw Exception(
          'Não foi possível conectar com a API. Verifique a URL da API nas configurações.',
        );
      }
      LoggerService.success('Conectividade OK');

      // Remove formatação do CPF (mantém apenas números)
      final cpfLimpo = cpf.replaceAll(RegExp(r'[^0-9]'), '');

      // Cria a requisição de login no formato esperado pela API client
      final loginData = {
        'cpf': cpfLimpo,
        'senha': password,
        'lembrarLogin': false,
      };

      // Faz a chamada para a API
      final responseData = await ApiService.login(loginData);
      LoggerService.success('Resposta da API recebida: $responseData');

      // Verifica se a resposta foi bem-sucedida
      if (responseData['success'] != true) {
        throw Exception(responseData['message'] ?? 'Erro no login');
      }

      // Extrai os dados do usuário e token da resposta
      final data = responseData['data'];
      final token = data['token'];
      final userData = data['usuario'];

      // Cria o usuário a partir dos dados da API
      final user = UserModel(
        id: userData['id'],
        nome: userData['nome'],
        email: userData['email'],
        telefone: userData['telefone'] ?? userData['celular'],
        dataNascimento: userData['dtaNascimento'] != null
            ? DateTime.parse(userData['dtaNascimento'])
            : null,
      );

      // Salva os dados do usuário e token
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_userKey, json.encode(user.toJson()));
      await prefs.setString(_tokenKey, token);

      // Salva credenciais e token de forma segura
      await SecureStorageService.saveUserCredentials(
        cpf: cpfLimpo,
        email: user.email,
        nome: user.nome,
      );
      await SecureStorageService.saveAuthToken(token);

      // Atualiza as variáveis estáticas
      _currentUser = user;
      _authToken = token;

      // Define o token no ApiService para futuras requisições
      ApiService.setAuthToken(token);

      LoggerService.success('Login realizado com sucesso para: ${user.nome}');
      return user;
    } catch (e) {
      LoggerService.failure('Erro no login: $e');
      rethrow;
    }
  }

  /// Realiza o registro do usuário
  static Future<UserModel> register({
    required String nome,
    required String email,
    required String cpf,
    required String telefone,
    required String password,
    DateTime? dataNascimento,
  }) async {
    try {
      LoggerService.auth('Iniciando registro para: $email');

      // Testa a conectividade primeiro
      LoggerService.connectivity('Testando conectividade com a API...');
      final isConnected = await ApiService.testConnection();
      if (!isConnected) {
        throw Exception(
          'Não foi possível conectar com a API. Verifique a URL da API nas configurações.',
        );
      }
      LoggerService.success('Conectividade OK');

      // Remove formatação do CPF (mantém apenas números)
      final cpfLimpo = cpf.replaceAll(RegExp(r'[^0-9]'), '');

      // Valida se o CPF tem 11 dígitos
      if (cpfLimpo.length != 11) {
        throw Exception('CPF deve ter 11 dígitos');
      }

      // Cria a requisição de registro no formato esperado pela API admin
      final registerData = {
        'nome': nome,
        'email': email,
        'cpf': cpfLimpo,
        'celular': telefone,
        'dtaNascimento':
            dataNascimento?.toIso8601String() ??
            DateTime(
              2000,
              1,
              1,
            ).toIso8601String(), // Data padrão se não fornecida
        'senha': password,
        'flgTermosECondicoes': true,
        'flgDoisFatores': false,
      };

      // Faz a chamada para a API
      final responseData = await ApiService.register(registerData);
      LoggerService.success('Resposta da API recebida: $responseData');

      // Verifica se a resposta foi bem-sucedida
      if (responseData['success'] != true) {
        final errorMessage = responseData['message'] ?? 'Erro no cadastro';
        LoggerService.failure('Erro no cadastro: $errorMessage');
        throw Exception(errorMessage);
      }

      // Extrai os dados do usuário da resposta
      final userData = responseData['data'];
      if (userData == null) {
        throw Exception('Dados do usuário não retornados pela API');
      }

      // Token temporário - em produção, a API deveria retornar um token
      final token = 'temp_token_${DateTime.now().millisecondsSinceEpoch}';

      // Cria o usuário a partir dos dados da API
      final user = UserModel(
        id: userData['id']?.toString() ?? '',
        nome: userData['nome']?.toString() ?? '',
        email: userData['email']?.toString() ?? '',
        telefone: userData['celular']?.toString() ?? '',
        dataNascimento: userData['dtaCadastro'] != null
            ? DateTime.parse(userData['dtaCadastro'])
            : dataNascimento,
      );

      // Salva os dados do usuário e token
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_userKey, json.encode(user.toJson()));
      await prefs.setString(_tokenKey, token);

      // Salva credenciais e token de forma segura
      await SecureStorageService.saveUserCredentials(
        cpf: cpfLimpo,
        email: user.email,
        nome: user.nome,
      );
      await SecureStorageService.saveAuthToken(token);

      // Atualiza as variáveis estáticas
      _currentUser = user;
      _authToken = token;

      // Define o token no ApiService para futuras requisições
      ApiService.setAuthToken(token);

      LoggerService.success(
        'Registro realizado com sucesso para: ${user.nome}',
      );
      return user;
    } catch (e) {
      LoggerService.failure('Erro no registro: $e');
      rethrow;
    }
  }

  /// Realiza o logout do usuário
  static Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userKey);
    await prefs.remove(_tokenKey);

    // Remove dados seguros
    await SecureStorageService.clearAllSecureData();

    _currentUser = null;
    _authToken = null;

    // Remove token do ApiService
    ApiService.clearAuthToken();

    LoggerService.success('Logout realizado com sucesso');
  }

  /// Verifica se o usuário está autenticado
  static Future<bool> isAuthenticated() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.containsKey(_tokenKey);
  }

  /// Verifica se precisa de re-autenticação
  static Future<bool> needsReAuthentication() async {
    try {
      // Verifica se há credenciais armazenadas
      final hasCredentials = await SecureStorageService.hasStoredCredentials();
      if (!hasCredentials) return false;

      // Verifica se há token de autenticação
      final hasToken = await SecureStorageService.hasAuthToken();
      if (!hasToken) return false;

      // Verifica se o app foi fechado e reaberto
      final wasClosedAndReopened =
          await SecureStorageService.wasAppClosedAndReopened();

      LoggerService.info(
        'Verificação de re-autenticação: hasCredentials=$hasCredentials, hasToken=$hasToken, wasClosedAndReopened=$wasClosedAndReopened',
      );

      return wasClosedAndReopened;
    } catch (e) {
      LoggerService.error(
        'Erro ao verificar necessidade de re-autenticação: $e',
      );
      return false;
    }
  }

  /// Obtém informações para re-autenticação
  static Future<Map<String, dynamic>?> getReAuthenticationInfo() async {
    try {
      return await SecureStorageService.getUserCredentials();
    } catch (e) {
      LoggerService.error('Erro ao obter informações de re-autenticação: $e');
      return null;
    }
  }

  /// Restaura a sessão após re-autenticação bem-sucedida
  static Future<void> restoreSession() async {
    try {
      // Restaura o token do armazenamento seguro
      final token = await SecureStorageService.getAuthToken();
      if (token != null) {
        _authToken = token;
        ApiService.setAuthToken(token);
        LoggerService.success('Sessão restaurada com sucesso');
      }
    } catch (e) {
      LoggerService.error('Erro ao restaurar sessão: $e');
      rethrow;
    }
  }

  /// Atualiza os dados do usuário
  static Future<UserModel> updateUserData(UserModel updatedUser) async {
    // Simulação de atualização - em produção, isso seria uma chamada à API
    await Future.delayed(const Duration(seconds: 1));

    // Salva os dados atualizados
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userKey, json.encode(updatedUser.toJson()));

    _currentUser = updatedUser;
    return updatedUser;
  }
}
