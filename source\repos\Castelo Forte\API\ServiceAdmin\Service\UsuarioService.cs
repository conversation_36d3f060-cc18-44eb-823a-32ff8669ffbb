﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using RepositoryAdmin.Interfaces;
using ServiceAdmin.Interfaces;
using ServiceAdmin.Repository.Generic;
using Shared.Entities.Admin;
using Shared.Utils;
using Shared.ViewModels.Admin;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace ServiceAdmin.Service
{
    public class UsuarioService(
        IUsuarioRepository repository,
        IHttpContextAccessor httpContextAccessor,
        IMapper mapper) : GenericAdminService<UsuarioViewModel, Usuario>(repository, httpContextAccessor, mapper), IUsuarioService
    {
        IHttpContextAccessor _httpContextAccessor = httpContextAccessor;
        IUsuarioRepository _repository = repository;
        IMapper _mapper = mapper;

        #region CadastroUsuario
        public async Task<bool> CadastroUsuario(UsuarioViewModel model)
        {
            try
            {
                // Preenche automaticamente os campos que devem ser gerados pelo sistema
                await PreencherCamposAutomaticos(model);

                await ValidaUsuario(model);
                model.Senha = Criptografias.Encriptar(model.Senha, false);
                model.FlgAtivo = true;
                model.DtaCadastro = DateTime.Now;

                UsuarioViewModel usuario = await AdicionarAsync(model);
                return usuario != null;
            }
            catch (Exception)
            {
                throw;
            }
        }

        /// <summary>
        /// Cadastra um novo usuário usando o ViewModel específico para cadastro
        /// </summary>
        /// <param name="cadastroModel">Dados do usuário para cadastro</param>
        /// <returns>True se o cadastro foi bem-sucedido</returns>
        public async Task<bool> CadastroUsuarioNovo(CadastroUsuarioViewModel cadastroModel)
        {
            try
            {
                if (cadastroModel == null)
                    throw new ArgumentNullException(nameof(cadastroModel), "Dados de cadastro são obrigatórios");

                // Converte o CadastroUsuarioViewModel para UsuarioViewModel
                var usuarioModel = cadastroModel.ToUsuarioViewModel();

                // Usa o método existente de cadastro
                return await CadastroUsuario(usuarioModel);
            }
            catch (Exception)
            {
                throw;
            }
        }

        /// <summary>
        /// Cadastra um novo usuário usando o ViewModel específico para cadastro e retorna o usuário criado
        /// </summary>
        /// <param name="cadastroModel">Dados do usuário para cadastro</param>
        /// <returns>UsuarioViewModel com ID preenchido se o cadastro foi bem-sucedido, null caso contrário</returns>
        public async Task<UsuarioViewModel?> CadastroUsuarioNovoComRetorno(CadastroUsuarioViewModel cadastroModel)
        {
            try
            {
                if (cadastroModel == null)
                    throw new ArgumentNullException(nameof(cadastroModel), "Dados de cadastro são obrigatórios");

                // Converte o CadastroUsuarioViewModel para UsuarioViewModel
                var usuarioModel = cadastroModel.ToUsuarioViewModel();

                // Preenche automaticamente os campos que devem ser gerados pelo sistema
                await PreencherCamposAutomaticos(usuarioModel);

                // Valida o usuário
                await ValidaUsuario(usuarioModel);

                // Criptografa a senha
                usuarioModel.Senha = Criptografias.Encriptar(usuarioModel.Senha, false);
                usuarioModel.FlgAtivo = true;
                usuarioModel.DtaCadastro = DateTime.Now;

                // Adiciona o usuário e retorna com o ID preenchido
                var usuarioCriado = await AdicionarAsync(usuarioModel);

                return usuarioCriado;
            }
            catch (Exception)
            {
                throw;
            }
        }
        #endregion

        #region ValidarUsuario
        public async Task ValidaUsuario(UsuarioViewModel model)
        {
            if (model == null)
                throw new ArgumentNullException(nameof(model), "O objeto de usuário não pode ser nulo");

            if (string.IsNullOrEmpty(model.Cpf))
                throw new ArgumentNullException(nameof(model.Cpf), "O CPF é um campo obrigatório, que não pode ser nulo");

            if (string.IsNullOrEmpty(model.Email))
                throw new ArgumentNullException(nameof(model.Email), "O Email é um campo obrigatório, que não pode ser nulo");

            if (string.IsNullOrEmpty(model.Celular))
                throw new ArgumentNullException(nameof(model.Celular), "O Celular é um campo obrigatório, que não pode ser nulo");

            if (string.IsNullOrEmpty(model.Senha))
                throw new ArgumentNullException(nameof(model.Senha), "A Senha é um campo obrigatório, que não pode ser nula");

            if (model.DtaNascimento == DateTime.MinValue)
                throw new ArgumentException("A Data de Nascimento é obrigatória e deve ser válida", nameof(model.DtaNascimento));

            if (model.FlgTermosECondicoes != true)
                throw new ArgumentException("A aceitação dos Termos e Condições é obrigatória", nameof(model.FlgTermosECondicoes));

            if (!ValidacaoDeDados.ValidarEmail(model.Email))
                throw new ArgumentException("O Email fornecido não possui um formato válido", nameof(model.Email));

            if (!ValidacaoDeDados.ValidarCpf(model.Cpf))
                throw new ArgumentException("O CPF fornecido não é válido", nameof(model.Cpf));

            if (!ValidacaoDeDados.ValidarTelefoneCelular(model.Celular))
                throw new ArgumentException("O Celular fornecido não possui um formato válido", nameof(model.Celular));

            if (!ValidacaoDeDados.ValidarDataNascimento(model.DtaNascimento))
                throw new ArgumentException("A Data de Nascimento deve ser anterior à data atual e o usuário deve ter pelo menos 18 anos", nameof(model.DtaNascimento));

            if (!ValidacaoDeDados.ValidarSenha(model.Senha))
                throw new ArgumentException("A Senha deve ter pelo menos 8 caracteres, incluindo pelo menos uma letra maiúscula, uma minúscula, um número e um caractere especial", nameof(model.Senha));
        }
        #endregion

        #region PreencherCamposAutomaticos
        /// <summary>
        /// Preenche automaticamente os campos que devem ser gerados pelo sistema
        /// </summary>
        /// <param name="model">Modelo do usuário</param>
        private async Task PreencherCamposAutomaticos(UsuarioViewModel model)
        {
            try
            {
                // Gera o nome da base de dados se não estiver preenchido
                if (string.IsNullOrEmpty(model.NomeBaseDados))
                {
                    model.NomeBaseDados = Utils.GerarNomeBaseDados(model.Cpf);
                }

                // Gera um token de acesso temporário se não estiver preenchido
                if (string.IsNullOrEmpty(model.TokenAcesso))
                {
                    model.TokenAcesso = Guid.NewGuid().ToString("N")[..16]; // Token temporário de 16 caracteres
                    model.DtaTokenAcessoGerado = DateTime.UtcNow;
                }

                // A connection string será gerada automaticamente no primeiro login
                // Não definimos um placeholder aqui para evitar problemas de descriptografia
                if (string.IsNullOrEmpty(model.ConnectionString))
                {
                    // Deixa vazio - será gerado automaticamente no primeiro login
                    model.ConnectionString = string.Empty;
                }

                // Define um perfil financeiro padrão se não estiver preenchido
                if (string.IsNullOrEmpty(model.IdPerfilFinanceiro))
                {
                    model.IdPerfilFinanceiro = "PERFIL_PADRAO"; // Perfil padrão que pode ser atualizado posteriormente
                }

                // Define a data de aceite dos termos se o usuário aceitou
                if (model.FlgTermosECondicoes && !model.DtaTermosECondicoes.HasValue)
                {
                    model.DtaTermosECondicoes = DateTime.UtcNow;
                }

                await Task.CompletedTask; // Para manter a assinatura async
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao preencher campos automáticos: {ex.Message}", ex);
            }
        }
        #endregion

        #region Novos Métodos de Consulta

        /// <summary>
        /// Busca todos os usuários com seus dados completos
        /// </summary>
        /// <returns>Lista de todos os usuários</returns>
        public async Task<IEnumerable<UsuarioViewModel>> BuscarTodosUsuariosComDadosAsync()
        {
            try
            {
                return await BuscarTodosAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar todos os usuários: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca usuários com filtros e paginação
        /// </summary>
        /// <param name="filtro">Filtros para busca</param>
        /// <returns>Resultado paginado de usuários</returns>
        public async Task<ResultadoPaginadoViewModel<UsuarioViewModel>> BuscarUsuariosFiltradosAsync(FiltroUsuarioViewModel filtro)
        {
            try
            {
                var usuarios = await BuscarTodosAsync();
                var query = usuarios.AsQueryable();

                // Aplicar filtros
                if (!string.IsNullOrWhiteSpace(filtro.Nome))
                    query = query.Where(u => u.Nome.Contains(filtro.Nome, StringComparison.OrdinalIgnoreCase));

                if (!string.IsNullOrWhiteSpace(filtro.Email))
                    query = query.Where(u => u.Email.Contains(filtro.Email, StringComparison.OrdinalIgnoreCase));

                if (filtro.FlgAtivo.HasValue)
                    query = query.Where(u => u.FlgAtivo == filtro.FlgAtivo.Value);

                if (filtro.DataCadastroInicio.HasValue)
                    query = query.Where(u => u.DtaCadastro >= filtro.DataCadastroInicio.Value);

                if (filtro.DataCadastroFim.HasValue)
                    query = query.Where(u => u.DtaCadastro <= filtro.DataCadastroFim.Value);

                if (!string.IsNullOrWhiteSpace(filtro.TextoBusca))
                {
                    query = query.Where(u =>
                        u.Nome.Contains(filtro.TextoBusca, StringComparison.OrdinalIgnoreCase) ||
                        u.Email.Contains(filtro.TextoBusca, StringComparison.OrdinalIgnoreCase));
                }

                // Aplicar ordenação
                query = filtro.OrdenarPor?.ToLower() switch
                {
                    "nome" => filtro.DirecaoOrdenacao?.ToUpper() == "ASC" ?
                        query.OrderBy(u => u.Nome) : query.OrderByDescending(u => u.Nome),
                    "email" => filtro.DirecaoOrdenacao?.ToUpper() == "ASC" ?
                        query.OrderBy(u => u.Email) : query.OrderByDescending(u => u.Email),
                    "dtacadastro" => filtro.DirecaoOrdenacao?.ToUpper() == "ASC" ?
                        query.OrderBy(u => u.DtaCadastro) : query.OrderByDescending(u => u.DtaCadastro),
                    _ => query.OrderByDescending(u => u.DtaCadastro)
                };

                var totalRegistros = query.Count();
                var dadosPaginados = query
                    .Skip((filtro.Pagina - 1) * filtro.ItensPorPagina)
                    .Take(filtro.ItensPorPagina)
                    .ToList();

                return new ResultadoPaginadoViewModel<UsuarioViewModel>(
                    dadosPaginados, totalRegistros, filtro.Pagina, filtro.ItensPorPagina);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar usuários filtrados: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca usuários ativos
        /// </summary>
        /// <returns>Lista de usuários ativos</returns>
        public async Task<IEnumerable<UsuarioViewModel>> BuscarUsuariosAtivosAsync()
        {
            try
            {
                var usuarios = await BuscarTodosAsync();
                return usuarios.Where(u => u.FlgAtivo);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar usuários ativos: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca usuários inativos
        /// </summary>
        /// <returns>Lista de usuários inativos</returns>
        public async Task<IEnumerable<UsuarioViewModel>> BuscarUsuariosInativosAsync()
        {
            try
            {
                var usuarios = await BuscarTodosAsync();
                return usuarios.Where(u => !u.FlgAtivo);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar usuários inativos: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Obtém estatísticas dos usuários
        /// </summary>
        /// <returns>Estatísticas dos usuários</returns>
        public async Task<object> ObterEstatisticasUsuariosAsync()
        {
            try
            {
                var usuarios = await BuscarTodosAsync();
                var totalUsuarios = usuarios.Count();
                var usuariosAtivos = usuarios.Count(u => u.FlgAtivo);
                var usuariosInativos = usuarios.Count(u => !u.FlgAtivo);

                var hoje = DateTime.Today;
                var usuariosCadastradosHoje = usuarios.Count(u => u.DtaCadastro?.Date == hoje);
                var usuariosCadastradosUltimos7Dias = usuarios.Count(u => u.DtaCadastro >= hoje.AddDays(-7));
                var usuariosCadastradosUltimos30Dias = usuarios.Count(u => u.DtaCadastro >= hoje.AddDays(-30));

                return new
                {
                    TotalUsuarios = totalUsuarios,
                    UsuariosAtivos = usuariosAtivos,
                    UsuariosInativos = usuariosInativos,
                    PercentualAtivos = totalUsuarios > 0 ? Math.Round((double)usuariosAtivos / totalUsuarios * 100, 2) : 0,
                    UsuariosCadastradosHoje = usuariosCadastradosHoje,
                    UsuariosCadastradosUltimos7Dias = usuariosCadastradosUltimos7Dias,
                    UsuariosCadastradosUltimos30Dias = usuariosCadastradosUltimos30Dias,
                    DataUltimaAtualizacao = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao obter estatísticas dos usuários: {ex.Message}", ex);
            }
        }

        #endregion
    }
}
