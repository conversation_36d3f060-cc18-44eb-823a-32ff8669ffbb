using Shared.Enums;
using System.ComponentModel.DataAnnotations;

namespace Shared.ViewModels.Client
{
    /// <summary>
    /// ViewModel para categoria
    /// </summary>
    public class CategoriaViewModel
    {
        /// <summary>
        /// ID da categoria
        /// </summary>
        public string Id { get; set; } = "";

        /// <summary>
        /// Nome da categoria
        /// </summary>
        [Required(ErrorMessage = "Nome é obrigatório")]
        [StringLength(100, ErrorMessage = "Nome deve ter no máximo 100 caracteres")]
        public string Nome { get; set; } = "";

        /// <summary>
        /// Descrição da categoria
        /// </summary>
        [StringLength(500, ErrorMessage = "Descrição deve ter no máximo 500 caracteres")]
        public string Descricao { get; set; } = "";

        /// <summary>
        /// Tipo da categoria
        /// </summary>
        [Required(ErrorMessage = "Tipo é obrigatório")]
        public TipoCategoria Tipo { get; set; } = TipoCategoria.Despesa;

        /// <summary>
        /// Cor da categoria em formato hexadecimal (ex: "#FFFFFF")
        /// </summary>
        public string Cor { get; set; } = "#FFFFFF";

        /// <summary>
        /// Código do ícone da categoria
        /// </summary>
        public int Icone { get; set; } = 0xE7FD;

        /// <summary>
        /// Indica se a categoria está ativa
        /// </summary>
        public bool Ativa { get; set; } = true;

        /// <summary>
        /// Data de criação
        /// </summary>
        public DateTime DataCriacao { get; set; } = DateTime.Now;

        /// <summary>
        /// Data da última alteração
        /// </summary>
        public DateTime? DataAlteracao { get; set; }

        /// <summary>
        /// Ordem de exibição
        /// </summary>
        public int Ordem { get; set; } = 0;



        /// <summary>
        /// Limite de gastos para esta categoria
        /// </summary>
        public decimal? LimiteGastos { get; set; }

        /// <summary>
        /// Período do limite
        /// </summary>
        public PeriodoLimite? PeriodoLimite { get; set; }

        /// <summary>
        /// Observações adicionais
        /// </summary>
        public string? Observacoes { get; set; }

        /// <summary>
        /// Número de transações associadas
        /// </summary>
        public int NumeroTransacoes { get; set; } = 0;

        /// <summary>
        /// Número de metas associadas
        /// </summary>
        public int NumeroMetas { get; set; } = 0;

        /// <summary>
        /// Valor total gasto na categoria (período atual)
        /// </summary>
        public decimal ValorGasto { get; set; } = 0;

        /// <summary>
        /// Percentual do limite utilizado
        /// </summary>
        public decimal? PercentualLimite => LimiteGastos > 0 ? (ValorGasto / LimiteGastos.Value) * 100 : null;

        /// <summary>
        /// Indica se o limite foi ultrapassado
        /// </summary>
        public bool LimiteUltrapassado => LimiteGastos.HasValue && ValorGasto > LimiteGastos.Value;
    }

    /// <summary>
    /// ViewModel para criação/edição de categoria
    /// </summary>
    public class CategoriaCreateUpdateViewModel
    {
        /// <summary>
        /// Nome da categoria
        /// </summary>
        [Required(ErrorMessage = "Nome é obrigatório")]
        [StringLength(100, MinimumLength = 2, ErrorMessage = "Nome deve ter entre 2 e 100 caracteres")]
        public string Nome { get; set; } = "";

        /// <summary>
        /// Descrição da categoria
        /// </summary>
        [StringLength(500, ErrorMessage = "Descrição deve ter no máximo 500 caracteres")]
        public string Descricao { get; set; } = "";

        /// <summary>
        /// Tipo da categoria
        /// </summary>
        [Required(ErrorMessage = "Tipo é obrigatório")]
        public TipoCategoria Tipo { get; set; } = TipoCategoria.Despesa;

        /// <summary>
        /// Cor da categoria em formato hexadecimal (ex: "#FFFFFF")
        /// </summary>
        [Required(ErrorMessage = "Cor é obrigatória")]
        [RegularExpression(@"^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", ErrorMessage = "Cor deve estar no formato hexadecimal (#RRGGBB ou #RGB)")]
        public string Cor { get; set; } = "#FFFFFF";

        /// <summary>
        /// Código do ícone da categoria
        /// </summary>
        public int Icone { get; set; } = 0xE7FD;

        /// <summary>
        /// Ordem de exibição
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "Ordem deve ser um número positivo")]
        public int Ordem { get; set; } = 0;



        /// <summary>
        /// Limite de gastos para esta categoria
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "Limite deve ser um valor positivo")]
        public decimal? LimiteGastos { get; set; }

        /// <summary>
        /// Período do limite
        /// </summary>
        public PeriodoLimite? PeriodoLimite { get; set; }

        /// <summary>
        /// Observações adicionais
        /// </summary>
        [StringLength(1000, ErrorMessage = "Observações devem ter no máximo 1000 caracteres")]
        public string? Observacoes { get; set; }
    }

    /// <summary>
    /// ViewModel para listagem paginada de categorias
    /// </summary>
    public class CategoriaListViewModel
    {
        /// <summary>
        /// Lista de categorias
        /// </summary>
        public IEnumerable<CategoriaViewModel> Categorias { get; set; } = new List<CategoriaViewModel>();

        /// <summary>
        /// Número total de registros
        /// </summary>
        public int Total { get; set; } = 0;

        /// <summary>
        /// Página atual
        /// </summary>
        public int Pagina { get; set; } = 1;

        /// <summary>
        /// Tamanho da página
        /// </summary>
        public int TamanhoPagina { get; set; } = 10;

        /// <summary>
        /// Número total de páginas
        /// </summary>
        public int TotalPaginas => (int)Math.Ceiling((double)Total / TamanhoPagina);

        /// <summary>
        /// Indica se há página anterior
        /// </summary>
        public bool TemPaginaAnterior => Pagina > 1;

        /// <summary>
        /// Indica se há próxima página
        /// </summary>
        public bool TemProximaPagina => Pagina < TotalPaginas;
    }
}
