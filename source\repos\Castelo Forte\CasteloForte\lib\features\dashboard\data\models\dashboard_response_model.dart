import 'conta_model.dart';
import 'cartao_model.dart';
import 'lancamento_model.dart';
import 'categoria_model.dart';

/// Modelo de resposta principal da dashboard
class DashboardResponseModel {
  final double valorTotal;
  final bool exibirQuestionarioPerfil;
  final List<ContaModel> contas;
  final List<CartaoModel> cartoes;
  final List<LancamentoModel> ultimosLancamentos;
  final List<CategoriaModel> categorias;

  const DashboardResponseModel({
    required this.valorTotal,
    required this.exibirQuestionarioPerfil,
    required this.contas,
    required this.cartoes,
    required this.ultimosLancamentos,
    required this.categorias,
  });

  /// Cria uma instância a partir de JSON
  factory DashboardResponseModel.fromJson(Map<String, dynamic> json) {
    return DashboardResponseModel(
      valorTotal: (json['valorTotal'] as num?)?.toDouble() ?? 0.0,
      exibirQuestionarioPerfil: json['exibirQuestionarioPerfil'] as bool? ?? true,
      contas: (json['contas'] as List<dynamic>?)
              ?.map((conta) => ContaModel.fromJson(conta as Map<String, dynamic>))
              .toList() ??
          [],
      cartoes: (json['cartoes'] as List<dynamic>?)
              ?.map((cartao) => CartaoModel.fromJson(cartao as Map<String, dynamic>))
              .toList() ??
          [],
      ultimosLancamentos: (json['ultimosLancamentos'] as List<dynamic>?)
              ?.map((lancamento) => LancamentoModel.fromJson(lancamento as Map<String, dynamic>))
              .toList() ??
          [],
      categorias: (json['categorias'] as List<dynamic>?)
              ?.map((categoria) => CategoriaModel.fromJson(categoria as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  /// Converte para JSON
  Map<String, dynamic> toJson() {
    return {
      'valorTotal': valorTotal,
      'exibirQuestionarioPerfil': exibirQuestionarioPerfil,
      'contas': contas.map((conta) => conta.toJson()).toList(),
      'cartoes': cartoes.map((cartao) => cartao.toJson()).toList(),
      'ultimosLancamentos': ultimosLancamentos.map((lancamento) => lancamento.toJson()).toList(),
      'categorias': categorias.map((categoria) => categoria.toJson()).toList(),
    };
  }

  /// Retorna apenas contas ativas
  List<ContaModel> get contasAtivas => contas.where((conta) => conta.ativa).toList();

  /// Retorna apenas cartões ativos
  List<CartaoModel> get cartoesAtivos => cartoes.where((cartao) => cartao.ativo).toList();

  /// Retorna o valor total formatado em reais
  String get valorTotalFormatado {
    return 'R\$ ${valorTotal.toStringAsFixed(2).replaceAll('.', ',')}';
  }

  /// Retorna as categorias de receita
  List<CategoriaModel> get categoriasReceita => 
      categorias.where((categoria) => categoria.isReceita).toList();

  /// Retorna as categorias de despesa
  List<CategoriaModel> get categoriasDespesa => 
      categorias.where((categoria) => categoria.isDespesa).toList();

  /// Cria uma cópia com campos modificados
  DashboardResponseModel copyWith({
    double? valorTotal,
    bool? exibirQuestionarioPerfil,
    List<ContaModel>? contas,
    List<CartaoModel>? cartoes,
    List<LancamentoModel>? ultimosLancamentos,
    List<CategoriaModel>? categorias,
  }) {
    return DashboardResponseModel(
      valorTotal: valorTotal ?? this.valorTotal,
      exibirQuestionarioPerfil: exibirQuestionarioPerfil ?? this.exibirQuestionarioPerfil,
      contas: contas ?? this.contas,
      cartoes: cartoes ?? this.cartoes,
      ultimosLancamentos: ultimosLancamentos ?? this.ultimosLancamentos,
      categorias: categorias ?? this.categorias,
    );
  }

  @override
  String toString() {
    return 'DashboardResponseModel(valorTotal: $valorTotal, exibirQuestionarioPerfil: $exibirQuestionarioPerfil, contas: ${contas.length}, cartoes: ${cartoes.length}, ultimosLancamentos: ${ultimosLancamentos.length}, categorias: ${categorias.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DashboardResponseModel &&
        other.valorTotal == valorTotal &&
        other.exibirQuestionarioPerfil == exibirQuestionarioPerfil &&
        other.contas == contas &&
        other.cartoes == cartoes &&
        other.ultimosLancamentos == ultimosLancamentos &&
        other.categorias == categorias;
  }

  @override
  int get hashCode {
    return Object.hash(
      valorTotal,
      exibirQuestionarioPerfil,
      contas,
      cartoes,
      ultimosLancamentos,
      categorias,
    );
  }
}
