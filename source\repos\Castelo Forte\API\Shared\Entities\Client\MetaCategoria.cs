using MongoDB.Bson.Serialization.Attributes;
using Shared.Entities.Base;
using System.ComponentModel.DataAnnotations;

namespace Shared.Entities.Client
{
    /// <summary>
    /// Entidade que representa o relacionamento muitos-para-muitos entre Meta e Categoria
    /// </summary>
    public class MetaCategoria : BaseEntidade
    {
        /// <summary>
        /// ID da meta
        /// </summary>
        [Required]
        public string IdMeta { get; set; } = "";

        /// <summary>
        /// ID da categoria
        /// </summary>
        [Required]
        public string IdCategoria { get; set; } = "";

        /// <summary>
        /// Peso da categoria na meta (0-100%)
        /// Usado para calcular quanto cada categoria contribui para o progresso da meta
        /// </summary>
        [Range(0, 100)]
        public decimal PesoCategoria { get; set; } = 100;

        /// <summary>
        /// Valor específico desta categoria para a meta
        /// Se não definido, usa o peso para calcular proporcionalmente
        /// </summary>
        public decimal? ValorEspecifico { get; set; }

        /// <summary>
        /// Progresso atual desta categoria específica na meta
        /// </summary>
        public decimal ProgressoCategoria { get; set; } = 0;

        /// <summary>
        /// Data de associação da categoria à meta
        /// </summary>
        [DataType(DataType.Date)]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime DataAssociacao { get; set; } = DateTime.Now;

        /// <summary>
        /// Observações específicas sobre esta associação
        /// </summary>
        public string? Observacoes { get; set; }
    }
}
