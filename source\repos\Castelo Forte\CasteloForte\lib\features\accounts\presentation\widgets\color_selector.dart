import 'package:flutter/material.dart';
import '../../data/constants/account_constants.dart';

/// Widget para seleção de cores de conta
class ColorSelector extends StatelessWidget {
  final String? selectedColor;
  final Function(String) onColorSelected;
  final String label;

  const ColorSelector({
    super.key,
    required this.selectedColor,
    required this.onColorSelected,
    this.label = '<PERSON><PERSON> <PERSON> Conta',
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF16213E),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(
              color: const Color(0xFF4ECDC4).withValues(alpha: 0.3),
            ),
          ),
          child: Column(
            children: [
              // Cor selecionada atual
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF4ECDC4).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: AccountConstants.getColorByName(selectedColor),
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(color: Colors.white24),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      selectedColor ?? 'blue',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                    const Spacer(),
                    const Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.white70,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              // Grid de cores
              GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 8,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemCount: AccountConstants.colorNames.length,
                itemBuilder: (context, index) {
                  final colorName = AccountConstants.colorNames[index];
                  final color = AccountConstants.availableColors[colorName]!;
                  final isSelected = selectedColor == colorName;

                  return GestureDetector(
                    onTap: () => onColorSelected(colorName),
                    child: Container(
                      decoration: BoxDecoration(
                        color: color,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isSelected ? Colors.white : Colors.transparent,
                          width: isSelected ? 3 : 1,
                        ),
                      ),
                      child: isSelected
                          ? const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 16,
                            )
                          : null,
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}
