﻿using Shared.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepositoryClient.Cache.Interface
{
    public interface ITenantCache
    {
        void SetTenant(string IdTenant, string connectionString, string nomeBaseDados);
        TenantInfo? GetTenant(string IdTenant);
        void RemoveTenant(string IdTenant);
        bool HasTenant(string IdTenant);
    }
}
