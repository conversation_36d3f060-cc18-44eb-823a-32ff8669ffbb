import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// Widget para seções do dashboard com cabeçalho e ação "Ver todas"
class DashboardSection extends StatelessWidget {
  final String title;
  final Widget content;
  final VoidCallback? onSeeAll;
  final Widget? footer;

  const DashboardSection({
    super.key,
    required this.title,
    required this.content,
    this.onSeeAll,
    this.footer,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Cabeçalho da seção
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (onSeeAll != null)
              GestureDetector(
                onTap: onSeeAll,
                child: const Text(
                  'Ver todas',
                  style: TextStyle(
                    color: AppTheme.goldColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
          ],
        ),

        const SizedBox(height: 16),

        // Conteúdo da seção
        content,

        // Footer opcional (ex: botão "Ver mais")
        if (footer != null) ...[const SizedBox(height: 12), footer!],
      ],
    );
  }
}

/// Widget para botão "Ver mais"
class SeeMoreButton extends StatelessWidget {
  final VoidCallback onPressed;
  final String text;

  const SeeMoreButton({
    super.key,
    required this.onPressed,
    this.text = 'Ver mais',
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: TextButton(
        onPressed: onPressed,
        style: TextButton.styleFrom(
          foregroundColor: AppTheme.goldColor,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              text,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
            const SizedBox(width: 4),
            const Icon(Icons.arrow_forward_ios, size: 12),
          ],
        ),
      ),
    );
  }
}

/// Widget para lista horizontal de cards com scroll otimizado
class HorizontalCardList extends StatefulWidget {
  final List<Widget> cards;
  final double height;

  const HorizontalCardList({super.key, required this.cards, this.height = 120});

  @override
  State<HorizontalCardList> createState() => _HorizontalCardListState();
}

class _HorizontalCardListState extends State<HorizontalCardList> {
  final ScrollController _scrollController = ScrollController();
  bool _showLeftIndicator = false;
  bool _showRightIndicator = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_updateIndicators);
    // Verifica se há necessidade de indicadores após o build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateIndicators();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_updateIndicators);
    _scrollController.dispose();
    super.dispose();
  }

  void _updateIndicators() {
    if (!mounted) return;

    final position = _scrollController.position;
    setState(() {
      _showLeftIndicator = position.pixels > 0;
      _showRightIndicator = position.pixels < position.maxScrollExtent;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.height,
      child: Stack(
        children: [
          // Lista horizontal principal
          ListView.builder(
            controller: _scrollController,
            scrollDirection: Axis.horizontal,
            physics: const BouncingScrollPhysics(), // Scroll suave e responsivo
            padding: const EdgeInsets.only(
              left: 4,
              right: 20,
            ), // Padding extra à direita
            itemCount: widget.cards.length,
            itemBuilder: (context, index) {
              // Adiciona padding extra ao último item para garantir visibilidade completa
              if (index == widget.cards.length - 1) {
                return Padding(
                  padding: const EdgeInsets.only(right: 4),
                  child: widget.cards[index],
                );
              }
              return widget.cards[index];
            },
          ),

          // Indicador de scroll à esquerda
          if (_showLeftIndicator)
            Positioned(
              left: 0,
              top: 0,
              bottom: 0,
              child: Container(
                width: 20,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    colors: [
                      Colors.black.withValues(alpha: 0.1),
                      Colors.transparent,
                    ],
                  ),
                ),
                child: const Center(
                  child: Icon(
                    Icons.chevron_left,
                    color: AppTheme.goldColor,
                    size: 16,
                  ),
                ),
              ),
            ),

          // Indicador de scroll à direita
          if (_showRightIndicator)
            Positioned(
              right: 0,
              top: 0,
              bottom: 0,
              child: Container(
                width: 20,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.centerRight,
                    end: Alignment.centerLeft,
                    colors: [
                      Colors.black.withValues(alpha: 0.1),
                      Colors.transparent,
                    ],
                  ),
                ),
                child: const Center(
                  child: Icon(
                    Icons.chevron_right,
                    color: AppTheme.goldColor,
                    size: 16,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Widget para conexão bancária
class BankConnectionCard extends StatelessWidget {
  final VoidCallback? onConnect;

  const BankConnectionCard({super.key, this.onConnect});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.navyBlueColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: const [
              Icon(
                Icons.account_balance_wallet_outlined,
                color: AppTheme.goldColor,
                size: 24,
              ),
              SizedBox(width: 8),
              Icon(Icons.link, color: AppTheme.goldColor, size: 20),
              SizedBox(width: 8),
              Icon(
                Icons.account_balance_outlined,
                color: AppTheme.goldColor,
                size: 24,
              ),
            ],
          ),
          const SizedBox(height: 16),
          const Text(
            'Conexão Bancária',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          const Text(
            'Conecte seus bancos e importe seus\nlançamentos.',
            style: TextStyle(color: Colors.white70, fontSize: 14),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: onConnect,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.goldColor,
                foregroundColor: AppTheme.navyBlueColor,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Começar agora',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
