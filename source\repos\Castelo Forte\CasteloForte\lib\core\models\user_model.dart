/// Modelo de usuário
class UserModel {
  final String id;
  final String nome;
  final String email;
  final String telefone;
  final DateTime? dataNascimento;

  UserModel({
    required this.id,
    required this.nome,
    required this.email,
    required this.telefone,
    this.dataNascimento,
  });

  /// Cria um UserModel a partir de um Map
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      nome: json['nome'],
      email: json['email'],
      telefone: json['telefone'],
      dataNascimento: json['dataNascimento'] != null
          ? DateTime.parse(json['dataNascimento'])
          : null,
    );
  }

  /// Converte o UserModel para um Map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nome': nome,
      'email': email,
      'telefone': telefone,
      'dataNascimento': dataNascimento?.toIso8601String(),
    };
  }

  /// Cria uma cópia do UserModel com os campos atualizados
  UserModel copyWith({
    String? id,
    String? nome,
    String? email,
    String? telefone,
    DateTime? dataNascimento,
  }) {
    return UserModel(
      id: id ?? this.id,
      nome: nome ?? this.nome,
      email: email ?? this.email,
      telefone: telefone ?? this.telefone,
      dataNascimento: dataNascimento ?? this.dataNascimento,
    );
  }
}
