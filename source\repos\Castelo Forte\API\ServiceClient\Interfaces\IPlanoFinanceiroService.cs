using ServiceClient.Interfaces.Generic;
using Shared.Entities.Client;
using Shared.ViewModels.Client;

namespace ServiceClient.Interfaces
{
    /// <summary>
    /// Interface para serviço de Plano Financeiro no contexto Client (multi-tenant)
    /// </summary>
    public interface IPlanoFinanceiroService : IGenericClientService<PlanoFinanceiroViewModel, PlanoFinanceiro>
    {
        /// <summary>
        /// Busca planos financeiros por título
        /// </summary>
        /// <param name="titulo">Título do plano</param>
        /// <returns>Lista de planos com o título especificado</returns>
        Task<IEnumerable<PlanoFinanceiroViewModel>> BuscarPorTituloAsync(string titulo);

        /// <summary>
        /// Busca planos financeiros ativos
        /// </summary>
        /// <returns>Lista de planos ativos</returns>
        Task<IEnumerable<PlanoFinanceiroViewModel>> BuscarAtivosAsync();

        /// <summary>
        /// Busca planos financeiros por período de criação
        /// </summary>
        /// <param name="dataInicio">Data de início</param>
        /// <param name="dataFim">Data de fim</param>
        /// <returns>Lista de planos criados no período</returns>
        Task<IEnumerable<PlanoFinanceiroViewModel>> BuscarPorPeriodoCriacaoAsync(DateTime dataInicio, DateTime dataFim);

        /// <summary>
        /// Duplica um plano financeiro existente
        /// </summary>
        /// <param name="idPlano">ID do plano a ser duplicado</param>
        /// <param name="novoTitulo">Novo título para o plano duplicado</param>
        /// <returns>Plano financeiro duplicado</returns>
        Task<PlanoFinanceiroViewModel?> DuplicarPlanoAsync(string idPlano, string novoTitulo);

        /// <summary>
        /// Arquiva um plano financeiro
        /// </summary>
        /// <param name="idPlano">ID do plano</param>
        /// <returns>True se a operação foi bem-sucedida</returns>
        Task<bool> ArquivarPlanoAsync(string idPlano);

        /// <summary>
        /// Restaura um plano financeiro arquivado
        /// </summary>
        /// <param name="idPlano">ID do plano</param>
        /// <returns>True se a operação foi bem-sucedida</returns>
        Task<bool> RestaurarPlanoAsync(string idPlano);

        /// <summary>
        /// Atualiza um plano financeiro existente
        /// </summary>
        /// <param name="plano">Dados atualizados do plano</param>
        /// <returns>Plano atualizado</returns>
        Task<PlanoFinanceiroViewModel?> AtualizarAsync(PlanoFinanceiroViewModel plano);

        /// <summary>
        /// Inativa um plano financeiro
        /// </summary>
        /// <param name="idPlano">ID do plano</param>
        /// <returns>True se a inativação foi bem-sucedida</returns>
        Task<bool> InativarAsync(string idPlano);
    }
}
