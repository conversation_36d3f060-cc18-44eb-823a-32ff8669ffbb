using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceClient.Interfaces;
using Shared.ViewModels.Client;

namespace CasteloForte.Controllers
{
    /// <summary>
    /// Controller para gerenciar dados da dashboard do cliente
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize] // Requer autenticação do cliente
    public class DashboardController : ControllerBase
    {
        private readonly ILogger<DashboardController> _logger;
        private readonly IContaService _contaService;
        private readonly ICartaoService _cartaoService;
        private readonly ICategoriaService _categoriaService;

        /// <summary>
        /// Construtor do DashboardController
        /// </summary>
        /// <param name="logger">Logger para registrar operações</param>
        /// <param name="contaService">Serviço de contas</param>
        /// <param name="cartaoService">Serviço de cartões</param>
        /// <param name="categoriaService">Serviço de categorias</param>
        public DashboardController(
            ILogger<DashboardController> logger,
            IContaService contaService,
            ICartaoService cartaoService,
            ICategoriaService categoriaService)
        {
            _logger = logger;
            _contaService = contaService;
            _cartaoService = cartaoService;
            _categoriaService = categoriaService;
        }

        /// <summary>
        /// Obtém todos os dados da dashboard do usuário cliente
        /// </summary>
        /// <returns>Dados completos da dashboard</returns>
        [HttpGet("dados")]
        [ProducesResponseType(typeof(DashboardResponseViewModel), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<DashboardResponseViewModel>> GetDashboardData()
        {
            try
            {
                _logger.LogInformation("Iniciando busca de dados da dashboard para o cliente");

                // Tenta buscar dados reais primeiro
                try
                {
                    var realData = await GetRealDashboardData();
                    _logger.LogInformation("Dados reais da dashboard obtidos com sucesso");
                    return Ok(realData);
                }
                catch (Exception dbEx)
                {
                    _logger.LogWarning(dbEx, "Erro ao buscar dados reais, usando dados mock como fallback");

                    _logger.LogInformation("Dados mock da dashboard retornados como fallback");
                    return BadRequest();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro geral ao buscar dados da dashboard");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    new { message = "Erro interno do servidor ao buscar dados da dashboard" });
            }
        }

        /// <summary>
        /// Atualiza a flag do questionário de perfil financeiro
        /// </summary>
        /// <param name="request">Dados da requisição</param>
        /// <returns>Resultado da operação</returns>
        [HttpPut("questionario-perfil")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> UpdateQuestionarioPerfil([FromBody] UpdateQuestionarioPerfilRequest request)
        {
            try
            {
                _logger.LogInformation("Atualizando flag do questionário de perfil: {ExibirQuestionario}",
                    request.ExibirQuestionarioPerfil);

                // TODO: Implementar atualização real no banco de dados
                // Por enquanto, simula sucesso
                await Task.Delay(100); // Simula operação assíncrona

                _logger.LogInformation("Flag do questionário de perfil atualizada com sucesso");

                return Ok(new { success = true, message = "Questionário de perfil atualizado com sucesso" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao atualizar questionário de perfil");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    new { message = "Erro interno do servidor ao atualizar questionário de perfil" });
            }
        }

        /// <summary>
        /// Endpoint de diagnóstico para verificar problemas de criptografia
        /// </summary>
        /// <returns>Diagnóstico de criptografia das coleções</returns>
        [HttpGet("diagnostico-criptografia")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> GetDiagnosticoCriptografia()
        {
            try
            {
                _logger.LogInformation("Iniciando diagnóstico de criptografia");

                var diagnosticos = new List<object>();

                // Diagnóstico de contas
                try
                {
                    await _contaService.BuscarTodosAsync();
                    diagnosticos.Add(new { colecao = "Contas", status = "OK", erro = (string?)null });
                }
                catch (Exception ex)
                {
                    diagnosticos.Add(new { colecao = "Contas", status = "ERRO", erro = ex.Message });
                }

                // Diagnóstico de cartões
                try
                {
                    await _cartaoService.BuscarTodosAsync();
                    diagnosticos.Add(new { colecao = "Cartoes", status = "OK", erro = (string?)null });
                }
                catch (Exception ex)
                {
                    diagnosticos.Add(new { colecao = "Cartoes", status = "ERRO", erro = ex.Message });
                }

                // Diagnóstico de categorias
                try
                {
                    await _categoriaService.BuscarTodosAsync();
                    diagnosticos.Add(new { colecao = "Categorias", status = "OK", erro = (string?)null });
                }
                catch (Exception ex)
                {
                    diagnosticos.Add(new { colecao = "Categorias", status = "ERRO", erro = ex.Message });
                }

                var resultado = new
                {
                    timestamp = DateTime.UtcNow,
                    diagnosticos = diagnosticos,
                    resumo = new
                    {
                        total = diagnosticos.Count,
                        comErro = diagnosticos.Count(d => ((dynamic)d).status == "ERRO"),
                        ok = diagnosticos.Count(d => ((dynamic)d).status == "OK")
                    }
                };

                return Ok(resultado);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro no diagnóstico de criptografia");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    new { message = "Erro no diagnóstico de criptografia", error = ex.Message });
            }
        }

        /// <summary>
        /// Endpoint de diagnóstico para testar conectividade dos serviços
        /// </summary>
        /// <returns>Status dos serviços</returns>
        [HttpGet("diagnostico")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> GetDiagnostico()
        {
            try
            {
                _logger.LogInformation("Iniciando diagnóstico dos serviços");

                var diagnostico = new
                {
                    timestamp = DateTime.UtcNow,
                    servicos = new
                    {
                        contaService = new
                        {
                            tipo = _contaService.GetType().Name,
                            status = "Disponível"
                        },
                        cartaoService = new
                        {
                            tipo = _cartaoService.GetType().Name,
                            status = "Disponível"
                        },
                        categoriaService = new
                        {
                            tipo = _categoriaService.GetType().Name,
                            status = "Disponível"
                        }
                    },
                    testes = new List<object>()
                };

                // Teste de conectividade básica
                try
                {
                    await DiagnosticarConectividade();
                    ((List<object>)diagnostico.testes).Add(new { teste = "Conectividade", status = "OK" });
                }
                catch (Exception ex)
                {
                    ((List<object>)diagnostico.testes).Add(new { teste = "Conectividade", status = "ERRO", erro = ex.Message });
                }

                // Teste de busca de dados
                try
                {
                    var testData = await GetRealDashboardData();
                    ((List<object>)diagnostico.testes).Add(new
                    {
                        teste = "Busca de Dados",
                        status = "OK",
                        registros = new
                        {
                            contas = testData.Contas.Count,
                            cartoes = testData.Cartoes.Count,
                            categorias = testData.Categorias.Count
                        }
                    });
                }
                catch (Exception ex)
                {
                    ((List<object>)diagnostico.testes).Add(new { teste = "Busca de Dados", status = "ERRO", erro = ex.Message });
                }

                _logger.LogInformation("Diagnóstico concluído");
                return Ok(diagnostico);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro no diagnóstico");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    new { message = "Erro no diagnóstico", erro = ex.Message });
            }
        }

        /// <summary>
        /// Busca dados reais da dashboard do banco de dados
        /// </summary>
        /// <returns>Dados reais da dashboard</returns>
        private async Task<DashboardResponseViewModel> GetRealDashboardData()
        {
            try
            {
                _logger.LogInformation("Iniciando busca de dados reais da dashboard");

                // Diagnóstico de conectividade
                await DiagnosticarConectividade();

                // Buscar contas reais do banco de dados com tratamento de criptografia
                List<ContaViewModel?> contas;
                List<ContaViewModel> contasAtivas;
                try
                {
                    _logger.LogInformation("Buscando contas do banco de dados");
                    contas = (await _contaService.BuscarTodosAsync())?.ToList() ?? new List<ContaViewModel?>();
                    contasAtivas = contas.Where(c => c != null && c.Ativa).Cast<ContaViewModel>().ToList();
                    _logger.LogInformation("Encontradas {Count} contas ativas de {Total} total", contasAtivas.Count, contas.Count);
                }
                catch (Exception ex) when (ex.Message.Contains("Padding is invalid") ||
                                         ex.Message.Contains("descriptografar") ||
                                         ex.InnerException?.Message.Contains("Padding is invalid") == true)
                {
                    _logger.LogWarning(ex, "Erro de criptografia ao buscar contas - alguns registros podem estar corrompidos");
                    contas = new List<ContaViewModel?>();
                    contasAtivas = new List<ContaViewModel>();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erro geral ao buscar contas do banco de dados");
                    contas = new List<ContaViewModel?>();
                    contasAtivas = new List<ContaViewModel>();
                }

                // Buscar cartões reais do banco de dados com tratamento de criptografia
                List<CartaoViewModel?> cartoes;
                List<CartaoViewModel> cartoesAtivos;
                try
                {
                    _logger.LogInformation("Buscando cartões do banco de dados");
                    cartoes = (await _cartaoService.BuscarTodosAsync())?.ToList() ?? new List<CartaoViewModel?>();
                    cartoesAtivos = cartoes.Where(c => c != null && c.FlgAtivo).Cast<CartaoViewModel>().ToList();
                    _logger.LogInformation("Encontrados {Count} cartões ativos de {Total} total", cartoesAtivos.Count, cartoes.Count);
                }
                catch (Exception ex) when (ex.Message.Contains("Padding is invalid") ||
                                         ex.Message.Contains("descriptografar") ||
                                         ex.InnerException?.Message.Contains("Padding is invalid") == true)
                {
                    _logger.LogWarning(ex, "Erro de criptografia ao buscar cartões - alguns registros podem estar corrompidos");
                    cartoes = new List<CartaoViewModel?>();
                    cartoesAtivos = new List<CartaoViewModel>();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erro geral ao buscar cartões do banco de dados");
                    cartoes = new List<CartaoViewModel?>();
                    cartoesAtivos = new List<CartaoViewModel>();
                }

                // Buscar categorias reais do banco de dados com tratamento de criptografia
                List<CategoriaViewModel?> categorias;
                List<CategoriaViewModel> categoriasAtivas;
                try
                {
                    _logger.LogInformation("Buscando categorias do banco de dados");
                    categorias = (await _categoriaService.BuscarTodosAsync())?.ToList() ?? new List<CategoriaViewModel?>();
                    categoriasAtivas = categorias.Where(c => c != null && c.Ativa).Cast<CategoriaViewModel>().ToList();
                    _logger.LogInformation("Encontradas {Count} categorias ativas de {Total} total", categoriasAtivas.Count, categorias.Count);
                }
                catch (Exception ex) when (ex.Message.Contains("Padding is invalid") ||
                                         ex.Message.Contains("descriptografar") ||
                                         ex.InnerException?.Message.Contains("Padding is invalid") == true)
                {
                    _logger.LogWarning(ex, "Erro de criptografia ao buscar categorias - alguns registros podem estar corrompidos");
                    categorias = new List<CategoriaViewModel?>();
                    categoriasAtivas = new List<CategoriaViewModel>();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erro geral ao buscar categorias do banco de dados");
                    categorias = new List<CategoriaViewModel?>();
                    categoriasAtivas = new List<CategoriaViewModel>();
                }

                // Calcular valor total das contas ativas
                var valorTotal = contasAtivas.Sum(c => c.Saldo);

                return new DashboardResponseViewModel
                {
                    ValorTotal = valorTotal,
                    ExibirQuestionarioPerfil = true,
                    Contas = contasAtivas.Select(c => new DashboardContaViewModel
                    {
                        Id = c.Id ?? string.Empty,
                        NomeBanco = c.Nome ?? "Banco",
                        TipoConta = c.TipoContaDescricao ?? "Conta",
                        Saldo = c.Saldo,
                        Ativa = c.Ativa
                    }).ToList(),
                    Cartoes = cartoesAtivos.Select(c => new DashboardCartaoViewModel
                    {
                        Id = c.Id ?? string.Empty,
                        NomeCartao = c.NomeCartao ?? "Cartão",
                        UltimosDigitos = "0000", // TODO: Implementar quando CartaoViewModel tiver UltimosDigitos
                        Bandeira = "VISA", // TODO: Implementar quando CartaoViewModel tiver Bandeira
                        Ativo = c.FlgAtivo
                    }).ToList(),
                    // TODO: Implementar busca de lançamentos quando o serviço estiver disponível
                    UltimosLancamentos = new List<DashboardLancamentoViewModel>(),
                    Categorias = categoriasAtivas.Select(c => new DashboardCategoriaViewModel
                    {
                        Id = c.Id ?? string.Empty,
                        Nome = c.Nome ?? "Categoria",
                        Icone = ConvertIconeToString(c.Icone),
                        Cor = ConvertCorToString(c.Cor),
                        Tipo = c.Tipo.ToString().ToUpper()
                    }).ToList()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao buscar dados reais da dashboard");

                // Verifica se é erro de criptografia
                if (ex.Message.Contains("Padding is invalid") ||
                    ex.Message.Contains("descriptografar") ||
                    ex.InnerException?.Message.Contains("Padding is invalid") == true ||
                    ex.InnerException?.Message.Contains("descriptografar") == true)
                {
                    _logger.LogWarning("Erro de criptografia detectado, retornando dados mock como fallback");
                }

                // Em caso de outros erros, retorna dados vazios
                _logger.LogWarning("Erro geral detectado, retornando dados vazios");
                return new DashboardResponseViewModel
                {
                    ValorTotal = 0m,
                    ExibirQuestionarioPerfil = true,
                    Contas = new List<DashboardContaViewModel>(),
                    Cartoes = new List<DashboardCartaoViewModel>(),
                    UltimosLancamentos = new List<DashboardLancamentoViewModel>(),
                    Categorias = new List<DashboardCategoriaViewModel>()
                };
            }
        }

        /// <summary>
        /// Converte código do ícone para string
        /// </summary>
        /// <param name="iconeCode">Código do ícone</param>
        /// <returns>Nome do ícone</returns>
        private static string ConvertIconeToString(int iconeCode)
        {
            // Mapeamento básico de códigos para nomes de ícones
            return iconeCode switch
            {
                0xE7FD => "category", // Ícone padrão
                0xE57C => "food", // restaurant
                0xE531 => "transport", // directions_car
                0xE8CC => "shopping", // shopping_cart
                0xE3F0 => "health", // local_hospital
                0xE80C => "education", // school
                0xE02C => "entertainment", // movie
                0xE8F9 => "work", // work
                0xE8E5 => "investment", // trending_up
                0xE88A => "home", // home
                0xEA65 => "bills", // receipt
                _ => "category" // Padrão
            };
        }

        /// <summary>
        /// Converte cor para string hexadecimal (já está no formato correto)
        /// </summary>
        /// <param name="cor">Cor em formato hexadecimal</param>
        /// <returns>Cor em formato hexadecimal</returns>
        private static string ConvertCorToString(string cor)
        {
            // Cor já está no formato hexadecimal, apenas retorna
            return cor ?? "#FFFFFF";
        }

        /// <summary>
        /// Diagnostica problemas de conectividade com os serviços
        /// </summary>
        private async Task DiagnosticarConectividade()
        {
            try
            {
                _logger.LogInformation("Iniciando diagnóstico de conectividade");

                // Testa conectividade básica dos serviços
                var contaServiceType = _contaService.GetType().Name;
                var cartaoServiceType = _cartaoService.GetType().Name;
                var categoriaServiceType = _categoriaService.GetType().Name;

                _logger.LogInformation("Serviços disponíveis: Conta={ContaService}, Cartao={CartaoService}, Categoria={CategoriaService}",
                    contaServiceType, cartaoServiceType, categoriaServiceType);

                // Teste básico de conectividade (sem buscar dados)
                await Task.Delay(10); // Simula verificação de conectividade

                _logger.LogInformation("Diagnóstico de conectividade concluído com sucesso");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Problema detectado no diagnóstico de conectividade");
                throw;
            }
        }
    }

    /// <summary>
    /// Request para atualizar questionário de perfil
    /// </summary>
    public class UpdateQuestionarioPerfilRequest
    {
        /// <summary>
        /// Flag para exibir ou ocultar o questionário de perfil
        /// </summary>
        public bool ExibirQuestionarioPerfil { get; set; }
    }
}
