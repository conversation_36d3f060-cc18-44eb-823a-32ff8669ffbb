0000000000000000000000000000000000000000 5aa3a543d5cf81ebdeacfe223c4c8cc464931db7 Vitorhfc <<EMAIL>> 1752425500 -0300	clone: from https://dev.azure.com/fasofs/Castelo%20Forte/_git/Castelo%20Forte
5aa3a543d5cf81ebdeacfe223c4c8cc464931db7 d5bb4484e0a4b936e7186949455e4ccf2d27a09a Vitorhfc <<EMAIL>> 1752425576 -0300	commit: up
d5bb4484e0a4b936e7186949455e4ccf2d27a09a f136c90b88af69bab14b1df013396ce07e4e7266 Vitorhfc <<EMAIL>> 1752426140 -0300	commit: ;;
f136c90b88af69bab14b1df013396ce07e4e7266 894261ec0e285441e17869f21fa5f18e969c773e Vitorhfc <<EMAIL>> 1752637600 -0300	commit: update
894261ec0e285441e17869f21fa5f18e969c773e 89912e82e478fa4470ede3cef56957b69b193a59 Vitorhfc <<EMAIL>> 1752725154 -0300	commit: Update
89912e82e478fa4470ede3cef56957b69b193a59 f13df9f3885c310a8a93dc34400684593311fe4c Vitorhfc <<EMAIL>> 1753148335 -0300	commit: ;;;
f13df9f3885c310a8a93dc34400684593311fe4c 6533b6bf455ffc778e7871c008431b7bd26f5993 Vitorhfc <<EMAIL>> 1753156953 -0300	commit: update
6533b6bf455ffc778e7871c008431b7bd26f5993 94f939b7b23adb177e57295eda5ea5f85fe11f8d Vitorhfc <<EMAIL>> 1753239658 -0300	commit: ;;;
94f939b7b23adb177e57295eda5ea5f85fe11f8d fea86137d4c901433b5ad78b996fe4e60ae3c09b Vitorhfc <<EMAIL>> 1753673506 -0300	commit: ;;;;
fea86137d4c901433b5ad78b996fe4e60ae3c09b bb0e787b0678c7d3706788b814614c75e585da99 Vitorhfc <<EMAIL>> 1753743823 -0300	commit: Remove redundant response type attributes and validation for unique category name in CategoriaController and CategoriaService
bb0e787b0678c7d3706788b814614c75e585da99 a96b30c613edc6acc305ef9d53306d2ee6f9dff2 Vitorhfc <<EMAIL>> 1753756762 -0300	commit: Refactor category handling: enhance filtering options in BuscarTodos, update color property to hexadecimal format, and remove unused subcategory methods across services and repositories.
a96b30c613edc6acc305ef9d53306d2ee6f9dff2 23880b9f3830728db317e77e313d5a0531fa3a1d Vitorhfc <<EMAIL>> 1753769967 -0300	commit: Refactor MetaService and IMetaService to streamline methods and improve filtering capabilities
23880b9f3830728db317e77e313d5a0531fa3a1d 8ae223fd0b335111964e07c2a65d638ed6d2efb2 Vitorhfc <<EMAIL>> 1754013002 -0300	commit: Implement TransferenciaController and TransferenciaService for managing financial transfers, including search, create, update, and delete operations with optional filters.
