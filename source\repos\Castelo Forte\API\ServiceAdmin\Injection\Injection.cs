﻿using Microsoft.Extensions.DependencyInjection;
using Shared.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServiceAdmin.Injection
{
    public static class Injection
    {
        public static IServiceCollection AddInjectionServiceAdmin(this IServiceCollection services)
        {
            var assemblies = AppDomain.CurrentDomain.GetAssemblies();

            var types = LoaderAssemblies.BuscarClassesEInterfaces(
                assemblies,
                "ServiceAdmin",
                "Service"
            );

            foreach (var (serviceType, interfaceType) in types)
            {
                if (!services.Any(s => s.ServiceType == interfaceType))
                    services.AddScoped(interfaceType, serviceType);
            }

            var usuarioServiceRegistered = services.Any(s => s.ServiceType.Name == "IUsuarioService");

            return services;
        }
    }
}