﻿using ServiceAdmin.Interfaces.Generic;
using Shared.Entities.Admin;
using Shared.Enums;
using Shared.ViewModels.Admin;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServiceAdmin.Interfaces
{
    public interface ILogErroAdminService : IGenericAdminService<LogErroAdminViewModel, LogErroAdmin>
    {
        Task LogErro(Exception ex, string metodo, string controller, string variaveis);

        /// <summary>
        /// Busca logs de erro por período
        /// </summary>
        /// <param name="dataInicio">Data de início</param>
        /// <param name="dataFim">Data de fim</param>
        /// <returns>Lista de logs de erro no período</returns>
        Task<IEnumerable<LogErroAdminViewModel>> BuscarPorPeriodoAsync(DateTime dataInicio, DateTime dataFim);

        /// <summary>
        /// Busca logs de erro por usuário
        /// </summary>
        /// <param name="idUsuario">ID do usuário</param>
        /// <returns>Lista de logs de erro do usuário</returns>
        Task<IEnumerable<LogErroAdminViewModel>> BuscarPorUsuarioAsync(string idUsuario);

        /// <summary>
        /// Busca logs de erro com filtros e paginação
        /// </summary>
        /// <param name="filtro">Filtros para busca</param>
        /// <returns>Resultado paginado de logs de erro</returns>
        Task<ResultadoPaginadoViewModel<LogErroAdminViewModel>> BuscarLogsFiltradosAsync(FiltroLogErroViewModel filtro);

        /// <summary>
        /// Busca logs de erro por controller
        /// </summary>
        /// <param name="controller">Nome do controller</param>
        /// <returns>Lista de logs de erro do controller</returns>
        Task<IEnumerable<LogErroAdminViewModel>> BuscarPorControllerAsync(string controller);

        /// <summary>
        /// Obtém estatísticas dos logs de erro
        /// </summary>
        /// <returns>Estatísticas dos logs</returns>
        Task<object> ObterEstatisticasLogsAsync();
    }
}
