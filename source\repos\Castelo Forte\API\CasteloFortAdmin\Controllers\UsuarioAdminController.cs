using CasteloForteAdmin.Attributes;
using CasteloForteAdmin.Controllers.ControllerBaseComplemento;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceAdmin.Interfaces;
using Shared.ViewModels.Admin;
using System.Text.Json;

namespace CasteloForteAdmin.Controllers
{
    [Authorize]
    [AdminAuthorize]
    [Route("api/[controller]")]
    [ApiController]
    public class UsuarioAdminController : ControllerBaseComplemento<UsuarioAdminController>
    {
        private readonly string _controller = "UsuarioAdminController";
        private readonly IUsuarioService _usuarioService;

        public UsuarioAdminController(
            IUsuarioService usuarioService,
            ILogErroAdminService logErroAdminService,
            IHistoricoUsuarioService historicoUsuarioService,
            ILogger<UsuarioAdminController> logger
            ) : base(logErroAdminService, historicoUsuarioService, logger)
        {
            _usuarioService = usuarioService ?? throw new ArgumentNullException(nameof(usuarioService));
        }

        #region Consultas Administrativas de Usuários

        /// <summary>
        /// Busca usuários com filtros avançados e paginação (Método principal recomendado)
        /// </summary>
        /// <param name="filtro">Filtros para busca</param>
        /// <returns>Resultado paginado de usuários</returns>
        [HttpPost("filtrados")]
        public async Task<IActionResult> BuscarUsuariosFiltrados([FromBody] FiltroUsuarioViewModel filtro)
        {
            string variaveis = JsonSerializer.Serialize(filtro);
            try
            {
                string metodo = _controller + " BuscarUsuariosFiltrados";
                await RegistraAcao(metodo, "Busca de usuários com filtros", "", variaveis);

                if (filtro == null)
                    return BadRequest(new { error = "Filtros são obrigatórios" });

                LogInfo($"Iniciando busca filtrada de usuários - Página: {filtro.Pagina}", nameof(BuscarUsuariosFiltrados), _controller);

                var resultado = await _usuarioService.BuscarUsuariosFiltradosAsync(filtro);

                LogInfo($"Busca filtrada concluída. {resultado.TotalRegistros} usuários encontrados", nameof(BuscarUsuariosFiltrados), _controller);
                return Ok(new
                {
                    success = true,
                    message = "Usuários filtrados recuperados com sucesso",
                    data = resultado
                });
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controller, variaveis);
            }
        }

        /// <summary>
        /// Busca usuário por ID específico
        /// </summary>
        /// <param name="id">ID do usuário</param>
        /// <returns>Usuário encontrado</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> BuscarPorId(string id)
        {
            string variaveis = JsonSerializer.Serialize(new { id });
            try
            {
                string metodo = _controller + " BuscarPorId";
                await RegistraAcao(metodo, "Busca de usuário por ID", "", variaveis);

                if (string.IsNullOrWhiteSpace(id))
                    return BadRequest(new { error = "ID é obrigatório" });

                LogInfo($"Iniciando busca do usuário com ID: {id}", nameof(BuscarPorId), _controller);

                var usuario = await _usuarioService.BuscarPorIdAsync(id);

                if (usuario == null)
                {
                    LogInfo($"Usuário com ID {id} não encontrado", nameof(BuscarPorId), _controller);
                    return NotFound(new { success = false, message = "Usuário não encontrado" });
                }

                LogInfo($"Usuário com ID {id} encontrado", nameof(BuscarPorId), _controller);
                return Ok(new
                {
                    success = true,
                    message = "Usuário recuperado com sucesso",
                    data = usuario
                });
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controller, variaveis);
            }
        }

        /// <summary>
        /// Obtém estatísticas dos usuários
        /// </summary>
        /// <returns>Estatísticas dos usuários</returns>
        [HttpGet("estatisticas")]
        public async Task<IActionResult> ObterEstatisticasUsuarios()
        {
            string variaveis = "";
            try
            {
                string metodo = _controller + " ObterEstatisticasUsuarios";
                await RegistraAcao(metodo, "Obtenção de estatísticas dos usuários", "", variaveis);

                LogInfo("Iniciando obtenção de estatísticas dos usuários", nameof(ObterEstatisticasUsuarios), _controller);

                var estatisticas = await _usuarioService.ObterEstatisticasUsuariosAsync();

                LogInfo("Estatísticas dos usuários obtidas com sucesso", nameof(ObterEstatisticasUsuarios), _controller);
                return Ok(new
                {
                    success = true,
                    message = "Estatísticas dos usuários obtidas com sucesso",
                    data = estatisticas
                });
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controller, variaveis);
            }
        }

        #endregion
    }
}
