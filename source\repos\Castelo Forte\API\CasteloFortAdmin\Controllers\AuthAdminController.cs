using CasteloForteAdmin.Controllers.ControllerBaseComplemento;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceAdmin.Interfaces;
using Shared.ViewModels.Admin;
using System.Text.Json;

namespace CasteloForteAdmin.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AuthAdminController : ControllerBaseComplemento<AuthAdminController>
    {
        private readonly string _controller = "AuthAdminController";
        private readonly IAuthAdminService _authAdminService;

        public AuthAdminController(
            IAuthAdminService authAdminService,
            ILogErroAdminService logErroAdminService,
            IHistoricoUsuarioService historicoUsuarioService,
            ILogger<AuthAdminController> logger
            ) : base(logErroAdminService, historicoUsuarioService, logger)
        {
            _authAdminService = authAdminService ?? throw new ArgumentNullException(nameof(authAdminService));
        }

        /// <summary>
        /// Realiza login específico para administradores do sistema
        /// </summary>
        /// <param name="loginRequest">Dados de login do administrador</param>
        /// <returns>Token JWT e dados do administrador autenticado</returns>
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<IActionResult> LoginAdmin([FromBody] LoginAdminRequestViewModel loginRequest)
        {
            string variaveis = JsonSerializer.Serialize(new { 
                CPF = loginRequest?.CPF ?? "", 
                Email = loginRequest?.Email ?? "",
                TemSenha = !string.IsNullOrEmpty(loginRequest?.Senha)
            });

            try
            {
                string metodo = _controller + " LoginAdmin";
                await RegistraAcao(metodo, "Tentativa de login de administrador", "", variaveis);

                // Validações básicas
                if (loginRequest == null)
                {
                    LogInfo("Tentativa de login com dados nulos", nameof(LoginAdmin), _controller);
                    return BadRequest(new
                    {
                        success = false,
                        message = "Dados de login são obrigatórios",
                        timestamp = DateTimeOffset.UtcNow
                    });
                }

                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList();

                    LogInfo($"Dados de login inválidos: {string.Join(", ", errors)}", nameof(LoginAdmin), _controller);
                    return BadRequest(new
                    {
                        success = false,
                        message = "Dados de login inválidos",
                        errors = errors,
                        timestamp = DateTimeOffset.UtcNow
                    });
                }

                // Validação de CPF ou Email
                if (string.IsNullOrWhiteSpace(loginRequest.CPF) && string.IsNullOrWhiteSpace(loginRequest.Email))
                {
                    LogInfo("Tentativa de login sem CPF ou email", nameof(LoginAdmin), _controller);
                    return BadRequest(new
                    {
                        success = false,
                        message = "CPF ou email é obrigatório",
                        timestamp = DateTimeOffset.UtcNow
                    });
                }

                LogInfo($"Iniciando autenticação de administrador - CPF: {loginRequest.CPF ?? "N/A"}, Email: {loginRequest.Email ?? "N/A"}", 
                    nameof(LoginAdmin), _controller);

                // Realiza a autenticação
                var response = await _authAdminService.AutenticarAdminAsync(loginRequest);

                LogInfo($"Login de administrador realizado com sucesso - Usuário: {response.Usuario.Nome}", 
                    nameof(LoginAdmin), _controller);

                // Registra ação de login bem-sucedido
                await RegistraAcao(
                    metodo + " - Sucesso", 
                    $"Login de administrador realizado com sucesso - {response.Usuario.Nome}", 
                    response.Usuario.Id, 
                    variaveis);

                return Ok(new
                {
                    success = true,
                    message = "Login de administrador realizado com sucesso",
                    data = response,
                    timestamp = DateTimeOffset.UtcNow
                });
            }
            catch (UnauthorizedAccessException ex)
            {
                LogInfo($"Falha na autenticação de administrador: {ex.Message}", nameof(LoginAdmin), _controller);
                
                await RegistraAcao(
                    _controller + " LoginAdmin - Falha", 
                    $"Falha na autenticação: {ex.Message}", 
                    "", 
                    variaveis);

                return Unauthorized(new
                {
                    success = false,
                    message = ex.Message,
                    timestamp = DateTimeOffset.UtcNow
                });
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controller, variaveis);
            }
        }

        /// <summary>
        /// Valida um token de administrador
        /// </summary>
        /// <param name="request">Token a ser validado</param>
        /// <returns>Resultado da validação</returns>
        [HttpPost("validate-token")]
        [AllowAnonymous]
        public async Task<IActionResult> ValidateAdminToken([FromBody] ValidateAdminTokenRequestViewModel request)
        {
            string variaveis = JsonSerializer.Serialize(new { TemToken = !string.IsNullOrEmpty(request?.Token) });

            try
            {
                string metodo = _controller + " ValidateAdminToken";
                await RegistraAcao(metodo, "Validação de token de administrador", "", variaveis);

                if (request == null || string.IsNullOrWhiteSpace(request.Token))
                {
                    LogInfo("Tentativa de validação com token nulo ou vazio", nameof(ValidateAdminToken), _controller);
                    return BadRequest(new
                    {
                        success = false,
                        message = "Token é obrigatório",
                        timestamp = DateTimeOffset.UtcNow
                    });
                }

                LogInfo("Iniciando validação de token de administrador", nameof(ValidateAdminToken), _controller);

                var response = await _authAdminService.ValidarTokenAdminAsync(request.Token);

                if (response.IsValid && response.IsAdmin)
                {
                    LogInfo($"Token de administrador válido - Usuário: {response.Usuario?.Nome ?? "N/A"}", 
                        nameof(ValidateAdminToken), _controller);

                    return Ok(new
                    {
                        success = true,
                        message = "Token de administrador válido",
                        data = response,
                        timestamp = DateTimeOffset.UtcNow
                    });
                }
                else
                {
                    LogInfo($"Token de administrador inválido: {response.MensagemErro}",
                        nameof(ValidateAdminToken), _controller);

                    return Unauthorized(new
                    {
                        success = false,
                        message = response.MensagemErro ?? "Token inválido",
                        timestamp = DateTimeOffset.UtcNow
                    });
                }
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controller, variaveis);
            }
        }

        /// <summary>
        /// Endpoint para verificar se o usuário atual tem privilégios de administrador
        /// </summary>
        /// <returns>Status de administrador do usuário autenticado</returns>
        [HttpGet("check-admin")]
        [Authorize]
        public async Task<IActionResult> CheckAdminStatus()
        {
            string variaveis = "";

            try
            {
                string metodo = _controller + " CheckAdminStatus";
                await RegistraAcao(metodo, "Verificação de status de administrador", "", variaveis);

                var usuarioId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                
                if (string.IsNullOrEmpty(usuarioId))
                {
                    LogInfo("ID do usuário não encontrado no token", nameof(CheckAdminStatus), _controller);
                    return Unauthorized(new
                    {
                        success = false,
                        message = "ID do usuário não encontrado no token",
                        timestamp = DateTimeOffset.UtcNow
                    });
                }

                // Aqui você pode usar o UsuarioService para verificar se o usuário é admin
                // Por simplicidade, vou usar as claims do token
                var isAdminClaim = User.FindFirst("isAdmin")?.Value;
                var isAdmin = isAdminClaim == "true";

                LogInfo($"Status de administrador verificado - Usuário: {usuarioId}, IsAdmin: {isAdmin}", 
                    nameof(CheckAdminStatus), _controller);

                return Ok(new
                {
                    success = true,
                    message = "Status de administrador verificado",
                    data = new
                    {
                        usuarioId = usuarioId,
                        isAdmin = isAdmin,
                        tokenType = User.FindFirst("tokenType")?.Value ?? "standard"
                    },
                    timestamp = DateTimeOffset.UtcNow
                });
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controller, variaveis);
            }
        }

        /// <summary>
        /// Endpoint para logout de administrador (invalidação do token no cliente)
        /// </summary>
        /// <returns>Confirmação de logout</returns>
        [HttpPost("logout")]
        [Authorize]
        public async Task<IActionResult> LogoutAdmin()
        {
            string variaveis = "";

            try
            {
                string metodo = _controller + " LogoutAdmin";
                var usuarioId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "";
                
                await RegistraAcao(metodo, "Logout de administrador", usuarioId, variaveis);

                LogInfo($"Logout de administrador realizado - Usuário: {usuarioId}", nameof(LogoutAdmin), _controller);

                return Ok(new
                {
                    success = true,
                    message = "Logout realizado com sucesso",
                    timestamp = DateTimeOffset.UtcNow
                });
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controller, variaveis);
            }
        }
    }
}
