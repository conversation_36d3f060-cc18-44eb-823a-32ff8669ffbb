import '../../../core/services/api_service.dart';
import '../../../core/services/logger_service.dart';
import 'models/account_model.dart';

/// Resultado do carregamento de contas
class AccountsLoadResult {
  final List<ContaViewModel> accounts;
  final bool isFromApi;
  final String? errorMessage;
  final bool hasMore;
  final int totalCount;

  const AccountsLoadResult({
    required this.accounts,
    required this.isFromApi,
    this.errorMessage,
    required this.hasMore,
    required this.totalCount,
  });
}

/// Service para gerenciar contas bancárias
class AccountsService {
  static const String _baseUrl = 'Conta';

  /// Busca todas as contas do usuário
  static Future<AccountsLoadResult> getAccounts({
    int page = 1,
    int limit = 20,
    String? search,
    String? bankFilter,
    String? typeFilter,
    bool? activeFilter,
  }) async {
    try {
      LoggerService.info('Buscando contas...');

      // Constrói os parâmetros de query para filtros server-side
      final queryParams = <String, String>{};

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }

      if (activeFilter != null) {
        queryParams['ativa'] = activeFilter.toString();
      }

      // Chama a API real do backend com filtros
      final response = await ApiService.get(_baseUrl, queryParams: queryParams);
      LoggerService.info('Contas obtidas da API com sucesso');

      // A API retorna um objeto com success, message, data, timestamp
      final data = response['data'];
      final accounts = <ContaViewModel>[];

      if (data is List) {
        for (final item in data) {
          if (item is Map<String, dynamic>) {
            accounts.add(ContaViewModel.fromJson(item));
          }
        }
      }

      // Os filtros agora são aplicados no servidor, então retornamos os dados diretamente
      return AccountsLoadResult(
        accounts: accounts,
        isFromApi: true,
        hasMore: false,
        totalCount: accounts.length,
      );
    } catch (apiError) {
      LoggerService.warning('Erro na API: $apiError');

      // Em caso de erro da API, retorna lista vazia
      return AccountsLoadResult(
        accounts: [],
        isFromApi: false,
        errorMessage:
            'Não foi possível conectar ao servidor. Verifique sua conexão com a internet.',
        hasMore: false,
        totalCount: 0,
      );
    }
  }

  /// Busca uma conta específica por ID
  static Future<ContaViewModel?> getAccountById(String id) async {
    try {
      LoggerService.info('Buscando conta por ID: $id');

      final response = await ApiService.get('$_baseUrl/$id');
      LoggerService.info('Conta obtida da API com sucesso');

      final data = response['data'];
      if (data is Map<String, dynamic>) {
        return ContaViewModel.fromJson(data);
      }

      return null;
    } catch (e) {
      LoggerService.failure('Erro ao buscar conta: $e');
      return null;
    }
  }

  /// Cria uma nova conta
  static Future<bool> createAccount(AccountFormModel account) async {
    try {
      LoggerService.info('Criando nova conta: ${account.nome}');

      await ApiService.post(_baseUrl, account.toJson());
      LoggerService.info('Conta criada com sucesso na API');
      return true;
    } catch (apiError) {
      LoggerService.warning('Erro na API ao criar conta: $apiError');
      return false;
    }
  }

  /// Atualiza uma conta existente
  static Future<bool> updateAccount(AccountFormModel account) async {
    try {
      LoggerService.info('Atualizando conta: ${account.id}');

      final response = await ApiService.put(
        '$_baseUrl/${account.id}',
        account.toJson(),
      );
      LoggerService.info('Conta atualizada com sucesso na API');
      LoggerService.debug('Response: $response');

      return true;
    } catch (e) {
      LoggerService.failure('Erro ao atualizar conta: $e');
      return false;
    }
  }

  /// Exclui uma conta
  static Future<bool> deleteAccount(String id) async {
    try {
      LoggerService.info('Excluindo conta: $id');

      final response = await ApiService.delete('$_baseUrl/$id');
      LoggerService.info('Conta excluída com sucesso na API');
      LoggerService.debug('Response: $response');

      return true;
    } catch (e) {
      LoggerService.failure('Erro ao excluir conta: $e');
      return false;
    }
  }

  /// Retorna lista de bancos disponíveis
  static List<String> getAvailableBanks() {
    return [
      'Nubank',
      'Banco do Brasil',
      'Itaú',
      'Caixa',
      'Santander',
      'Bradesco',
      'Inter',
      'C6 Bank',
      'Original',
      'Sicoob',
      'Sicredi',
      'Outro',
    ];
  }

  /// Retorna lista de tipos de conta disponíveis
  static List<String> getAccountTypes() {
    return ['Conta Corrente', 'Conta Poupança'];
  }

  /// Retorna lista de tipos de conta disponíveis para filtros
  static List<String> getAvailableAccountTypes() {
    return getAccountTypes();
  }
}
