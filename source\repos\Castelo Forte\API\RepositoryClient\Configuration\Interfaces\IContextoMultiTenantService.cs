﻿using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepositoryClient.Configuration.Interfaces
{
    public interface IContextoMultiTenantService
    {
        string? BuscarTenantAtualId();
        void ConfigurarTenant(string IdTenant, string connectionString, string nomeBaseDados);
        Task<IMongoDatabase> ObterBaseDadosUsuarioAsync(string UsuarioId);
        Task<IMongoDatabase> ObterDatabaseUsuarioAsync();
        Task<string> ObterConnectionStringUsuarioAsync();
        IMongoDatabase ObterBaseDadosAdmin();
        string? ObterConnectionStringTenantAtual();
        string? ObterNomeBaseDadosTenantAtual();
        string? ObterIdUsuarioAtual();
        bool TemContextoUsuario();
        void LimparContexto();
        Task ConfigurarContextoUsuarioAsync(string UsuarioId);
    }
}
