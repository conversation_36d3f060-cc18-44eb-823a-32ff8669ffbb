﻿
using MongoDB.Driver;
using Microsoft.AspNetCore.Http;
using RepositoryAdmin.Interfaces;
using RepositoryClient.Configuration.Interfaces;
using RepositoryClient.Interfaces;
using RepositoryClient.Repositories.Generic;
using Shared.Entities.Client;

namespace RepositoryClient.Repositories
{
    /// <summary>
    /// Repositório para logs de erro no contexto Client (multi-tenant)
    /// </summary>
    public class LogErroClientRepository : GenericClientRepository<LogErroClient>, ILogErroClientRepository
    {
        public LogErroClientRepository(
            IContextoMultiTenantService contextoMultiTenant,
            IHttpContextAccessor httpContextAccessor,
            IUsuarioRepository usuarioRepository)
            : base(contextoMultiTenant, httpContextAccessor, usuarioRepository, "LogErroClient")
        {
        }

        /// <summary>
        /// Busca logs de erro por usuário
        /// </summary>
        /// <param name="idUsuario">ID do usuário</param>
        /// <returns>Lista de logs de erro do usuário</returns>
        public async Task<IEnumerable<LogErroClient>> BuscarPorUsuarioAsync(string idUsuario)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(idUsuario))
                    throw new ArgumentException("ID do usuário não pode ser nulo ou vazio", nameof(idUsuario));

                var filter = Builders<LogErroClient>.Filter.Eq(x => x.IdUsuario, idUsuario);
                var sort = Builders<LogErroClient>.Sort.Descending(x => x.DtaCadastro);

                return await _collection
                    .Find(filter)
                    .Sort(sort)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar logs de erro por usuário: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca logs de erro por período
        /// </summary>
        /// <param name="dataInicio">Data de início</param>
        /// <param name="dataFim">Data de fim</param>
        /// <returns>Lista de logs de erro no período</returns>
        public async Task<IEnumerable<LogErroClient>> BuscarPorPeriodoAsync(DateTime dataInicio, DateTime dataFim)
        {
            try
            {
                if (dataInicio > dataFim)
                    throw new ArgumentException("Data de início não pode ser maior que data de fim");

                var filter = Builders<LogErroClient>.Filter.And(
                    Builders<LogErroClient>.Filter.Gte(x => x.DtaCadastro, dataInicio),
                    Builders<LogErroClient>.Filter.Lte(x => x.DtaCadastro, dataFim)
                );
                var sort = Builders<LogErroClient>.Sort.Descending(x => x.DtaCadastro);

                return await _collection
                    .Find(filter)
                    .Sort(sort)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar logs de erro por período: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca logs de erro por severidade
        /// </summary>
        /// <param name="severidade">Severidade do erro</param>
        /// <returns>Lista de logs de erro com a severidade especificada</returns>
        public async Task<IEnumerable<LogErroClient>> BuscarPorSeveridadeAsync(string severidade)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(severidade))
                    throw new ArgumentException("Severidade não pode ser nula ou vazia", nameof(severidade));

                var filter = Builders<LogErroClient>.Filter.Eq(x => x.Severidade, severidade);
                var sort = Builders<LogErroClient>.Sort.Descending(x => x.DtaCadastro);

                return await _collection
                    .Find(filter)
                    .Sort(sort)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar logs de erro por severidade: {ex.Message}", ex);
            }
        }
    }
}
