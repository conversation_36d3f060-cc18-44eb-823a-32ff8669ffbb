/// Modelo de dados para conta bancária
class ContaModel {
  final String id;
  final String nomeBanco;
  final String tipoConta;
  final double saldo;
  final bool ativa;

  const ContaModel({
    required this.id,
    required this.nomeBanco,
    required this.tipoConta,
    required this.saldo,
    required this.ativa,
  });

  /// Cria uma instância a partir de JSON
  factory ContaModel.fromJson(Map<String, dynamic> json) {
    return ContaModel(
      id: json['id']?.toString() ?? '',
      nomeBanco: json['nomeBanco']?.toString() ?? '',
      tipoConta: json['tipoConta']?.toString() ?? '',
      saldo: (json['saldo'] as num?)?.toDouble() ?? 0.0,
      ativa: json['ativa'] as bool? ?? true,
    );
  }

  /// Converte para JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nomeBanco': nomeBanco,
      'tipoConta': tipoConta,
      'saldo': saldo,
      'ativa': ativa,
    };
  }

  /// Cria uma cópia com campos modificados
  ContaModel copyWith({
    String? id,
    String? nomeBanco,
    String? tipoConta,
    double? saldo,
    bool? ativa,
  }) {
    return ContaModel(
      id: id ?? this.id,
      nomeBanco: nomeBanco ?? this.nomeBanco,
      tipoConta: tipoConta ?? this.tipoConta,
      saldo: saldo ?? this.saldo,
      ativa: ativa ?? this.ativa,
    );
  }

  @override
  String toString() {
    return 'ContaModel(id: $id, nomeBanco: $nomeBanco, tipoConta: $tipoConta, saldo: $saldo, ativa: $ativa)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ContaModel &&
        other.id == id &&
        other.nomeBanco == nomeBanco &&
        other.tipoConta == tipoConta &&
        other.saldo == saldo &&
        other.ativa == ativa;
  }

  @override
  int get hashCode {
    return Object.hash(id, nomeBanco, tipoConta, saldo, ativa);
  }
}
