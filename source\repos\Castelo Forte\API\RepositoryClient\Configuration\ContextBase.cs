﻿using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using MongoDB.Driver;
using MongoDB.EntityFrameworkCore.Extensions;
using RepositoryClient.Configuration.Interfaces;
using Shared.Entities.Admin;
using Shared.Entities.Client;
using Shared.Models.Configuration;

namespace RepositoryClient.Configuration
{
    public class ContextBase(
        DbContextOptions<ContextBase> options,
        IOptions<MongoDBSettings>? mongoSettings = null,
        IContextoMultiTenantService contextoMultiTenant = null,
        IHttpContextAccessor httpContextAccessor = null) : DbContext(options)
    {
        private readonly IMongoDatabase _mongoDatabase;
        private readonly IContextoMultiTenantService _contextoMultiTenant = contextoMultiTenant;
        private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;
        private readonly MongoDBSettings _mongoSettings = mongoSettings?.Value;

        public IMongoCollection<Cartao> CartaoCollection => _mongoDatabase.GetCollection<Cartao>("Cartao");
        public IMongoCollection<Categoria> CategoriaCollection => _mongoDatabase.GetCollection<Categoria>("Categoria");
        public IMongoCollection<Conta> ContaCollection => _mongoDatabase.GetCollection<Conta>("Conta");
        public IMongoCollection<HistoricoEdicao> HistoricoEdicaoCollection => _mongoDatabase.GetCollection<HistoricoEdicao>("HistoricoEdicao");
        public IMongoCollection<LogErroClient> LogErroClientCollection => _mongoDatabase.GetCollection<LogErroClient>("LogErroClient");
        public IMongoCollection<LogNotificacao> LogNotificacaoCollection => _mongoDatabase.GetCollection<LogNotificacao>("LogNotificacao");
        public IMongoCollection<Meta> MetaCollection => _mongoDatabase.GetCollection<Meta>("Meta");
        public IMongoCollection<MetaCategoria> MetaCategoriaCollection => _mongoDatabase.GetCollection<MetaCategoria>("MetaCategoria");
        public IMongoCollection<PlanoFinanceiro> PlanoFinanceiroCollection => _mongoDatabase.GetCollection<PlanoFinanceiro>("PlanoFinanceiro");
        public IMongoCollection<Transferencia> TransferenciaCollection => _mongoDatabase.GetCollection<Transferencia>("Transferencia");
        public IMongoCollection<TransferenciasRecorrentes> TransferenciasRecorrentesCollection => _mongoDatabase.GetCollection<TransferenciasRecorrentes>("TransferenciasRecorrentes");

        public IMongoDatabase Database => _mongoDatabase;

        public IMongoCollection<T> GetCollection<T>(string collectionName)
        {
            return _mongoDatabase.GetCollection<T>(collectionName);
        }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            builder.Entity<Cartao>().ToCollection("Cartao");
            builder.Entity<Categoria>().ToCollection("Categoria");
            builder.Entity<Conta>().ToCollection("Conta");
            builder.Entity<HistoricoEdicao>().ToCollection("HistoricoEdicao");
            builder.Entity<LogErroClient>().ToCollection("LogErroClient");
            builder.Entity<LogNotificacao>().ToCollection("LogNotificacao");
            builder.Entity<Meta>().ToCollection("Meta");
            builder.Entity<MetaCategoria>().ToCollection("MetaCategoria");
            builder.Entity<PlanoFinanceiro>().ToCollection("PlanoFinanceiro");
            builder.Entity<Transferencia>().ToCollection("Transferencia");
            builder.Entity<TransferenciasRecorrentes>().ToCollection("TransferenciasRecorrentes");
        }
    }
}