﻿using Microsoft.AspNetCore.Http;
using RepositoryAdmin.Interfaces;
using RepositoryClient.Configuration.Interfaces;
using RepositoryClient.Interfaces;
using RepositoryClient.Repositories.Generic;
using Shared.Entities.Client;

namespace RepositoryClient.Repositories
{
    public class CartaoRepository(
        IContextoMultiTenantService contextoMultiTenant,
        IHttpContextAccessor httpContextAccessor,
         IUsuarioRepository usuarioRepository) : GenericClientRepository<Cartao>(contextoMultiTenant, httpContextAccessor, usuarioRepository, "Cartao"), ICartaoRepository
    {
    }
}
