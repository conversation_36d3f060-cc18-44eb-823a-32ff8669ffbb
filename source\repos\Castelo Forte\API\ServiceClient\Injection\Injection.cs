﻿using Microsoft.Extensions.DependencyInjection;
using Shared.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServiceClient.Injection
{
    public static class Injection
    {
        public static IServiceCollection AddInjectionService(this IServiceCollection services)
        {
            try
            {
                var types = LoaderAssemblies.BuscarClassesEInterfaces(
                    AppDomain.CurrentDomain.GetAssemblies(),
                    "ServiceClient",
                    "Service"
                );

                foreach (var type in types)
                {
                    var existingService = services.FirstOrDefault(s => s.ServiceType == type.Value);
                    if (existingService == null)
                    {
                        services.AddScoped(type.Value, type.Key);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao registrar serviços: {ex.Message}");
                throw;
            }

            return services;
        }
    }
}
