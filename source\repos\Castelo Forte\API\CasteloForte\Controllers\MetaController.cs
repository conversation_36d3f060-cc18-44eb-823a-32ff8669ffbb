using CasteloForte.Controllers.BaseComplemento;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceClient.Interfaces;
using Shared.ViewModels.Client;
using System.Text.Json;

namespace CasteloForte.Controllers
{
    /// <summary>
    /// Controller para gerenciamento de metas financeiras
    /// Refatorado para conter apenas 5 endpoints essenciais RESTful
    /// </summary>
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class MetaController : ControllerBaseComplemento<MetaController>
    {
        private readonly string _controllerName = "MetaController";
        private readonly IMetaService _metaService;

        public MetaController(
            IMetaService metaService,
            ILogger<MetaController> logger,
            ILogErroClientService? logErroClientService = null,
            IHistoricoUsuarioClientService? historicoUsuarioClientService = null)
            : base(logger, logErroClientService, historicoUsuarioClientService)
        {
            _metaService = metaService ?? throw new ArgumentNullException(nameof(metaService));
        }

        #region 5 Endpoints Essenciais

        /// <summary>
        /// 1. GetById - Retrieve a specific goal by its ID
        /// GET /api/meta/{id}
        /// </summary>
        /// <param name="id">ID da meta</param>
        /// <returns>Meta encontrada</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(string id)
        {
            string variaveis = JsonSerializer.Serialize(new { id });
            try
            {
                string metodo = _controllerName + " GetById";
                await RegistraAcao(metodo, "Busca de meta por ID", "", variaveis);

                if (string.IsNullOrWhiteSpace(id))
                    return CriarRespostaErro("ID é obrigatório", "INVALID_ID");

                LogInfo($"Iniciando busca da meta com ID: {id}", nameof(GetById), _controllerName);

                var meta = await _metaService.GetByIdAsync(id);

                if (meta == null)
                {
                    LogInfo($"Meta com ID {id} não encontrada", nameof(GetById), _controllerName);
                    return CriarRespostaNaoEncontrado("Meta não encontrada");
                }

                LogInfo($"Meta com ID {id} encontrada", nameof(GetById), _controllerName);
                return CriarRespostaSucesso(meta, "Meta recuperada com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        /// <summary>
        /// 2. GetAll - Retrieve all goals with filtering capabilities
        /// GET /api/meta
        /// </summary>
        /// <param name="filtros">Filtros para aplicar na busca (via query string ou body)</param>
        /// <returns>Resposta com todas as metas filtradas</returns>
        [HttpGet]
        public async Task<IActionResult> GetAll([FromQuery] MetaFilterViewModel? filtros = null)
        {
            string variaveis = JsonSerializer.Serialize(filtros);
            try
            {
                string metodo = _controllerName + " GetAll";
                await RegistraAcao(metodo, "Busca de metas com filtros", "", variaveis);

                LogInfo($"Iniciando busca de metas com filtros: {variaveis}", nameof(GetAll), _controllerName);

                // Validar se o filtro ApenasAbertas está sendo recebido
                if (filtros?.ApenasAbertas.HasValue == true)
                {
                    LogInfo($"Filtro ApenasAbertas aplicado: {filtros.ApenasAbertas.Value}", nameof(GetAll), _controllerName);
                }

                var resultado = await _metaService.GetAllAsync(filtros);

                LogInfo($"Busca concluída. {resultado.Metas.Count} metas encontradas",
                    nameof(GetAll), _controllerName);
                return CriarRespostaSucesso(resultado, "Metas recuperadas com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        /// <summary>
        /// 3. CreateOrUpdate - Combined method to create new goals or update existing ones
        /// POST /api/meta (create) or PUT /api/meta (update)
        /// </summary>
        /// <param name="meta">Dados da meta (ID presente = update, ID ausente = create)</param>
        /// <returns>Meta criada ou atualizada</returns>
        [HttpPost]
        [HttpPut]
        public async Task<IActionResult> CreateOrUpdate([FromBody] MetaViewModel meta)
        {
            string variaveis = JsonSerializer.Serialize(meta);
            try
            {
                string metodo = _controllerName + " CreateOrUpdate";
                var isUpdate = !string.IsNullOrWhiteSpace(meta?.Id);
                var operacao = isUpdate ? "Atualização" : "Criação";

                await RegistraAcao(metodo, $"{operacao} de meta", "", variaveis);

                if (meta == null)
                    return CriarRespostaErro("Dados da meta são obrigatórios", "INVALID_DATA");

                if (!ModelState.IsValid)
                    return CriarRespostaErro("Dados inválidos", "VALIDATION_ERROR");

                LogInfo($"Iniciando {operacao.ToLower()} de meta", nameof(CreateOrUpdate), _controllerName);

                var resultado = await _metaService.CreateOrUpdateAsync(meta);

                if (resultado == null)
                {
                    LogInfo($"Falha na {operacao.ToLower()} da meta", nameof(CreateOrUpdate), _controllerName);
                    return CriarRespostaErro($"Erro na {operacao.ToLower()} da meta", "OPERATION_FAILED");
                }

                LogInfo($"{operacao} de meta concluída com sucesso. ID: {resultado.Id}", nameof(CreateOrUpdate), _controllerName);

                if (isUpdate)
                    return CriarRespostaSucesso(resultado, "Meta atualizada com sucesso");
                else
                    return Created($"/api/meta/{resultado.Id}", new
                    {
                        success = true,
                        message = "Meta criada com sucesso",
                        data = resultado,
                        timestamp = DateTimeOffset.UtcNow
                    });
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        /// <summary>
        /// 4. Delete - Soft delete/deactivate a goal (set active flag to false)
        /// DELETE /api/meta/{id}
        /// </summary>
        /// <param name="id">ID da meta</param>
        /// <returns>Confirmação da exclusão</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(string id)
        {
            string variaveis = JsonSerializer.Serialize(new { id });
            try
            {
                string metodo = _controllerName + " Delete";
                await RegistraAcao(metodo, "Exclusão de meta", "", variaveis);

                if (string.IsNullOrWhiteSpace(id))
                    return CriarRespostaErro("ID é obrigatório", "INVALID_ID");

                LogInfo($"Iniciando exclusão da meta com ID: {id}", nameof(Delete), _controllerName);

                var sucesso = await _metaService.DeleteAsync(id);

                if (!sucesso)
                {
                    LogInfo($"Meta com ID {id} não encontrada para exclusão", nameof(Delete), _controllerName);
                    return CriarRespostaNaoEncontrado("Meta não encontrada");
                }

                LogInfo($"Meta com ID {id} excluída com sucesso", nameof(Delete), _controllerName);
                return CriarRespostaSucesso(new { id, excluida = true }, "Meta excluída com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        /// <summary>
        /// 5. GetFilterOptions - Return available filter options for the GetAll method
        /// GET /api/meta/filter-options
        /// </summary>
        /// <returns>Opções disponíveis para filtros</returns>
        [HttpGet("filter-options")]
        public async Task<IActionResult> GetFilterOptions()
        {
            string variaveis = "";
            try
            {
                string metodo = _controllerName + " GetFilterOptions";
                await RegistraAcao(metodo, "Busca de opções de filtro", "", variaveis);

                LogInfo("Iniciando busca de opções de filtro", nameof(GetFilterOptions), _controllerName);

                var opcoes = await _metaService.GetFilterOptionsAsync();

                LogInfo("Opções de filtro recuperadas com sucesso", nameof(GetFilterOptions), _controllerName);
                return CriarRespostaSucesso(opcoes, "Opções de filtro recuperadas com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        #endregion
    }
}
