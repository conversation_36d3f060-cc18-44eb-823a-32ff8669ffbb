using System.ComponentModel.DataAnnotations;

namespace Shared.ViewModels.Admin
{
    /// <summary>
    /// ViewModel para filtrar logs de erro no sistema Admin
    /// </summary>
    public class FiltroLogErroViewModel
    {
        /// <summary>
        /// Data de início para filtro por período
        /// </summary>
        [DataType(DataType.Date)]
        public DateTime? DataInicio { get; set; }

        /// <summary>
        /// Data de fim para filtro por período
        /// </summary>
        [DataType(DataType.Date)]
        public DateTime? DataFim { get; set; }

        /// <summary>
        /// ID do usuário para filtrar logs específicos
        /// </summary>
        public string? IdUsuario { get; set; }

        /// <summary>
        /// Nome do controller para filtrar logs
        /// </summary>
        public string? Controller { get; set; }

        /// <summary>
        /// Nome do método para filtrar logs
        /// </summary>
        public string? Metodo { get; set; }

        /// <summary>
        /// Texto para busca na mensagem de erro
        /// </summary>
        public string? TextoErro { get; set; }

        /// <summary>
        /// Número da página para paginação
        /// </summary>
        public int Pagina { get; set; } = 1;

        /// <summary>
        /// Quantidade de itens por página
        /// </summary>
        public int ItensPorPagina { get; set; } = 10;

        /// <summary>
        /// Campo para ordenação
        /// </summary>
        public string? OrdenarPor { get; set; } = "DtaCadastro";

        /// <summary>
        /// Direção da ordenação (ASC ou DESC)
        /// </summary>
        public string? DirecaoOrdenacao { get; set; } = "DESC";
    }
}
