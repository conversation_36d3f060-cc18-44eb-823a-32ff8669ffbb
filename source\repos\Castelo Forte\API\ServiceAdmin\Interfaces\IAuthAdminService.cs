using Shared.ViewModels.Admin;

namespace ServiceAdmin.Interfaces
{
    /// <summary>
    /// Interface para serviços de autenticação de administradores
    /// </summary>
    public interface IAuthAdminService
    {
        /// <summary>
        /// Autentica um administrador no sistema
        /// </summary>
        /// <param name="loginRequest">Dados de login do administrador</param>
        /// <returns>Resposta com token e dados do administrador</returns>
        Task<LoginAdminResponseViewModel> AutenticarAdminAsync(LoginAdminRequestViewModel loginRequest);

        /// <summary>
        /// Valida um token de administrador
        /// </summary>
        /// <param name="token">Token JWT a ser validado</param>
        /// <returns>Resposta com dados de validação</returns>
        Task<ValidateAdminTokenResponseViewModel> ValidarTokenAdminAsync(string token);
    }
}
