using CasteloForte.Controllers.BaseComplemento;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceClient.Interfaces;
using Shared.ViewModels.Client;
using System.Text.Json;

namespace CasteloForte.Controllers
{
    /// <summary>
    /// Controller para autenticação no sistema Client
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class AuthController : ControllerBaseComplemento<AuthController>
    {
        private readonly IAuthClientService _authService;
        private readonly string _controllerName = "AuthController";

        public AuthController(
            IAuthClientService authService,
            ILogger<AuthController> logger,
            ILogErroClientService? logErroClientService = null,
            IHistoricoUsuarioClientService? historicoUsuarioClientService = null)
            : base(logger, logErroClientService, historicoUsuarioClientService)
        {
            _authService = authService ?? throw new ArgumentNullException(nameof(authService));
        }

        #region POST - Autenticação

        /// <summary>
        /// Realiza o login do usuário
        /// </summary>
        /// <param name="loginRequest">Dados de login (CPF/email e senha)</param>
        /// <returns>Token JWT e dados do usuário</returns>
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<IActionResult> Login([FromBody] LoginRequestViewModel loginRequest)
        {
            string variaveis = JsonSerializer.Serialize(new { 
                CPF = loginRequest?.CPF?.Substring(0, Math.Min(3, loginRequest.CPF?.Length ?? 0)) + "***", 
                Email = loginRequest?.Email?.Substring(0, Math.Min(3, loginRequest.Email?.Length ?? 0)) + "***",
                LembrarLogin = loginRequest?.LembrarLogin 
            });

            try
            {
                string metodo = _controllerName + " Login";
                await RegistraAcao(metodo, "Tentativa de login no sistema", "", variaveis);

                if (loginRequest == null)
                    return CriarRespostaErro("Dados de login são obrigatórios", "INVALID_DATA");

                if (!ModelState.IsValid)
                    return CriarRespostaErro("Dados inválidos", "VALIDATION_ERROR");

                LogInfo("Iniciando processo de autenticação", nameof(Login), _controllerName);

                var response = await _authService.AutenticarAsync(loginRequest);

                LogInfo($"Autenticação realizada com sucesso para usuário: {response.Usuario.Nome}", nameof(Login), _controllerName);
                return CriarRespostaSucesso(response, "Login realizado com sucesso");
            }
            catch (UnauthorizedAccessException ex)
            {
                LogInfo($"Tentativa de login inválida: {ex.Message}", nameof(Login), _controllerName);
                return Unauthorized(new
                {
                    success = false,
                    message = "Credenciais inválidas",
                    timestamp = DateTimeOffset.UtcNow
                });
            }
            catch (ArgumentException ex)
            {
                return CriarRespostaErro(ex.Message, "INVALID_ARGUMENT");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        #endregion

        #region POST - Validação de Token

        /// <summary>
        /// Valida um token JWT
        /// </summary>
        /// <param name="validateRequest">Token a ser validado</param>
        /// <returns>Resultado da validação</returns>
        [HttpPost("validate-token")]
        [AllowAnonymous]
        public async Task<IActionResult> ValidateToken([FromBody] ValidateTokenRequestViewModel validateRequest)
        {
            string variaveis = JsonSerializer.Serialize(new { TokenLength = validateRequest?.Token?.Length ?? 0 });
            try
            {
                string metodo = _controllerName + " ValidateToken";
                await RegistraAcao(metodo, "Validação de token JWT", "", variaveis);

                if (validateRequest == null || string.IsNullOrWhiteSpace(validateRequest.Token))
                    return CriarRespostaErro("Token é obrigatório", "INVALID_TOKEN");

                LogInfo("Iniciando validação de token", nameof(ValidateToken), _controllerName);

                var response = await _authService.ValidarTokenAsync(validateRequest.Token);

                if (response.IsValid)
                {
                    LogInfo("Token validado com sucesso", nameof(ValidateToken), _controllerName);
                    return CriarRespostaSucesso(response, "Token válido");
                }
                else
                {
                    LogInfo($"Token inválido: {response.MensagemErro}", nameof(ValidateToken), _controllerName);
                    return Unauthorized(new
                    {
                        success = false,
                        message = response.MensagemErro ?? "Token inválido",
                        timestamp = DateTimeOffset.UtcNow
                    });
                }
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        #endregion

        #region POST - Refresh Token

        /// <summary>
        /// Renova um token JWT
        /// </summary>
        /// <param name="refreshRequest">Token atual a ser renovado</param>
        /// <returns>Novo token</returns>
        [HttpPost("refresh-token")]
        [AllowAnonymous]
        public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenRequestViewModel refreshRequest)
        {
            string variaveis = JsonSerializer.Serialize(new { TokenLength = refreshRequest?.Token?.Length ?? 0 });
            try
            {
                string metodo = _controllerName + " RefreshToken";
                await RegistraAcao(metodo, "Renovação de token JWT", "", variaveis);

                if (refreshRequest == null || string.IsNullOrWhiteSpace(refreshRequest.Token))
                    return CriarRespostaErro("Token é obrigatório", "INVALID_TOKEN");

                LogInfo("Iniciando renovação de token", nameof(RefreshToken), _controllerName);

                var response = await _authService.RenovarTokenAsync(refreshRequest.Token);

                LogInfo("Token renovado com sucesso", nameof(RefreshToken), _controllerName);
                return CriarRespostaSucesso(response, "Token renovado com sucesso");
            }
            catch (UnauthorizedAccessException ex)
            {
                LogInfo($"Falha na renovação de token: {ex.Message}", nameof(RefreshToken), _controllerName);
                return Unauthorized(new
                {
                    success = false,
                    message = "Token inválido para renovação",
                    timestamp = DateTimeOffset.UtcNow
                });
            }
            catch (ArgumentException ex)
            {
                return CriarRespostaErro(ex.Message, "INVALID_ARGUMENT");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        #endregion

        #region POST - Logout

        /// <summary>
        /// Realiza o logout do usuário
        /// </summary>
        /// <param name="logoutRequest">Dados de logout (opcional)</param>
        /// <returns>Confirmação do logout</returns>
        [HttpPost("logout")]
        [Authorize]
        public async Task<IActionResult> Logout([FromBody] LogoutRequestViewModel? logoutRequest = null)
        {
            string variaveis = JsonSerializer.Serialize(new { HasToken = !string.IsNullOrEmpty(logoutRequest?.Token) });
            try
            {
                string metodo = _controllerName + " Logout";
                await RegistraAcao(metodo, "Logout do usuário", "", variaveis);

                LogInfo("Iniciando processo de logout", nameof(Logout), _controllerName);

                // Obtém o token do header Authorization se não foi fornecido no body
                var token = logoutRequest?.Token;
                if (string.IsNullOrWhiteSpace(token))
                {
                    var authHeader = Request.Headers["Authorization"].FirstOrDefault();
                    if (!string.IsNullOrWhiteSpace(authHeader) && authHeader.StartsWith("Bearer "))
                    {
                        token = authHeader.Substring("Bearer ".Length).Trim();
                    }
                }

                var sucesso = await _authService.LogoutAsync(token);

                if (sucesso)
                {
                    LogInfo("Logout realizado com sucesso", nameof(Logout), _controllerName);
                    return CriarRespostaSucesso(null, "Logout realizado com sucesso");
                }
                else
                {
                    return CriarRespostaErro("Falha ao realizar logout", "LOGOUT_FAILED");
                }
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        #endregion

        #region GET - Informações do Usuário Atual

        /// <summary>
        /// Obtém informações do usuário autenticado
        /// </summary>
        /// <returns>Dados do usuário atual</returns>
        [HttpGet("me")]
        [Authorize]
        public async Task<IActionResult> GetCurrentUser()
        {
            string variaveis = "";
            try
            {
                string metodo = _controllerName + " GetCurrentUser";
                await RegistraAcao(metodo, "Busca de informações do usuário atual", "", variaveis);

                LogInfo("Buscando informações do usuário atual", nameof(GetCurrentUser), _controllerName);

                // Obtém o token do header Authorization
                var authHeader = Request.Headers["Authorization"].FirstOrDefault();
                if (string.IsNullOrWhiteSpace(authHeader) || !authHeader.StartsWith("Bearer "))
                {
                    return Unauthorized(new
                    {
                        success = false,
                        message = "Token de autorização não encontrado",
                        timestamp = DateTimeOffset.UtcNow
                    });
                }

                var token = authHeader.Substring("Bearer ".Length).Trim();
                var response = await _authService.ValidarTokenAsync(token);

                if (response.IsValid && response.Usuario != null)
                {
                    LogInfo($"Informações do usuário {response.Usuario.Nome} recuperadas", nameof(GetCurrentUser), _controllerName);
                    return CriarRespostaSucesso(response.Usuario, "Informações do usuário recuperadas");
                }
                else
                {
                    return Unauthorized(new
                    {
                        success = false,
                        message = "Token inválido",
                        timestamp = DateTimeOffset.UtcNow
                    });
                }
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        #endregion

        #region GET - Status da Autenticação

        /// <summary>
        /// Verifica o status da autenticação
        /// </summary>
        /// <returns>Status da autenticação</returns>
        [HttpGet("status")]
        [Authorize]
        public async Task<IActionResult> GetAuthStatus()
        {
            string variaveis = "";
            try
            {
                string metodo = _controllerName + " GetAuthStatus";
                await RegistraAcao(metodo, "Verificação do status da autenticação", "", variaveis);

                LogInfo("Verificando status da autenticação", nameof(GetAuthStatus), _controllerName);

                // Obtém o token do header Authorization
                var authHeader = Request.Headers["Authorization"].FirstOrDefault();
                if (string.IsNullOrWhiteSpace(authHeader) || !authHeader.StartsWith("Bearer "))
                {
                    return Unauthorized(new
                    {
                        success = false,
                        message = "Token de autorização não encontrado",
                        timestamp = DateTimeOffset.UtcNow
                    });
                }

                var token = authHeader.Substring("Bearer ".Length).Trim();
                var response = await _authService.ValidarTokenAsync(token);

                var status = new
                {
                    isAuthenticated = response.IsValid,
                    expiresAt = response.ExpiresAt,
                    usuario = response.Usuario?.Nome,
                    timestamp = DateTimeOffset.UtcNow
                };

                LogInfo($"Status da autenticação verificado: {response.IsValid}", nameof(GetAuthStatus), _controllerName);
                return CriarRespostaSucesso(status, "Status da autenticação verificado");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        #endregion
    }
}
