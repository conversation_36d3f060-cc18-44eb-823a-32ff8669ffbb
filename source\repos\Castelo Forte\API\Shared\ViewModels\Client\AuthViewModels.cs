using System.ComponentModel.DataAnnotations;

namespace Shared.ViewModels.Client
{
    /// <summary>
    /// ViewModel para requisição de login no sistema Client
    /// </summary>
    public class LoginRequestViewModel
    {
        /// <summary>
        /// CPF do usuário (formato: 000.000.000-00)
        /// </summary>
        [Required(ErrorMessage = "CPF é obrigatório")]
        [StringLength(14, MinimumLength = 11, ErrorMessage = "CPF deve ter entre 11 e 14 caracteres")]
        public string CPF { get; set; } = string.Empty;

        /// <summary>
        /// Email do usuário (alternativo ao CPF)
        /// </summary>
        [EmailAddress(ErrorMessage = "Email deve ter um formato válido")]
        public string? Email { get; set; }

        /// <summary>
        /// Senha do usuário
        /// </summary>
        [Required(ErrorMessage = "Senha é obrigatória")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "Senha deve ter entre 6 e 100 caracteres")]
        public string Senha { get; set; } = string.Empty;

        /// <summary>
        /// Indica se o login deve ser lembrado (opcional)
        /// </summary>
        public bool LembrarLogin { get; set; } = false;
    }

    /// <summary>
    /// ViewModel para resposta de login bem-sucedido
    /// </summary>
    public class LoginResponseViewModel
    {
        /// <summary>
        /// Token JWT para autenticação
        /// </summary>
        public string Token { get; set; } = string.Empty;

        /// <summary>
        /// Tipo do token (sempre "Bearer")
        /// </summary>
        public string TokenType { get; set; } = "Bearer";

        /// <summary>
        /// Data e hora de expiração do token
        /// </summary>
        public DateTime ExpiresAt { get; set; }

        /// <summary>
        /// Dados do usuário autenticado
        /// </summary>
        public UsuarioAutenticadoViewModel Usuario { get; set; } = new();

        /// <summary>
        /// Connection string criptografada do banco do usuário
        /// </summary>
        public string ConnectionString { get; set; } = string.Empty;

        /// <summary>
        /// Nome da base de dados do usuário
        /// </summary>
        public string NomeBaseDados { get; set; } = string.Empty;

        /// <summary>
        /// Indica se é o primeiro acesso do usuário
        /// </summary>
        public bool PrimeiroAcesso { get; set; } = false;

        /// <summary>
        /// Mensagem de boas-vindas ou informações adicionais
        /// </summary>
        public string? Mensagem { get; set; }
    }

    /// <summary>
    /// ViewModel com dados do usuário autenticado (sem informações sensíveis)
    /// </summary>
    public class UsuarioAutenticadoViewModel
    {
        /// <summary>
        /// ID único do usuário
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Nome completo do usuário
        /// </summary>
        public string Nome { get; set; } = string.Empty;

        /// <summary>
        /// Email do usuário
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// CPF do usuário (mascarado para segurança)
        /// </summary>
        public string CPF { get; set; } = string.Empty;

        /// <summary>
        /// Telefone do usuário
        /// </summary>
        public string? Telefone { get; set; }

        /// <summary>
        /// Data de nascimento do usuário
        /// </summary>
        public DateTime? DataNascimento { get; set; }

        /// <summary>
        /// Data do último acesso
        /// </summary>
        public DateTime? UltimoAcesso { get; set; }

        /// <summary>
        /// Indica se o usuário tem dois fatores habilitado
        /// </summary>
        public bool DoisFatoresHabilitado { get; set; } = false;

        /// <summary>
        /// Perfil/Role do usuário
        /// </summary>
        public string? Perfil { get; set; }

        /// <summary>
        /// Data de cadastro do usuário
        /// </summary>
        public DateTime DataCadastro { get; set; }
    }

    /// <summary>
    /// ViewModel para validação de token
    /// </summary>
    public class ValidateTokenRequestViewModel
    {
        /// <summary>
        /// Token a ser validado
        /// </summary>
        [Required(ErrorMessage = "Token é obrigatório")]
        public string Token { get; set; } = string.Empty;
    }

    /// <summary>
    /// ViewModel para resposta de validação de token
    /// </summary>
    public class ValidateTokenResponseViewModel
    {
        /// <summary>
        /// Indica se o token é válido
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Dados do usuário se o token for válido
        /// </summary>
        public UsuarioAutenticadoViewModel? Usuario { get; set; }

        /// <summary>
        /// Data de expiração do token
        /// </summary>
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// Mensagem de erro se o token for inválido
        /// </summary>
        public string? MensagemErro { get; set; }
    }

    /// <summary>
    /// ViewModel para refresh de token
    /// </summary>
    public class RefreshTokenRequestViewModel
    {
        /// <summary>
        /// Token atual a ser renovado
        /// </summary>
        [Required(ErrorMessage = "Token é obrigatório")]
        public string Token { get; set; } = string.Empty;
    }

    /// <summary>
    /// ViewModel para resposta de refresh de token
    /// </summary>
    public class RefreshTokenResponseViewModel
    {
        /// <summary>
        /// Novo token JWT
        /// </summary>
        public string Token { get; set; } = string.Empty;

        /// <summary>
        /// Tipo do token (sempre "Bearer")
        /// </summary>
        public string TokenType { get; set; } = "Bearer";

        /// <summary>
        /// Nova data de expiração
        /// </summary>
        public DateTime ExpiresAt { get; set; }
    }

    /// <summary>
    /// ViewModel para logout
    /// </summary>
    public class LogoutRequestViewModel
    {
        /// <summary>
        /// Token a ser invalidado (opcional, pode ser obtido do header)
        /// </summary>
        public string? Token { get; set; }
    }
}
