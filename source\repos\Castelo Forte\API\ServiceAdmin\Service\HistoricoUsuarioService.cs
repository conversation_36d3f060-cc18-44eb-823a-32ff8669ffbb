﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using RepositoryAdmin.Interfaces;
using RepositoryClient.Configuration.Interfaces;
using ServiceAdmin.Interfaces;
using ServiceAdmin.Repository.Generic;
using Shared.Entities.Admin;
using Shared.ViewModels.Admin;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServiceAdmin.Service
{
    public class HistoricoUsuarioService(
        IHistoricoUsuarioRepository historicoUsuarioAdminRepository,
        IHttpContextAccessor httpContextAccessor,
        IContextoMultiTenantService contextoMultiTenant,
        IMapper mapper) : GenericAdminService<HistoricoUsuarioViewModel, HistoricoUsuario>(historicoUsuarioAdminRepository, httpContextAccessor, mapper), IHistoricoUsuarioService
    {
        public async Task RegistrarAcao(string metodo, string action, string dadoAntigo, string dadoNovo)
        {
            try
            {
                HistoricoUsuario obj = new HistoricoUsuario()
                {
                    Acao = action,
                    objAntigo = dadoAntigo,
                    objNovo = dadoNovo,
                    Metodo = metodo,
                    IdUsuario = IdUsuarioLogado,
                    DtaCadastro = DateTime.Now,
                };

                await _repository.AdicionarAsync(obj);
            }
            catch (Exception)
            {
                throw;
            }
        }

        /// <summary>
        /// Busca histórico de ações por usuário
        /// </summary>
        /// <param name="idUsuario">ID do usuário</param>
        /// <returns>Lista de histórico de ações do usuário</returns>
        public async Task<IEnumerable<HistoricoUsuarioViewModel>> BuscarPorUsuarioAsync(string idUsuario)
        {
            try
            {
                var historico = await BuscarTodosAsync();
                return historico.Where(h => h.IdUsuario == idUsuario);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar histórico por usuário: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca histórico de ações por período
        /// </summary>
        /// <param name="dataInicio">Data de início</param>
        /// <param name="dataFim">Data de fim</param>
        /// <returns>Lista de histórico de ações no período</returns>
        public async Task<IEnumerable<HistoricoUsuarioViewModel>> BuscarPorPeriodoAsync(DateTime dataInicio, DateTime dataFim)
        {
            try
            {
                var historico = await BuscarTodosAsync();
                return historico.Where(h => h.DtaCadastro >= dataInicio && h.DtaCadastro <= dataFim);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar histórico por período: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca histórico com filtros e paginação
        /// </summary>
        /// <param name="filtro">Filtros para busca</param>
        /// <returns>Resultado paginado de histórico</returns>
        public async Task<ResultadoPaginadoViewModel<HistoricoUsuarioViewModel>> BuscarHistoricoFiltradoAsync(FiltroHistoricoViewModel filtro)
        {
            try
            {
                var historico = await BuscarTodosAsync();
                var query = historico.AsQueryable();

                // Aplicar filtros
                if (filtro.DataInicio.HasValue)
                    query = query.Where(h => h.DtaCadastro >= filtro.DataInicio.Value);

                if (filtro.DataFim.HasValue)
                    query = query.Where(h => h.DtaCadastro <= filtro.DataFim.Value);

                if (!string.IsNullOrWhiteSpace(filtro.IdUsuario))
                    query = query.Where(h => h.IdUsuario == filtro.IdUsuario);

                if (!string.IsNullOrWhiteSpace(filtro.Metodo))
                    query = query.Where(h => h.Metodo.Contains(filtro.Metodo, StringComparison.OrdinalIgnoreCase));

                if (!string.IsNullOrWhiteSpace(filtro.Acao))
                    query = query.Where(h => h.Acao.Contains(filtro.Acao, StringComparison.OrdinalIgnoreCase));

                if (!string.IsNullOrWhiteSpace(filtro.TextoBusca))
                {
                    query = query.Where(h =>
                        h.Acao.Contains(filtro.TextoBusca, StringComparison.OrdinalIgnoreCase) ||
                        h.Metodo.Contains(filtro.TextoBusca, StringComparison.OrdinalIgnoreCase));
                }

                // Aplicar ordenação
                query = filtro.OrdenarPor?.ToLower() switch
                {
                    "acao" => filtro.DirecaoOrdenacao?.ToUpper() == "ASC" ?
                        query.OrderBy(h => h.Acao) : query.OrderByDescending(h => h.Acao),
                    "metodo" => filtro.DirecaoOrdenacao?.ToUpper() == "ASC" ?
                        query.OrderBy(h => h.Metodo) : query.OrderByDescending(h => h.Metodo),
                    _ => query.OrderByDescending(h => h.DtaCadastro)
                };

                var totalRegistros = query.Count();
                var dadosPaginados = query
                    .Skip((filtro.Pagina - 1) * filtro.ItensPorPagina)
                    .Take(filtro.ItensPorPagina)
                    .ToList();

                return new ResultadoPaginadoViewModel<HistoricoUsuarioViewModel>(
                    dadosPaginados, totalRegistros, filtro.Pagina, filtro.ItensPorPagina);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar histórico filtrado: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca histórico por ação específica
        /// </summary>
        /// <param name="acao">Nome da ação</param>
        /// <returns>Lista de histórico da ação</returns>
        public async Task<IEnumerable<HistoricoUsuarioViewModel>> BuscarPorAcaoAsync(string acao)
        {
            try
            {
                var historico = await BuscarTodosAsync();
                return historico.Where(h => h.Acao.Contains(acao, StringComparison.OrdinalIgnoreCase));
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar histórico por ação: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Obtém estatísticas do histórico
        /// </summary>
        /// <returns>Estatísticas do histórico</returns>
        public async Task<object> ObterEstatisticasHistoricoAsync()
        {
            try
            {
                var historico = await BuscarTodosAsync();
                var totalRegistros = historico.Count();

                var hoje = DateTime.Today;
                var registrosHoje = historico.Count(h => h.DtaCadastro?.Date == hoje);
                var registrosUltimos7Dias = historico.Count(h => h.DtaCadastro >= hoje.AddDays(-7));
                var registrosUltimos30Dias = historico.Count(h => h.DtaCadastro >= hoje.AddDays(-30));

                var acoesMaisFrequentes = historico
                    .GroupBy(h => h.Acao)
                    .Select(g => new { Acao = g.Key, Quantidade = g.Count() })
                    .OrderByDescending(x => x.Quantidade)
                    .Take(10)
                    .ToList();

                var metodosMaisUsados = historico
                    .GroupBy(h => h.Metodo)
                    .Select(g => new { Metodo = g.Key, Quantidade = g.Count() })
                    .OrderByDescending(x => x.Quantidade)
                    .Take(10)
                    .ToList();

                var usuariosMaisAtivos = historico
                    .Where(h => !string.IsNullOrEmpty(h.IdUsuario))
                    .GroupBy(h => h.IdUsuario)
                    .Select(g => new { IdUsuario = g.Key, Quantidade = g.Count() })
                    .OrderByDescending(x => x.Quantidade)
                    .Take(10)
                    .ToList();

                return new
                {
                    TotalRegistros = totalRegistros,
                    RegistrosHoje = registrosHoje,
                    RegistrosUltimos7Dias = registrosUltimos7Dias,
                    RegistrosUltimos30Dias = registrosUltimos30Dias,
                    AcoesMaisFrequentes = acoesMaisFrequentes,
                    MetodosMaisUsados = metodosMaisUsados,
                    UsuariosMaisAtivos = usuariosMaisAtivos,
                    DataUltimaAtualizacao = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao obter estatísticas do histórico: {ex.Message}", ex);
            }
        }
    }
}
