import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'logger_service.dart';

/// Serviço para armazenamento seguro de dados sensíveis
class SecureStorageService {
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
    lOptions: LinuxOptions(),
    wOptions: WindowsOptions(),
    mOptions: MacOsOptions(),
  );

  // Chaves para armazenamento seguro
  static const String _authTokenKey = 'secure_auth_token';
  static const String _userCredentialsKey = 'secure_user_credentials';
  static const String _sessionDataKey = 'secure_session_data';
  static const String _appStateKey = 'secure_app_state';

  /// Salva o token de autenticação de forma segura
  static Future<void> saveAuthToken(String token) async {
    try {
      await _secureStorage.write(key: _authTokenKey, value: token);
      LoggerService.success('Token de autenticação salvo com segurança');
    } catch (e) {
      LoggerService.error('Erro ao salvar token de autenticação: $e');
      rethrow;
    }
  }

  /// Obtém o token de autenticação
  static Future<String?> getAuthToken() async {
    try {
      return await _secureStorage.read(key: _authTokenKey);
    } catch (e) {
      LoggerService.error('Erro ao obter token de autenticação: $e');
      return null;
    }
  }

  /// Salva as credenciais do usuário (sem senha)
  static Future<void> saveUserCredentials({
    required String cpf,
    required String email,
    required String nome,
  }) async {
    try {
      final credentials = {
        'cpf': cpf,
        'email': email,
        'nome': nome,
        'savedAt': DateTime.now().toIso8601String(),
      };
      
      await _secureStorage.write(
        key: _userCredentialsKey,
        value: json.encode(credentials),
      );
      
      LoggerService.success('Credenciais do usuário salvas com segurança');
    } catch (e) {
      LoggerService.error('Erro ao salvar credenciais do usuário: $e');
      rethrow;
    }
  }

  /// Obtém as credenciais do usuário
  static Future<Map<String, dynamic>?> getUserCredentials() async {
    try {
      final credentialsJson = await _secureStorage.read(key: _userCredentialsKey);
      if (credentialsJson != null) {
        return json.decode(credentialsJson) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      LoggerService.error('Erro ao obter credenciais do usuário: $e');
      return null;
    }
  }

  /// Salva dados da sessão
  static Future<void> saveSessionData(Map<String, dynamic> sessionData) async {
    try {
      await _secureStorage.write(
        key: _sessionDataKey,
        value: json.encode(sessionData),
      );
      LoggerService.success('Dados da sessão salvos com segurança');
    } catch (e) {
      LoggerService.error('Erro ao salvar dados da sessão: $e');
      rethrow;
    }
  }

  /// Obtém dados da sessão
  static Future<Map<String, dynamic>?> getSessionData() async {
    try {
      final sessionJson = await _secureStorage.read(key: _sessionDataKey);
      if (sessionJson != null) {
        return json.decode(sessionJson) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      LoggerService.error('Erro ao obter dados da sessão: $e');
      return null;
    }
  }

  /// Salva o estado do app (para detectar fechamento/reabertura)
  static Future<void> saveAppState({
    required bool wasClosedProperly,
    required DateTime lastActiveTime,
    String? lastRoute,
  }) async {
    try {
      final appState = {
        'wasClosedProperly': wasClosedProperly,
        'lastActiveTime': lastActiveTime.toIso8601String(),
        'lastRoute': lastRoute,
        'savedAt': DateTime.now().toIso8601String(),
      };
      
      await _secureStorage.write(
        key: _appStateKey,
        value: json.encode(appState),
      );
      
      LoggerService.info('Estado do app salvo');
    } catch (e) {
      LoggerService.error('Erro ao salvar estado do app: $e');
      rethrow;
    }
  }

  /// Obtém o estado do app
  static Future<Map<String, dynamic>?> getAppState() async {
    try {
      final appStateJson = await _secureStorage.read(key: _appStateKey);
      if (appStateJson != null) {
        return json.decode(appStateJson) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      LoggerService.error('Erro ao obter estado do app: $e');
      return null;
    }
  }

  /// Marca o app como fechado adequadamente
  static Future<void> markAppAsClosed({String? lastRoute}) async {
    await saveAppState(
      wasClosedProperly: true,
      lastActiveTime: DateTime.now(),
      lastRoute: lastRoute,
    );
  }

  /// Marca o app como ativo
  static Future<void> markAppAsActive() async {
    await saveAppState(
      wasClosedProperly: false,
      lastActiveTime: DateTime.now(),
    );
  }

  /// Verifica se o app foi fechado e reaberto
  static Future<bool> wasAppClosedAndReopened() async {
    try {
      final appState = await getAppState();
      if (appState == null) {
        // Primeira execução do app
        return false;
      }

      final wasClosedProperly = appState['wasClosedProperly'] as bool? ?? false;
      final lastActiveTimeStr = appState['lastActiveTime'] as String?;
      
      if (lastActiveTimeStr == null) return false;
      
      final lastActiveTime = DateTime.parse(lastActiveTimeStr);
      final timeDifference = DateTime.now().difference(lastActiveTime);
      
      // Se o app foi fechado adequadamente e passou mais de 30 segundos,
      // considera que foi fechado e reaberto
      return wasClosedProperly && timeDifference.inSeconds > 30;
    } catch (e) {
      LoggerService.error('Erro ao verificar estado do app: $e');
      return false;
    }
  }

  /// Obtém a última rota visitada
  static Future<String?> getLastRoute() async {
    try {
      final appState = await getAppState();
      return appState?['lastRoute'] as String?;
    } catch (e) {
      LoggerService.error('Erro ao obter última rota: $e');
      return null;
    }
  }

  /// Remove todos os dados seguros (logout completo)
  static Future<void> clearAllSecureData() async {
    try {
      await _secureStorage.deleteAll();
      LoggerService.success('Todos os dados seguros foram removidos');
    } catch (e) {
      LoggerService.error('Erro ao limpar dados seguros: $e');
      rethrow;
    }
  }

  /// Remove apenas o token de autenticação
  static Future<void> clearAuthToken() async {
    try {
      await _secureStorage.delete(key: _authTokenKey);
      LoggerService.success('Token de autenticação removido');
    } catch (e) {
      LoggerService.error('Erro ao remover token de autenticação: $e');
      rethrow;
    }
  }

  /// Verifica se existem credenciais salvas
  static Future<bool> hasStoredCredentials() async {
    try {
      final credentials = await getUserCredentials();
      return credentials != null && 
             credentials['cpf'] != null && 
             credentials['email'] != null;
    } catch (e) {
      LoggerService.error('Erro ao verificar credenciais armazenadas: $e');
      return false;
    }
  }

  /// Verifica se existe token de autenticação
  static Future<bool> hasAuthToken() async {
    try {
      final token = await getAuthToken();
      return token != null && token.isNotEmpty;
    } catch (e) {
      LoggerService.error('Erro ao verificar token de autenticação: $e');
      return false;
    }
  }
}
