import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

/// Serviço para armazenamento local
class StorageService {
  static late SharedPreferences _prefs;

  /// Inicializa o serviço de armazenamento
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  /// Salva um valor String
  static Future<bool> setString(String key, String value) async {
    return await _prefs.setString(key, value);
  }

  /// Obtém um valor String
  static String? getString(String key) {
    return _prefs.getString(key);
  }

  /// Salva um valor int
  static Future<bool> setInt(String key, int value) async {
    return await _prefs.setInt(key, value);
  }

  /// Obtém um valor int
  static int? getInt(String key) {
    return _prefs.getInt(key);
  }

  /// Salva um valor double
  static Future<bool> setDouble(String key, double value) async {
    return await _prefs.setDouble(key, value);
  }

  /// Obtém um valor double
  static double? getDouble(String key) {
    return _prefs.getDouble(key);
  }

  /// Salva um valor bool
  static Future<bool> setBool(String key, bool value) async {
    return await _prefs.setBool(key, value);
  }

  /// Obtém um valor bool
  static bool? getBool(String key) {
    return _prefs.getBool(key);
  }

  /// Salva uma lista de Strings
  static Future<bool> setStringList(String key, List<String> value) async {
    return await _prefs.setStringList(key, value);
  }

  /// Obtém uma lista de Strings
  static List<String>? getStringList(String key) {
    return _prefs.getStringList(key);
  }

  /// Salva um objeto como JSON
  static Future<bool> setObject(String key, Map<String, dynamic> value) async {
    return await _prefs.setString(key, json.encode(value));
  }

  /// Obtém um objeto de JSON
  static Map<String, dynamic>? getObject(String key) {
    final data = _prefs.getString(key);
    if (data != null) {
      return json.decode(data) as Map<String, dynamic>;
    }
    return null;
  }

  /// Remove um valor
  static Future<bool> remove(String key) async {
    return await _prefs.remove(key);
  }

  /// Limpa todos os valores
  static Future<bool> clear() async {
    return await _prefs.clear();
  }

  /// Verifica se uma chave existe
  static bool containsKey(String key) {
    return _prefs.containsKey(key);
  }
}
