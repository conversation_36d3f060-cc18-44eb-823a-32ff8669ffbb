import 'package:flutter/material.dart';
import '../../features/financial/presentation/financial_area_screen.dart';
import '../../features/notifications/presentation/notifications_screen.dart';
import '../theme/app_theme.dart';
import '../utils/navigation_helper.dart';
import '../../features/auth/data/auth_service.dart';

/// Header padrão para telas autenticadas
class AppHeader extends StatefulWidget {
  const AppHeader({super.key});

  @override
  State<AppHeader> createState() => _AppHeaderState();
}

class _AppHeaderState extends State<AppHeader> {
  final int _unreadNotifications = 2; // Simulando notificações não lidas

  @override
  Widget build(BuildContext context) {
    final user = AuthService.getCurrentUser();
    final userName = user?.nome ?? 'Usuário';

    // Determina a saudação baseada na hora do dia
    final hour = DateTime.now().hour;
    String greeting = '<PERSON><PERSON><PERSON>';
    if (hour < 12) {
      greeting = 'Bom dia';
    } else if (hour < 18) {
      greeting = 'Boa tarde';
    } else {
      greeting = 'Boa noite';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: const BoxDecoration(
        color: AppTheme.navyBlueColor,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(0),
          bottomRight: Radius.circular(0),
        ),
      ),
      child: Row(
        children: [
          // Avatar do usuário (clicável para ir ao perfil)
          GestureDetector(
            onTap: () => NavigationHelper.goToProfile(context),
            child: CircleAvatar(
              radius: 24,
              backgroundColor: AppTheme.goldColor,
              child: Text(
                userName.isNotEmpty ? userName[0].toUpperCase() : 'U',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.navyBlueColor,
                ),
              ),
            ),
          ),

          const SizedBox(width: 12),

          // Saudação e nome do usuário (também clicável para ir ao perfil)
          Expanded(
            child: GestureDetector(
              onTap: () => NavigationHelper.goToProfile(context),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    '$greeting,',
                    style: const TextStyle(fontSize: 14, color: Colors.white70),
                  ),
                  Text(
                    userName,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Botão financeiro
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const FinancialAreaScreen(),
                ),
              );
            },
            icon: const Icon(
              Icons.diamond_outlined,
              color: Colors.white,
              size: 24,
            ),
          ),

          // Botão de notificações com badge
          Stack(
            children: [
              IconButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const NotificationsScreen(),
                    ),
                  );
                },
                icon: const Icon(
                  Icons.notifications_outlined,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              // Badge de notificações (mostrar apenas se houver notificações)
              if (_unreadNotifications > 0)
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: AppTheme.errorColor,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: Text(
                      _unreadNotifications.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }
}
