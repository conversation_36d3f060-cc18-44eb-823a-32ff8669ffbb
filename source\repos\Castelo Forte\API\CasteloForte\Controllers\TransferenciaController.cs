using CasteloForte.Controllers.BaseComplemento;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceClient.Interfaces;
using Shared.ViewModels.Client;
using System.Text.Json;

namespace CasteloForte.Controllers
{
    /// <summary>
    /// Controller para gerenciamento de transferências/transações financeiras
    /// </summary>
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class TransferenciaController : ControllerBaseComplemento<TransferenciaController>
    {
        private readonly string _controllerName = "TransferenciaController";
        private readonly ITransferenciaService _transferenciaService;

        public TransferenciaController(
            ITransferenciaService transferenciaService,
            ILogger<TransferenciaController> logger,
            ILogErroClientService? logErroClientService = null,
            IHistoricoUsuarioClientService? historicoUsuarioClientService = null) 
            : base(logger, logErroClientService, historicoUsuarioClientService)
        {
            _transferenciaService = transferenciaService ?? throw new ArgumentNullException(nameof(transferenciaService));
        }

        #region Buscar

        /// <summary>
        /// Busca todas as transferências do usuário
        /// </summary>
        /// <param name="page">Página (padrão: 1)</param>
        /// <param name="limit">Limite por página (padrão: 20)</param>
        /// <param name="accountId">ID da conta para filtrar</param>
        /// <param name="categoryId">ID da categoria para filtrar</param>
        /// <param name="startDate">Data inicial para filtrar</param>
        /// <param name="endDate">Data final para filtrar</param>
        /// <returns>Lista de transferências</returns>
        [HttpGet]
        public async Task<IActionResult> BuscarTodas(
            [FromQuery] int page = 1,
            [FromQuery] int limit = 20,
            [FromQuery] string? accountId = null,
            [FromQuery] string? categoryId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            string variaveis = JsonSerializer.Serialize(new { page, limit, accountId, categoryId, startDate, endDate });
            try
            {
                string metodo = _controllerName + " BuscarTodas";
                await RegistraAcao(metodo, "Busca de transferências", "", variaveis);

                LogInfo($"Buscando transferências - página {page}, limite {limit}", nameof(BuscarTodas), _controllerName);
                
                var transferencias = await _transferenciaService.BuscarTodosAsync();
                
                // Aplicar filtros se fornecidos
                if (!string.IsNullOrEmpty(accountId))
                    transferencias = transferencias.Where(t => t.IdContaOrigem == accountId || t.IdContaDestino == accountId);
                
                if (!string.IsNullOrEmpty(categoryId))
                    transferencias = transferencias.Where(t => t.IdCategoria == categoryId);
                
                if (startDate.HasValue)
                    transferencias = transferencias.Where(t => t.DataTransferencia >= startDate.Value);
                
                if (endDate.HasValue)
                    transferencias = transferencias.Where(t => t.DataTransferencia <= endDate.Value);

                // Aplicar paginação
                var totalItems = transferencias.Count();
                var items = transferencias
                    .OrderByDescending(t => t.DataTransferencia)
                    .Skip((page - 1) * limit)
                    .Take(limit)
                    .ToList();

                LogInfo($"Encontradas {totalItems} transferências", nameof(BuscarTodas), _controllerName);
                
                var response = new
                {
                    data = items,
                    pagination = new
                    {
                        page,
                        limit,
                        total = totalItems,
                        totalPages = (int)Math.Ceiling((double)totalItems / limit)
                    }
                };

                return CriarRespostaSucesso(response, "Transferências obtidas com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        /// <summary>
        /// Busca uma transferência por ID
        /// </summary>
        /// <param name="id">ID da transferência</param>
        /// <returns>Transferência encontrada</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> BuscarPorId(string id)
        {
            string variaveis = JsonSerializer.Serialize(new { id });
            try
            {
                string metodo = _controllerName + " BuscarPorId";
                await RegistraAcao(metodo, "Busca de transferência por ID", "", variaveis);

                if (string.IsNullOrWhiteSpace(id))
                    return CriarRespostaErro("ID é obrigatório", "INVALID_ID");

                LogInfo($"Buscando transferência com ID: {id}", nameof(BuscarPorId), _controllerName);
                
                var transferencia = await _transferenciaService.BuscarPorIdAsync(id);
                
                if (transferencia == null)
                    return CriarRespostaNaoEncontrado("Transferência não encontrada");

                LogInfo($"Transferência encontrada: {transferencia.Descricao}", nameof(BuscarPorId), _controllerName);
                return CriarRespostaSucesso(transferencia, "Transferência obtida com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        #endregion

        #region Criar

        /// <summary>
        /// Cria uma nova transferência
        /// </summary>
        /// <param name="transferencia">Dados da transferência</param>
        /// <returns>Transferência criada</returns>
        [HttpPost]
        public async Task<IActionResult> Criar([FromBody] TransferenciaViewModel transferencia)
        {
            string variaveis = JsonSerializer.Serialize(transferencia);
            try
            {
                string metodo = _controllerName + " Criar";
                await RegistraAcao(metodo, "Criação de nova transferência", "", variaveis);

                if (transferencia == null)
                    return CriarRespostaErro("Dados da transferência são obrigatórios", "INVALID_DATA");

                if (!ModelState.IsValid)
                    return CriarRespostaErro("Dados inválidos", "VALIDATION_ERROR");

                LogInfo($"Iniciando criação de transferência: {transferencia.Descricao}", nameof(Criar), _controllerName);
                
                var transferenciaCriada = await _transferenciaService.AdicionarAsync(transferencia);
                
                if (transferenciaCriada == null)
                    return CriarRespostaErro("Falha ao criar transferência", "CREATE_ERROR");

                LogInfo($"Transferência criada com sucesso. ID: {transferenciaCriada.Id}", nameof(Criar), _controllerName);
                return CriarRespostaSucesso(transferenciaCriada, "Transferência criada com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        #endregion

        #region Atualizar

        /// <summary>
        /// Atualiza uma transferência existente
        /// </summary>
        /// <param name="id">ID da transferência</param>
        /// <param name="transferencia">Dados atualizados da transferência</param>
        /// <returns>Transferência atualizada</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> Atualizar(string id, [FromBody] TransferenciaViewModel transferencia)
        {
            string variaveis = JsonSerializer.Serialize(new { id, transferencia });
            try
            {
                string metodo = _controllerName + " Atualizar";
                await RegistraAcao(metodo, "Atualização de transferência", "", variaveis);

                if (string.IsNullOrWhiteSpace(id))
                    return CriarRespostaErro("ID é obrigatório", "INVALID_ID");

                if (transferencia == null)
                    return CriarRespostaErro("Dados da transferência são obrigatórios", "INVALID_DATA");

                if (!ModelState.IsValid)
                    return CriarRespostaErro("Dados inválidos", "VALIDATION_ERROR");

                LogInfo($"Iniciando atualização da transferência com ID: {id}", nameof(Atualizar), _controllerName);
                
                transferencia.Id = id;
                var transferenciaAtualizada = await _transferenciaService.EditarAsync(transferencia);
                
                if (transferenciaAtualizada == null)
                    return CriarRespostaNaoEncontrado("Transferência não encontrada");

                LogInfo($"Transferência atualizada com sucesso: {transferenciaAtualizada.Descricao}", nameof(Atualizar), _controllerName);
                return CriarRespostaSucesso(transferenciaAtualizada, "Transferência atualizada com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        #endregion

        #region Excluir

        /// <summary>
        /// Exclui uma transferência
        /// </summary>
        /// <param name="id">ID da transferência</param>
        /// <returns>Resultado da operação</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> Excluir(string id)
        {
            string variaveis = JsonSerializer.Serialize(new { id });
            try
            {
                string metodo = _controllerName + " Excluir";
                await RegistraAcao(metodo, "Exclusão de transferência", "", variaveis);

                if (string.IsNullOrWhiteSpace(id))
                    return CriarRespostaErro("ID é obrigatório", "INVALID_ID");

                LogInfo($"Iniciando exclusão da transferência com ID: {id}", nameof(Excluir), _controllerName);
                
                var sucesso = await _transferenciaService.RemoverAsync(id);
                
                if (!sucesso)
                    return CriarRespostaNaoEncontrado("Transferência não encontrada");

                LogInfo($"Transferência excluída com sucesso", nameof(Excluir), _controllerName);
                return CriarRespostaSucesso(null, "Transferência excluída com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        #endregion
    }
}
