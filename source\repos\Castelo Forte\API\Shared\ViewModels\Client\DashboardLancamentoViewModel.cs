using System.ComponentModel.DataAnnotations;

namespace Shared.ViewModels.Client
{
    /// <summary>
    /// ViewModel para lançamento financeiro na dashboard do cliente
    /// </summary>
    public class DashboardLancamentoViewModel
    {
        /// <summary>
        /// Identificador único do lançamento
        /// </summary>
        [Required]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Descrição do lançamento
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Descricao { get; set; } = string.Empty;

        /// <summary>
        /// Valor do lançamento
        /// </summary>
        [Required]
        public decimal Valor { get; set; }

        /// <summary>
        /// Data do lançamento
        /// </summary>
        [Required]
        public DateTime Data { get; set; }

        /// <summary>
        /// Tipo do lançamento ("RECEITA" ou "DESPESA")
        /// </summary>
        [Required]
        [StringLength(10)]
        public string Tipo { get; set; } = string.Empty;

        /// <summary>
        /// Categoria do lançamento
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Categoria { get; set; } = string.Empty;
    }
}
