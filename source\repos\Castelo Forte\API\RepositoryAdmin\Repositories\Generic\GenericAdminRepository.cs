﻿using MongoDB.Bson;
using MongoDB.Driver;
using RepositoryAdmin.Configuration;
using RepositoryAdmin.Interfaces.Generic;
using System.Linq.Expressions;

namespace RepositoryAdmin.Repositories.Generic
{
    public class GenericAdminRepository<TEntidade> : IGenericAdminRepository<TEntidade>, IDisposable
        where TEntidade : class
    {
        #region CONSTRUTOR
        protected readonly ContextBaseAdmin _context;
        protected readonly IMongoCollection<TEntidade> _collection;

        protected GenericAdminRepository(ContextBaseAdmin context, IMongoCollection<TEntidade> collection)
        {
            _context = context;
            _collection = collection;
        }
        #endregion

        #region CONSULTAS
        public async Task<IEnumerable<TEntidade?>> BuscarTodosAsync()
        {
            try
            {
                return await _collection.
                    Find(Builders<TEntidade>.Filter.Empty).
                    ToListAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro no método: {System.Reflection.MethodBase.GetCurrentMethod()!.Name}: {ex.Message}", ex);
            }
        }

        public async Task<IEnumerable<TEntidade?>> BuscarPorFiltroAsync(Expression<Func<TEntidade?, bool>> expression)
        {
            try
            {
                if (expression == null)
                    throw new ArgumentNullException(nameof(expression), "Expressão de filtro não pode ser nula");

                var a = await _collection.
                    Find(expression!).ToListAsync();

                return a;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro no método: {System.Reflection.MethodBase.GetCurrentMethod()!.Name}: {ex.Message}", ex);
            }
        }

        public async Task<IEnumerable<TEntidade?>> BuscarPorFiltroPaginadoAsync(Expression<Func<TEntidade?, bool>> expression, int page, int pageSize)
        {
            try
            {
                if (expression == null)
                    throw new ArgumentNullException(nameof(expression), "Expressão de filtro não pode ser nula");

                return await _collection.Find(expression!)
                    .Skip((page - 1) * pageSize)
                    .Limit(pageSize)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro no método: {System.Reflection.MethodBase.GetCurrentMethod()!.Name}: {ex.Message}", ex);
            }
        }

        public async Task<TEntidade?> BuscarPorIdAsync(string Id)
        {
            try
            {
                if (string.IsNullOrEmpty(Id))
                    throw new ArgumentNullException(nameof(Id), "O ID não pode ser nulo ou vazio");

                var filter = Builders<TEntidade>.Filter.Eq("Id", Id);
                return await _collection.Find(filter).FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro no método: {System.Reflection.MethodBase.GetCurrentMethod()!.Name}: {ex.Message}", ex);
            }
        }

        public async Task<TEntidade?> BuscarPrimeiroPorFiltroAsync(Expression<Func<TEntidade?, bool>> expression)
        {
            try
            {
                if (expression == null)
                    throw new ArgumentNullException(nameof(expression), "Expressão de filtro não pode ser nula");

                return await _collection.Find(expression!).FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro no método: {System.Reflection.MethodBase.GetCurrentMethod()!.Name}: {ex.Message}", ex);
            }
        }

        public async Task<int> BuscarContagemTotalPorFiltroAsync(Expression<Func<TEntidade?, bool>> expression)
        {
            try
            {
                return (int)await _collection.CountDocumentsAsync(expression!);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro no método: {System.Reflection.MethodBase.GetCurrentMethod()!.Name}: {ex.Message}", ex);
            }
        }

        public async Task<int> BuscarContagemTotalAsync()
        {
            try
            {
                return (int)await _collection.CountDocumentsAsync(Builders<TEntidade>.Filter.Empty);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro no método: {System.Reflection.MethodBase.GetCurrentMethod()!.Name}: {ex.Message}", ex);
            }
        }

        #endregion

        #region OPERAÇÕES
        public async Task<TEntidade?> AdicionarAsync(TEntidade Object)
        {
            try
            {
                if (Object == null)
                    throw new ArgumentNullException(nameof(Object), "Objeto a ser adicionado não pode ser nulo");

                await _collection.InsertOneAsync(Object);
                return Object;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro no método: {System.Reflection.MethodBase.GetCurrentMethod()!.Name}: {ex.Message}", ex);
            }
        }

        public async Task<TEntidade?> EditarAsync(TEntidade objeto)
        {
            try
            {
                if (objeto == null)
                    throw new ArgumentNullException(nameof(objeto), "Objeto a ser editado não pode ser nulo");

                var idProperty = typeof(TEntidade).GetProperty("Id");
                if (idProperty == null)
                    throw new InvalidOperationException("A entidade não possui uma propriedade 'Id'");

                var id = idProperty.GetValue(objeto)?.ToString();
                if (string.IsNullOrEmpty(id))
                    throw new InvalidOperationException("O ID da entidade não pode ser nulo ou vazio");

                var filter = Builders<TEntidade>.Filter.Eq("Id", id);
                var entidadeAtual = await _collection.Find(filter).FirstOrDefaultAsync();

                if (entidadeAtual == null)
                    throw new KeyNotFoundException($"Entidade com ID '{id}' não encontrada");

                var options = new ReplaceOptions { IsUpsert = false };
                var result = await _collection.ReplaceOneAsync(filter, objeto, options);

                if (result.MatchedCount == 0)
                    throw new KeyNotFoundException($"Entidade com ID '{id}' não encontrada para atualização");

                return result.ModifiedCount > 0 ? objeto : entidadeAtual;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro no método: {System.Reflection.MethodBase.GetCurrentMethod()!.Name}: {ex.Message}", ex);
            }
        }

        public async Task<TEntidade?> EditarComBsonAsync(TEntidade objeto)
        {
            try
            {
                if (objeto == null)
                    throw new ArgumentNullException(nameof(objeto), "Objeto a ser editado não pode ser nulo");

                var idProperty = typeof(TEntidade).GetProperty("Id");
                if (idProperty == null)
                    throw new InvalidOperationException("A entidade não possui uma propriedade 'Id'");

                var id = idProperty.GetValue(objeto)?.ToString();
                if (string.IsNullOrEmpty(id))
                    throw new InvalidOperationException("O ID da entidade não pode ser nulo ou vazio");

                var filter = Builders<TEntidade>.Filter.Eq("Id", id);
                var entidadeAtual = await _collection.Find(filter).FirstOrDefaultAsync();

                if (entidadeAtual == null)
                    throw new KeyNotFoundException($"Entidade com ID '{id}' não encontrada");

                var bsonDocument = objeto.ToBsonDocument();

                bsonDocument.Remove("_id");

                var updates = new List<UpdateDefinition<TEntidade>>();

                foreach (var element in bsonDocument.Elements)
                {
                    if (element.Name == "Id")
                        continue;

                    updates.Add(Builders<TEntidade>.Update.Set(element.Name, element.Value));
                }

                if (updates.Count == 0)
                    return entidadeAtual;

                var updateDefinition = Builders<TEntidade>.Update.Combine(updates);
                var updateOptions = new UpdateOptions { IsUpsert = false };

                var result = await _collection.UpdateOneAsync(filter, updateDefinition, updateOptions);

                if (result.MatchedCount == 0)
                    throw new KeyNotFoundException($"Entidade com ID '{id}' não encontrada para atualização");

                return result.ModifiedCount > 0 ? objeto : entidadeAtual;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro no método: {System.Reflection.MethodBase.GetCurrentMethod()!.Name}: {ex.Message}", ex);
            }
        }

        public async Task ExcluirAsync(TEntidade Object)
        {
            try
            {
                if (Object == null)
                    throw new ArgumentNullException(nameof(Object), "Objeto a ser excluído não pode ser nulo");

                var idProperty = typeof(TEntidade).GetProperty("Id");
                if (idProperty == null)
                    throw new InvalidOperationException("A entidade não possui uma propriedade 'Id'");

                var id = idProperty.GetValue(Object)?.ToString();
                if (string.IsNullOrEmpty(id))
                    throw new InvalidOperationException("O ID da entidade não pode ser nulo ou vazio");

                var filter = Builders<TEntidade>.Filter.Eq("Id", id);
                var result = await _collection.DeleteOneAsync(filter);

                if (result.DeletedCount == 0)
                    throw new KeyNotFoundException($"Entidade com ID '{id}' não encontrada");
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro no método: {System.Reflection.MethodBase.GetCurrentMethod()!.Name}: {ex.Message}", ex);
            }
        }
        #endregion

        #region AUXILIARES
        private bool disposed = false;

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposed)
                return;

            disposed = true;
        }
        #endregion
    }
}