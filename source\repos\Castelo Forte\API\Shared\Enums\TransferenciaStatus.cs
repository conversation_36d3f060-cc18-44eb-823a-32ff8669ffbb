using System.ComponentModel;

namespace Shared.Enums
{
    /// <summary>
    /// Status possíveis para uma transferência/transação
    /// </summary>
    public enum TransferenciaStatus
    {
        [Description("Pendente")]
        Pendente = 1,

        [Description("Processando")]
        Processando = 2,

        [Description("Concluída")]
        Concluida = 3,

        [Description("Falhou")]
        Falhou = 4,

        [Description("Cancelada")]
        Cancelada = 5,

        [Description("Estornada")]
        Estornada = 6
    }

    /// <summary>
    /// Tipo de origem da transação
    /// </summary>
    public enum OrigemTransacao
    {
        [Description("Manual")]
        Manual = 1,

        [Description("Recorrente")]
        Recorrente = 2,

        [Description("Externa")]
        Externa = 3,

        [Description("Importação")]
        Importacao = 4,

        [Description("API")]
        API = 5
    }

    /// <summary>
    /// Extensões para os enums de transferência
    /// </summary>
    public static class TransferenciaStatusExtensions
    {
        /// <summary>
        /// Retorna a descrição do status
        /// </summary>
        public static string GetDescription(this TransferenciaStatus status)
        {
            var field = status.GetType().GetField(status.ToString());
            var attribute = (DescriptionAttribute?)Attribute.GetCustomAttribute(field!, typeof(DescriptionAttribute));
            return attribute?.Description ?? status.ToString();
        }

        /// <summary>
        /// Retorna a descrição da origem
        /// </summary>
        public static string GetDescription(this OrigemTransacao origem)
        {
            var field = origem.GetType().GetField(origem.ToString());
            var attribute = (DescriptionAttribute?)Attribute.GetCustomAttribute(field!, typeof(DescriptionAttribute));
            return attribute?.Description ?? origem.ToString();
        }

        /// <summary>
        /// Verifica se a transação está em estado final
        /// </summary>
        public static bool IsEstadoFinal(this TransferenciaStatus status)
        {
            return status == TransferenciaStatus.Concluida || 
                   status == TransferenciaStatus.Falhou || 
                   status == TransferenciaStatus.Cancelada ||
                   status == TransferenciaStatus.Estornada;
        }

        /// <summary>
        /// Verifica se a transação pode ser cancelada
        /// </summary>
        public static bool PodeCancelar(this TransferenciaStatus status)
        {
            return status == TransferenciaStatus.Pendente || 
                   status == TransferenciaStatus.Processando;
        }

        /// <summary>
        /// Verifica se a transação foi bem-sucedida
        /// </summary>
        public static bool IsSucesso(this TransferenciaStatus status)
        {
            return status == TransferenciaStatus.Concluida;
        }

        /// <summary>
        /// Converte string para TransferenciaStatus
        /// </summary>
        public static TransferenciaStatus FromString(string status)
        {
            return status.ToLower() switch
            {
                "pendente" or "pending" => TransferenciaStatus.Pendente,
                "processando" or "processing" => TransferenciaStatus.Processando,
                "concluída" or "concluida" or "completed" or "success" => TransferenciaStatus.Concluida,
                "falhou" or "failed" or "error" => TransferenciaStatus.Falhou,
                "cancelada" or "cancelled" or "canceled" => TransferenciaStatus.Cancelada,
                "estornada" or "reversed" or "refunded" => TransferenciaStatus.Estornada,
                _ => TransferenciaStatus.Pendente // Default
            };
        }

        /// <summary>
        /// Converte string para OrigemTransacao
        /// </summary>
        public static OrigemTransacao OrigemFromString(string origem)
        {
            return origem.ToLower() switch
            {
                "manual" or "user" => OrigemTransacao.Manual,
                "recorrente" or "recurring" => OrigemTransacao.Recorrente,
                "externa" or "external" => OrigemTransacao.Externa,
                "importação" or "importacao" or "import" => OrigemTransacao.Importacao,
                "api" => OrigemTransacao.API,
                _ => OrigemTransacao.Manual // Default
            };
        }
    }
}
