using Microsoft.AspNetCore.Mvc;
using ServiceClient.Interfaces;

namespace CasteloForte.Controllers.BaseComplemento
{
    /// <summary>
    /// Controlador base que fornece funcionalidades comuns para todos os controladores do sistema Client.
    /// </summary>
    /// <typeparam name="T">Tipo do controlador que herda esta classe base.</typeparam>
    public abstract class ControllerBaseComplemento<T>(
        ILogger<T> logger,
        ILogErroClientService? logErroClientService = null,
        IHistoricoUsuarioClientService? historicoUsuarioClientService = null) : ControllerBase where T : class
    {
        #region CONSTRUTOR
        private readonly ILogger<T> _logger = logger;
        private readonly ILogErroClientService? _logErroClientService = logErroClientService;
        private readonly IHistoricoUsuarioClientService? _historicoUsuarioClientService = historicoUsuarioClientService;
        #endregion

        #region Loggers
        /// <summary>
        /// Registra um erro no sistema e retorna uma resposta BadRequest com a mensagem de erro.
        /// Este método não deve ser exposto como um endpoint de API.
        /// </summary>
        /// <param name="ex">Exceção que ocorreu.</param>
        /// <param name="metodo">Nome do método onde ocorreu o erro</param>
        /// <param name="controller">Nome do controller onde ocorreu o erro</param>
        /// <param name="variaveis">Variáveis relacionadas ao erro</param>
        /// <returns>BadRequestObjectResult com a mensagem de erro.</returns>
        [NonAction] // Este atributo indica que o método não é uma action de API
        public async Task<BadRequestObjectResult> LogErro(Exception ex, string metodo, string controller, string variaveis)
        {
            _logger.LogError($"[{controller}] - {metodo}: {ex.Message}. Variáveis: {variaveis}", DateTimeOffset.UtcNow);

            // Registra o erro no banco de dados multi-tenant se o serviço estiver disponível
            if (_logErroClientService != null)
            {
                await _logErroClientService.LogErroComContexto(ex, metodo, controller, variaveis);
            }

            return BadRequest(new
            {
                error = ex.Message,
                timestamp = DateTimeOffset.UtcNow
            });
        }

        /// <summary>
        /// Registra informações no log do sistema.
        /// Este método não deve ser exposto como um endpoint de API.
        /// </summary>
        /// <param name="mensagem">Mensagem a ser registrada</param>
        /// <param name="metodo">Nome do método</param>
        /// <param name="controller">Nome do controller</param>
        [NonAction] // Este atributo indica que o método não é uma action de API
        public void LogInfo(string mensagem, string metodo, string controller)
        {
            _logger.LogInformation($"[{controller}] - {metodo}: {mensagem}", DateTimeOffset.UtcNow);
        }

        /// <summary>
        /// Registra uma ação ou alteração realizada no sistema.
        /// Este método não deve ser exposto como um endpoint de API.
        /// </summary>
        /// <param name="metodo">Nome do método</param>
        /// <param name="acao">Descrição da ação</param>
        /// <param name="dadoAntigo">Dados antes da alteração</param>
        /// <param name="dadoNovo">Dados após a alteração</param>
        [NonAction] // Este atributo indica que o método não é uma action de API
        public async Task RegistraAcao(string metodo, string acao, string dadoAntigo, string dadoNovo)
        {
            // Registra a ação no banco de dados multi-tenant se o serviço estiver disponível
            if (_historicoUsuarioClientService != null)
            {
                await _historicoUsuarioClientService.RegistrarAcaoComContexto(metodo, acao, dadoAntigo, dadoNovo);
            }
        }

        /// <summary>
        /// Cria uma resposta de sucesso padronizada
        /// </summary>
        /// <param name="data">Dados a serem retornados</param>
        /// <param name="mensagem">Mensagem de sucesso</param>
        /// <returns>OkObjectResult com os dados e mensagem</returns>
        [NonAction]
        protected OkObjectResult CriarRespostaSucesso(object? data = null, string mensagem = "Operação realizada com sucesso")
        {
            return Ok(new
            {
                success = true,
                message = mensagem,
                data = data,
                timestamp = DateTimeOffset.UtcNow
            });
        }

        /// <summary>
        /// Cria uma resposta de erro padronizada
        /// </summary>
        /// <param name="mensagem">Mensagem de erro</param>
        /// <param name="codigo">Código do erro</param>
        /// <returns>BadRequestObjectResult com a mensagem de erro</returns>
        [NonAction]
        protected BadRequestObjectResult CriarRespostaErro(string mensagem, string? codigo = null)
        {
            return BadRequest(new
            {
                success = false,
                message = mensagem,
                code = codigo,
                timestamp = DateTimeOffset.UtcNow
            });
        }

        /// <summary>
        /// Cria uma resposta de não encontrado padronizada
        /// </summary>
        /// <param name="mensagem">Mensagem de não encontrado</param>
        /// <returns>NotFoundObjectResult com a mensagem</returns>
        [NonAction]
        protected NotFoundObjectResult CriarRespostaNaoEncontrado(string mensagem = "Recurso não encontrado")
        {
            return NotFound(new
            {
                success = false,
                message = mensagem,
                timestamp = DateTimeOffset.UtcNow
            });
        }
        #endregion
    }
}
