using CasteloForte.Controllers.BaseComplemento;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceClient.Interfaces;
using Shared.ViewModels.Client;
using System.Text.Json;

namespace CasteloForte.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class PlanoFinanceiroController : ControllerBaseComplemento<PlanoFinanceiroController>
    {
        private readonly string _controllerName = "PlanoFinanceiroController";
        private readonly IPlanoFinanceiroService _planoFinanceiroService;

        public PlanoFinanceiroController(
            IPlanoFinanceiroService planoFinanceiroService,
            ILogger<PlanoFinanceiroController> logger,
            ILogErroClientService? logErroClientService = null,
            IHistoricoUsuarioClientService? historicoUsuarioClientService = null) 
            : base(logger, logErroClientService, historicoUsuarioClientService)
        {
            _planoFinanceiroService = planoFinanceiroService ?? throw new ArgumentNullException(nameof(planoFinanceiroService));
        }

        #region Buscar

        /// <summary>
        /// Busca todos os planos financeiros ativos do usuário
        /// </summary>
        /// <returns>Lista de planos ativos</returns>
        [HttpGet]
        public async Task<IActionResult> BuscarTodos()
        {
            string variaveis = "";
            try
            {
                string metodo = _controllerName + " BuscarTodos";
                await RegistraAcao(metodo, "Busca de todos os planos financeiros ativos", "", variaveis);

                LogInfo("Iniciando busca de todos os planos financeiros ativos", nameof(BuscarTodos), _controllerName);
                
                var planos = await _planoFinanceiroService.BuscarAtivosAsync();
                
                LogInfo($"Busca concluída. {planos.Count()} planos encontrados", nameof(BuscarTodos), _controllerName);
                return CriarRespostaSucesso(planos, "Planos financeiros recuperados com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        /// <summary>
        /// Busca um plano financeiro por ID
        /// </summary>
        /// <param name="id">ID do plano</param>
        /// <returns>Plano encontrado</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> BuscarPorId(string id)
        {
            string variaveis = JsonSerializer.Serialize(new { id });
            try
            {
                string metodo = _controllerName + " BuscarPorId";
                await RegistraAcao(metodo, "Busca de plano financeiro por ID", "", variaveis);

                if (string.IsNullOrWhiteSpace(id))
                    return CriarRespostaErro("ID é obrigatório", "INVALID_ID");

                LogInfo($"Iniciando busca do plano financeiro com ID: {id}", nameof(BuscarPorId), _controllerName);
                
                var plano = await _planoFinanceiroService.BuscarPorIdAsync(id);
                
                if (plano == null)
                {
                    LogInfo($"Plano financeiro com ID {id} não encontrado", nameof(BuscarPorId), _controllerName);
                    return CriarRespostaNaoEncontrado("Plano financeiro não encontrado");
                }

                LogInfo($"Plano financeiro com ID {id} encontrado", nameof(BuscarPorId), _controllerName);
                return CriarRespostaSucesso(plano, "Plano financeiro recuperado com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        /// <summary>
        /// Busca planos financeiros por título
        /// </summary>
        /// <param name="titulo">Título do plano</param>
        /// <returns>Lista de planos com o título especificado</returns>
        [HttpGet("titulo/{titulo}")]
        public async Task<IActionResult> BuscarPorTitulo(string titulo)
        {
            string variaveis = JsonSerializer.Serialize(new { titulo });
            try
            {
                string metodo = _controllerName + " BuscarPorTitulo";
                await RegistraAcao(metodo, "Busca de planos financeiros por título", "", variaveis);

                if (string.IsNullOrWhiteSpace(titulo))
                    return CriarRespostaErro("Título é obrigatório", "INVALID_TITULO");

                LogInfo($"Iniciando busca de planos financeiros por título: {titulo}", nameof(BuscarPorTitulo), _controllerName);
                
                var planos = await _planoFinanceiroService.BuscarPorTituloAsync(titulo);
                
                LogInfo($"Busca concluída. {planos.Count()} planos encontrados para o título {titulo}", nameof(BuscarPorTitulo), _controllerName);
                return CriarRespostaSucesso(planos, "Planos por título recuperados com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        /// <summary>
        /// Busca planos financeiros por período de criação
        /// </summary>
        /// <param name="dataInicio">Data de início</param>
        /// <param name="dataFim">Data de fim</param>
        /// <returns>Lista de planos criados no período</returns>
        [HttpGet("periodo")]
        public async Task<IActionResult> BuscarPorPeriodoCriacao([FromQuery] DateTime dataInicio, [FromQuery] DateTime dataFim)
        {
            string variaveis = JsonSerializer.Serialize(new { dataInicio, dataFim });
            try
            {
                string metodo = _controllerName + " BuscarPorPeriodoCriacao";
                await RegistraAcao(metodo, "Busca de planos financeiros por período de criação", "", variaveis);

                if (dataInicio > dataFim)
                    return CriarRespostaErro("Data de início não pode ser maior que data de fim", "INVALID_DATE_RANGE");

                LogInfo($"Iniciando busca de planos por período de criação: {dataInicio:yyyy-MM-dd} a {dataFim:yyyy-MM-dd}", nameof(BuscarPorPeriodoCriacao), _controllerName);
                
                var planos = await _planoFinanceiroService.BuscarPorPeriodoCriacaoAsync(dataInicio, dataFim);
                
                LogInfo($"Busca concluída. {planos.Count()} planos encontrados no período", nameof(BuscarPorPeriodoCriacao), _controllerName);
                return CriarRespostaSucesso(planos, "Planos por período recuperados com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        #endregion

        #region Criar

        /// <summary>
        /// Cria um novo plano financeiro
        /// </summary>
        /// <param name="plano">Dados do plano</param>
        /// <returns>Plano criado</returns>
        [HttpPost]
        public async Task<IActionResult> Criar([FromBody] PlanoFinanceiroViewModel plano)
        {
            string variaveis = JsonSerializer.Serialize(plano);
            try
            {
                string metodo = _controllerName + " Criar";
                await RegistraAcao(metodo, "Criação de novo plano financeiro", "", variaveis);

                if (plano == null)
                    return CriarRespostaErro("Dados do plano são obrigatórios", "INVALID_DATA");

                if (!ModelState.IsValid)
                    return CriarRespostaErro("Dados inválidos", "VALIDATION_ERROR");

                LogInfo($"Iniciando criação de plano financeiro: {plano.Titulo}", nameof(Criar), _controllerName);
                
                var planoCriado = await _planoFinanceiroService.AdicionarAsync(plano);
                
                if (planoCriado == null)
                    return CriarRespostaErro("Falha ao criar plano financeiro", "CREATE_ERROR");

                LogInfo($"Plano financeiro criado com sucesso. ID: {planoCriado.Id}", nameof(Criar), _controllerName);
                return CriarRespostaSucesso(planoCriado, "Plano financeiro criado com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        #endregion

        #region Atualizar

        /// <summary>
        /// Atualiza um plano financeiro existente
        /// </summary>
        /// <param name="id">ID do plano</param>
        /// <param name="plano">Dados atualizados do plano</param>
        /// <returns>Plano atualizado</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> Atualizar(string id, [FromBody] PlanoFinanceiroViewModel plano)
        {
            string variaveis = JsonSerializer.Serialize(new { id, plano });
            try
            {
                string metodo = _controllerName + " Atualizar";
                await RegistraAcao(metodo, "Atualização de plano financeiro", "", variaveis);

                if (string.IsNullOrWhiteSpace(id))
                    return CriarRespostaErro("ID é obrigatório", "INVALID_ID");

                if (plano == null)
                    return CriarRespostaErro("Dados do plano são obrigatórios", "INVALID_DATA");

                if (!ModelState.IsValid)
                    return CriarRespostaErro("Dados inválidos", "VALIDATION_ERROR");

                if (id != plano.Id)
                    return CriarRespostaErro("ID da URL não confere com ID do objeto", "ID_MISMATCH");

                LogInfo($"Iniciando atualização do plano financeiro com ID: {id}", nameof(Atualizar), _controllerName);
                
                var planoAtualizado = await _planoFinanceiroService.AtualizarAsync(plano);
                
                if (planoAtualizado == null)
                    return CriarRespostaNaoEncontrado("Plano financeiro não encontrado");

                LogInfo($"Plano financeiro atualizado com sucesso. ID: {id}", nameof(Atualizar), _controllerName);
                return CriarRespostaSucesso(planoAtualizado, "Plano financeiro atualizado com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        #endregion

        #region Operações Especiais

        /// <summary>
        /// Duplica um plano financeiro existente
        /// </summary>
        /// <param name="id">ID do plano a ser duplicado</param>
        /// <param name="novoTitulo">Novo título para o plano duplicado</param>
        /// <returns>Plano duplicado</returns>
        [HttpPost("{id}/duplicar")]
        public async Task<IActionResult> Duplicar(string id, [FromBody] string novoTitulo)
        {
            string variaveis = JsonSerializer.Serialize(new { id, novoTitulo });
            try
            {
                string metodo = _controllerName + " Duplicar";
                await RegistraAcao(metodo, "Duplicação de plano financeiro", "", variaveis);

                if (string.IsNullOrWhiteSpace(id))
                    return CriarRespostaErro("ID é obrigatório", "INVALID_ID");

                if (string.IsNullOrWhiteSpace(novoTitulo))
                    return CriarRespostaErro("Novo título é obrigatório", "INVALID_TITULO");

                LogInfo($"Iniciando duplicação do plano financeiro {id} com novo título: {novoTitulo}", nameof(Duplicar), _controllerName);
                
                var planoDuplicado = await _planoFinanceiroService.DuplicarPlanoAsync(id, novoTitulo);
                
                if (planoDuplicado == null)
                    return CriarRespostaNaoEncontrado("Plano financeiro não encontrado");

                LogInfo($"Plano financeiro duplicado com sucesso. Novo ID: {planoDuplicado.Id}", nameof(Duplicar), _controllerName);
                return CriarRespostaSucesso(planoDuplicado, "Plano financeiro duplicado com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        /// <summary>
        /// Arquiva um plano financeiro
        /// </summary>
        /// <param name="id">ID do plano</param>
        /// <returns>Resultado da operação</returns>
        [HttpPatch("{id}/arquivar")]
        public async Task<IActionResult> Arquivar(string id)
        {
            string variaveis = JsonSerializer.Serialize(new { id });
            try
            {
                string metodo = _controllerName + " Arquivar";
                await RegistraAcao(metodo, "Arquivamento de plano financeiro", "", variaveis);

                if (string.IsNullOrWhiteSpace(id))
                    return CriarRespostaErro("ID é obrigatório", "INVALID_ID");

                LogInfo($"Iniciando arquivamento do plano financeiro {id}", nameof(Arquivar), _controllerName);
                
                var sucesso = await _planoFinanceiroService.ArquivarPlanoAsync(id);
                
                if (!sucesso)
                    return CriarRespostaNaoEncontrado("Plano financeiro não encontrado");

                LogInfo($"Plano financeiro {id} arquivado com sucesso", nameof(Arquivar), _controllerName);
                return CriarRespostaSucesso(new { id }, "Plano financeiro arquivado com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        /// <summary>
        /// Restaura um plano financeiro arquivado
        /// </summary>
        /// <param name="id">ID do plano</param>
        /// <returns>Resultado da operação</returns>
        [HttpPatch("{id}/restaurar")]
        public async Task<IActionResult> Restaurar(string id)
        {
            string variaveis = JsonSerializer.Serialize(new { id });
            try
            {
                string metodo = _controllerName + " Restaurar";
                await RegistraAcao(metodo, "Restauração de plano financeiro", "", variaveis);

                if (string.IsNullOrWhiteSpace(id))
                    return CriarRespostaErro("ID é obrigatório", "INVALID_ID");

                LogInfo($"Iniciando restauração do plano financeiro {id}", nameof(Restaurar), _controllerName);
                
                var sucesso = await _planoFinanceiroService.RestaurarPlanoAsync(id);
                
                if (!sucesso)
                    return CriarRespostaNaoEncontrado("Plano financeiro não encontrado");

                LogInfo($"Plano financeiro {id} restaurado com sucesso", nameof(Restaurar), _controllerName);
                return CriarRespostaSucesso(new { id }, "Plano financeiro restaurado com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        #endregion

        #region Inativar

        /// <summary>
        /// Inativa um plano financeiro
        /// </summary>
        /// <param name="id">ID do plano</param>
        /// <returns>Resultado da operação</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> Inativar(string id)
        {
            string variaveis = JsonSerializer.Serialize(new { id });
            try
            {
                string metodo = _controllerName + " Inativar";
                await RegistraAcao(metodo, "Inativação de plano financeiro", "", variaveis);

                if (string.IsNullOrWhiteSpace(id))
                    return CriarRespostaErro("ID é obrigatório", "INVALID_ID");

                LogInfo($"Iniciando inativação do plano financeiro com ID: {id}", nameof(Inativar), _controllerName);
                
                var sucesso = await _planoFinanceiroService.InativarAsync(id);
                
                if (!sucesso)
                    return CriarRespostaNaoEncontrado("Plano financeiro não encontrado");

                LogInfo($"Plano financeiro inativado com sucesso. ID: {id}", nameof(Inativar), _controllerName);
                return CriarRespostaSucesso(new { id }, "Plano financeiro inativado com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        #endregion


    }
}
