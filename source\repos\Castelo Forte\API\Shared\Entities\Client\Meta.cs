﻿using MongoDB.Bson.Serialization.Attributes;
using Shared.Entities.Base;
using Shared.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Shared.Entities.Client
{
    /// <summary>
    /// Entidade que representa uma meta financeira do usuário
    /// </summary>
    public class Meta : BaseEntidade
    {
        /// <summary>
        /// Nome da meta
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Nome { get; set; } = "";

        /// <summary>
        /// Descrição detalhada da meta
        /// </summary>
        [StringLength(500)]
        public string Descricao { get; set; } = "";

        /// <summary>
        /// Data de abertura/criação da meta
        /// </summary>
        [DataType(DataType.Date)]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime DataAbertura { get; set; } = DateTime.Now;

        /// <summary>
        /// Data-alvo para conclusão da meta
        /// </summary>
        [DataType(DataType.Date)]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime DataConclusao { get; set; }

        /// <summary>
        /// Data real de conclusão (quando a meta for atingida)
        /// </summary>
        [DataType(DataType.Date)]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime? DataConclusaoReal { get; set; }

        /// <summary>
        /// Valor-alvo da meta
        /// </summary>
        [Required]
        public decimal ValorAlvo { get; set; } = 0;

        /// <summary>
        /// Progresso atual da meta
        /// </summary>
        public decimal ProgressoAtual { get; set; } = 0;

        /// <summary>
        /// Status atual da meta
        /// </summary>
        public MetaStatus Status { get; set; } = MetaStatus.Ativa;

        /// <summary>
        /// Ícone da meta
        /// </summary>
        public string IconeMeta { get; set; } = "";

        /// <summary>
        /// Cor da meta (hexadecimal)
        /// </summary>
        public string CorMeta { get; set; } = "#4CAF50";

        /// <summary>
        /// IDs das categorias associadas à meta (relação muitos-para-muitos)
        /// </summary>
        public List<string> CategoriasAssociadas { get; set; } = new List<string>();

        /// <summary>
        /// Indica se é uma meta mensal recorrente
        /// </summary>
        public bool IsMetaMensal { get; set; } = false;

        /// <summary>
        /// Percentual de progresso da meta (0-100)
        /// </summary>
        public decimal PercentualProgresso => ValorAlvo > 0 ? Math.Min((ProgressoAtual / ValorAlvo) * 100, 100) : 0;

        /// <summary>
        /// Valor restante para atingir a meta
        /// </summary>
        public decimal ValorRestante => Math.Max(ValorAlvo - ProgressoAtual, 0);

        /// <summary>
        /// Verifica se a meta foi atingida
        /// </summary>
        public bool IsAtingida => ProgressoAtual >= ValorAlvo;

        /// <summary>
        /// Verifica se a meta está vencida
        /// </summary>
        public bool IsVencida => DateTime.Now > DataConclusao && !IsAtingida;

        /// <summary>
        /// Dias restantes para a meta
        /// </summary>
        public int DiasRestantes => Math.Max((DataConclusao - DateTime.Now).Days, 0);

        /// <summary>
        /// Atualiza o progresso da meta
        /// </summary>
        /// <param name="novoProgresso">Novo valor de progresso</param>
        public void AtualizarProgresso(decimal novoProgresso)
        {
            if (Status.PodeReceberProgresso())
            {
                ProgressoAtual = Math.Max(novoProgresso, 0);

                // Verifica se a meta foi atingida
                if (IsAtingida && Status != MetaStatus.Concluida)
                {
                    Status = MetaStatus.Concluida;
                    DataConclusaoReal = DateTime.Now;
                }
            }
        }

        /// <summary>
        /// Adiciona valor ao progresso atual
        /// </summary>
        /// <param name="valor">Valor a ser adicionado</param>
        public void AdicionarProgresso(decimal valor)
        {
            AtualizarProgresso(ProgressoAtual + valor);
        }
    }
}
