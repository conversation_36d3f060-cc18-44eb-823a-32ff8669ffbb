﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="FFMpegCore" Version="5.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Features" Version="2.3.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.4" />
    <PackageReference Include="MongoDB.Bson" Version="3.2.1" />
    <PackageReference Include="MongoDB.Driver" Version="3.1.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="RestSharp" Version="106.11.7" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Configuration\" />
    <Folder Include="Templates\" />
  </ItemGroup>

</Project>
