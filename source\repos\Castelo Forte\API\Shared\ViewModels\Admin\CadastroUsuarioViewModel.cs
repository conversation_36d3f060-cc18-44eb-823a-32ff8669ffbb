using System.ComponentModel.DataAnnotations;

namespace Shared.ViewModels.Admin
{
    /// <summary>
    /// ViewModel específico para cadastro de usuário, sem campos que devem ser gerados automaticamente
    /// </summary>
    public class CadastroUsuarioViewModel
    {
        #region Dados Obrigatórios do Usuário

        /// <summary>
        /// Nome completo do usuário
        /// </summary>
        [Required(ErrorMessage = "O nome é obrigatório")]
        [StringLength(100, MinimumLength = 2, ErrorMessage = "O nome deve ter entre 2 e 100 caracteres")]
        public string Nome { get; set; } = "";

        /// <summary>
        /// Email do usuário
        /// </summary>
        [Required(ErrorMessage = "O email é obrigatório")]
        [EmailAddress(ErrorMessage = "Email deve ter um formato válido")]
        [StringLength(100, ErrorMessage = "O email deve ter no máximo 100 caracteres")]
        public string Email { get; set; } = "";

        /// <summary>
        /// CPF do usuário (apenas números ou com formatação)
        /// </summary>
        [Required(ErrorMessage = "O CPF é obrigatório")]
        [StringLength(14, MinimumLength = 11, ErrorMessage = "CPF deve ter entre 11 e 14 caracteres")]
        public string Cpf { get; set; } = "";

        /// <summary>
        /// Celular do usuário
        /// </summary>
        [Required(ErrorMessage = "O celular é obrigatório")]
        [StringLength(15, MinimumLength = 10, ErrorMessage = "Celular deve ter entre 10 e 15 caracteres")]
        public string Celular { get; set; } = "";

        /// <summary>
        /// Data de nascimento do usuário
        /// </summary>
        [Required(ErrorMessage = "A data de nascimento é obrigatória")]
        public DateTime DtaNascimento { get; set; }

        /// <summary>
        /// Senha do usuário
        /// </summary>
        [Required(ErrorMessage = "A senha é obrigatória")]
        [StringLength(100, MinimumLength = 8, ErrorMessage = "A senha deve ter entre 8 e 100 caracteres")]
        public string Senha { get; set; } = "";

        #endregion

        #region Termos e Condições

        /// <summary>
        /// Indica se o usuário aceitou os termos e condições
        /// </summary>
        [Required(ErrorMessage = "A aceitação dos termos e condições é obrigatória")]
        [Range(typeof(bool), "true", "true", ErrorMessage = "Você deve aceitar os termos e condições")]
        public bool FlgTermosECondicoes { get; set; }

        #endregion

        #region Campos Opcionais

        /// <summary>
        /// Indica se o usuário quer habilitar dois fatores (opcional)
        /// </summary>
        public bool FlgDoisFatores { get; set; } = false;

        #endregion

        #region Campos Gerados Automaticamente (Não Obrigatórios na Entrada)

        /// <summary>
        /// Token de acesso (gerado automaticamente pelo sistema)
        /// </summary>
        public string? TokenAcesso { get; set; }

        /// <summary>
        /// Nome da base de dados (gerado automaticamente pelo sistema)
        /// </summary>
        public string? NomeBaseDados { get; set; }

        /// <summary>
        /// Connection string (gerada automaticamente pelo sistema)
        /// </summary>
        public string? ConnectionString { get; set; }

        /// <summary>
        /// ID do perfil financeiro (atribuído automaticamente pelo sistema)
        /// </summary>
        public string? IdPerfilFinanceiro { get; set; }

        /// <summary>
        /// Data de aceite dos termos (preenchida automaticamente)
        /// </summary>
        public DateTime? DtaTermosECondicoes { get; set; }

        /// <summary>
        /// Data de geração do token (preenchida automaticamente)
        /// </summary>
        public DateTime? DtaTokenAcessoGerado { get; set; }

        /// <summary>
        /// Data do último acesso (preenchida automaticamente)
        /// </summary>
        public DateTime? DtaUltimoAcesso { get; set; }

        #endregion

        #region Campos de Controle (Preenchidos Automaticamente)

        /// <summary>
        /// ID do usuário (gerado automaticamente)
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// Data de cadastro (preenchida automaticamente)
        /// </summary>
        public DateTime? DtaCadastro { get; set; }

        /// <summary>
        /// Flag ativo (definido automaticamente como true)
        /// </summary>
        public bool FlgAtivo { get; set; } = true;

        #endregion

        #region Perfil Financeiro (Opcionais)

        /// <summary>
        /// Porcentagem do perfil Coração Inquieto
        /// </summary>
        [Range(0, 100, ErrorMessage = "A porcentagem deve estar entre 0 e 100")]
        public int CoracaoInquieto { get; set; } = 0;

        /// <summary>
        /// Porcentagem do perfil Construtor Analítico
        /// </summary>
        [Range(0, 100, ErrorMessage = "A porcentagem deve estar entre 0 e 100")]
        public int ConstrutorAnalitico { get; set; } = 0;

        /// <summary>
        /// Porcentagem do perfil Visionário Ousado
        /// </summary>
        [Range(0, 100, ErrorMessage = "A porcentagem deve estar entre 0 e 100")]
        public int VisionarioOusado { get; set; } = 0;

        /// <summary>
        /// Porcentagem do perfil Explorador Generoso
        /// </summary>
        [Range(0, 100, ErrorMessage = "A porcentagem deve estar entre 0 e 100")]
        public int ExploradorGeneroso { get; set; } = 0;

        /// <summary>
        /// Porcentagem do perfil Estrategista Consciente
        /// </summary>
        [Range(0, 100, ErrorMessage = "A porcentagem deve estar entre 0 e 100")]
        public int EstrategistaConsciente { get; set; } = 0;

        #endregion

        /// <summary>
        /// Converte este ViewModel para UsuarioViewModel
        /// </summary>
        /// <returns>UsuarioViewModel preenchido</returns>
        public UsuarioViewModel ToUsuarioViewModel()
        {
            return new UsuarioViewModel
            {
                Id = this.Id,
                Nome = this.Nome,
                Email = this.Email,
                Cpf = this.Cpf,
                Celular = this.Celular,
                DtaNascimento = this.DtaNascimento,
                Senha = this.Senha,
                FlgTermosECondicoes = this.FlgTermosECondicoes,
                FlgDoisFatores = this.FlgDoisFatores,
                TokenAcesso = this.TokenAcesso ?? "",
                NomeBaseDados = this.NomeBaseDados ?? "",
                ConnectionString = this.ConnectionString ?? "",
                IdPerfilFinanceiro = this.IdPerfilFinanceiro ?? "",
                DtaTermosECondicoes = this.DtaTermosECondicoes,
                DtaTokenAcessoGerado = this.DtaTokenAcessoGerado,
                DtaUltimoAcesso = this.DtaUltimoAcesso,
                DtaCadastro = this.DtaCadastro,
                FlgAtivo = this.FlgAtivo,
                CoracaoInquieto = this.CoracaoInquieto,
                ConstrutorAnalitico = this.ConstrutorAnalitico,
                VisionarioOusado = this.VisionarioOusado,
                ExploradorGeneroso = this.ExploradorGeneroso,
                EstrategistaConsciente = this.EstrategistaConsciente
            };
        }
    }
}
