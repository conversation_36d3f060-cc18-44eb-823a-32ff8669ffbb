import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:casteloforte/core/utils/constants.dart';
import 'package:casteloforte/core/utils/navigation_helper.dart';

void main() {
  group('App Router Tests', () {
    testWidgets('Constants are properly defined for routes', (
      WidgetTester tester,
    ) async {
      // Test that all route constants are defined
      expect(AppConstants.splashRoute, equals('/'));
      expect(AppConstants.loginRoute, equals('/login'));
      expect(AppConstants.dashboardRoute, equals('/dashboard'));
      expect(AppConstants.profileRoute, equals('/profile'));
      expect(AppConstants.settingsRoute, equals('/settings'));
      expect(AppConstants.securityRoute, equals('/security'));
      expect(AppConstants.transactionsRoute, equals('/transactions'));
      expect(AppConstants.reportsRoute, equals('/reports'));
      expect(AppConstants.goalsRoute, equals('/goals'));
      expect(AppConstants.categoriesRoute, equals('/categories'));
      expect(AppConstants.accountsRoute, equals('/accounts'));
      expect(AppConstants.helpRoute, equals('/help'));
      expect(AppConstants.notificationsRoute, equals('/notifications'));
    });

    testWidgets('Navigation helper class exists and has required methods', (
      WidgetTester tester,
    ) async {
      // Test that NavigationHelper class exists and has the expected methods
      expect(NavigationHelper.goToLogin, isA<Function>());
      expect(NavigationHelper.goToDashboard, isA<Function>());
      expect(NavigationHelper.goToProfile, isA<Function>());
      expect(NavigationHelper.goToSettings, isA<Function>());
      // expect(NavigationHelper.goToSecurity, isA<Function>());
      expect(NavigationHelper.goToTransactions, isA<Function>());
      expect(NavigationHelper.goToReports, isA<Function>());
      expect(NavigationHelper.goToGoals, isA<Function>());
      // expect(NavigationHelper.goToCategories, isA<Function>());
      // expect(NavigationHelper.goToAccounts, isA<Function>());
      // expect(NavigationHelper.goToHelp, isA<Function>());
      expect(NavigationHelper.goToNotifications, isA<Function>());
      // expect(NavigationHelper.goBack, isA<Function>());
      // expect(NavigationHelper.goReplace, isA<Function>());
      // expect(NavigationHelper.goAndClearStack, isA<Function>());
      // expect(NavigationHelper.canGoBack, isA<Function>());
      // expect(NavigationHelper.getCurrentRoute, isA<Function>());
    });

    testWidgets('Basic widget creation without router works', (
      WidgetTester tester,
    ) async {
      // Test basic widget creation to ensure no compilation errors
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Center(child: Text('Router Test: ${AppConstants.appName}')),
          ),
        ),
      );

      expect(find.text('Router Test: Castelo Forte'), findsOneWidget);
    });
  });
}
