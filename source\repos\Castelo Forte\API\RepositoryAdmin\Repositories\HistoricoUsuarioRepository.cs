﻿using RepositoryAdmin.Configuration;
using RepositoryAdmin.Interfaces;
using RepositoryAdmin.Repositories.Generic;
using Shared.Entities.Admin;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepositoryAdmin.Repositories
{
    public class HistoricoUsuarioRepository(ContextBaseAdmin context) : GenericAdminRepository<HistoricoUsuario>(context, context.HistoricoUsuarioCollection), IHistoricoUsuarioRepository
    {
    }
}
