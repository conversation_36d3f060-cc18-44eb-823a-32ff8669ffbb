import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/categoria_model.dart';

class CategoriaService {
  static const String baseUrl = 'https://localhost:7001/api/categoria'; // Ajuste conforme necessário
  
  // Headers padrão para as requisições
  Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    // Adicione aqui o token de autenticação quando necessário
    // 'Authorization': 'Bearer $token',
  };

  /// Busca todas as categorias
  Future<List<CategoriaModel>> buscarTodas() async {
    try {
      final response = await http.get(
        Uri.parse(baseUrl),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => CategoriaModel.fromJson(json)).toList();
      } else {
        throw Exception('Erro ao buscar categorias: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro de conexão: $e');
    }
  }

  /// Busca categoria por ID
  Future<CategoriaModel?> buscarPorId(String id) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/$id'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return CategoriaModel.fromJson(data);
      } else if (response.statusCode == 404) {
        return null;
      } else {
        throw Exception('Erro ao buscar categoria: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro de conexão: $e');
    }
  }

  /// Busca categorias por tipo (Receita ou Despesa)
  Future<List<CategoriaModel>> buscarPorTipo(String tipo) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/tipo/$tipo'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => CategoriaModel.fromJson(json)).toList();
      } else {
        throw Exception('Erro ao buscar categorias por tipo: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro de conexão: $e');
    }
  }

  /// Busca apenas categorias ativas
  Future<List<CategoriaModel>> buscarAtivas() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/ativas'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => CategoriaModel.fromJson(json)).toList();
      } else {
        throw Exception('Erro ao buscar categorias ativas: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro de conexão: $e');
    }
  }

  /// Busca categorias por nome
  Future<List<CategoriaModel>> buscarPorNome(String nome) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/buscar/$nome'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => CategoriaModel.fromJson(json)).toList();
      } else {
        throw Exception('Erro ao buscar categorias por nome: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro de conexão: $e');
    }
  }

  /// Cria uma nova categoria
  Future<CategoriaModel> criar(CategoriaCreateUpdateModel categoria) async {
    try {
      final response = await http.post(
        Uri.parse(baseUrl),
        headers: _headers,
        body: json.encode(categoria.toJson()),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return CategoriaModel.fromJson(data);
      } else {
        throw Exception('Erro ao criar categoria: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro de conexão: $e');
    }
  }

  /// Atualiza uma categoria existente
  Future<CategoriaModel> atualizar(String id, CategoriaCreateUpdateModel categoria) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/$id'),
        headers: _headers,
        body: json.encode(categoria.toJson()),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return CategoriaModel.fromJson(data);
      } else {
        throw Exception('Erro ao atualizar categoria: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro de conexão: $e');
    }
  }

  /// Exclui uma categoria (soft delete)
  Future<bool> excluir(String id) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/$id'),
        headers: _headers,
      );

      return response.statusCode == 204;
    } catch (e) {
      throw Exception('Erro de conexão: $e');
    }
  }

  /// Reativa uma categoria
  Future<bool> reativar(String id) async {
    try {
      final response = await http.patch(
        Uri.parse('$baseUrl/$id/reativar'),
        headers: _headers,
      );

      return response.statusCode == 204;
    } catch (e) {
      throw Exception('Erro de conexão: $e');
    }
  }

  /// Verifica se uma categoria pode ser excluída
  Future<bool> podeExcluir(String id) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/$id/pode-excluir'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        return json.decode(response.body) as bool;
      } else {
        throw Exception('Erro ao verificar se categoria pode ser excluída: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro de conexão: $e');
    }
  }

  /// Busca subcategorias de uma categoria pai
  Future<List<CategoriaModel>> buscarSubcategorias(String idCategoriaPai) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/$idCategoriaPai/subcategorias'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => CategoriaModel.fromJson(json)).toList();
      } else {
        throw Exception('Erro ao buscar subcategorias: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Erro de conexão: $e');
    }
  }
}
