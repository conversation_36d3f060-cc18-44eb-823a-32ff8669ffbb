import 'package:flutter/material.dart';
import 'edit_transaction_screen.dart';
import 'create_recurring_transaction_screen.dart';

class TransactionDetailsScreen extends StatelessWidget {
  final Map<String, dynamic> transaction;

  const TransactionDetailsScreen({super.key, required this.transaction});

  @override
  Widget build(BuildContext context) {
    final isExpense = transaction['isExpense'] ?? true;
    final amount = transaction['amount'] ?? 0.0;
    final title = transaction['title'] ?? 'Transação';
    final category = transaction['category'] ?? 'Sem categoria';
    final date = transaction['date'] ?? DateTime.now();
    final account = transaction['account'] ?? 'Conta não informada';
    final description = transaction['description'] ?? '';

    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Detalhes da Transação',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit, color: Colors.white),
            onPressed: () => _editTransaction(context),
          ),
          IconButton(
            icon: const Icon(Icons.delete, color: Colors.red),
            onPressed: () => _deleteTransaction(context),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Valor principal
            Center(
              child: Column(
                children: [
                  Icon(
                    isExpense ? Icons.arrow_downward : Icons.arrow_upward,
                    color: isExpense ? Colors.red : Colors.green,
                    size: 48,
                  ),
                  const SizedBox(height: 10),
                  Text(
                    '${isExpense ? '-' : '+'}R\$ ${amount.toStringAsFixed(2)}',
                    style: TextStyle(
                      color: isExpense ? Colors.red : Colors.green,
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 40),

            // Informações detalhadas
            _buildDetailCard('Informações Gerais', [
              _buildDetailRow('Categoria', category, Icons.label),
              _buildDetailRow('Data', _formatDate(date), Icons.calendar_today),
              _buildDetailRow('Conta', account, Icons.account_balance),
              _buildDetailRow(
                'Tipo',
                isExpense ? 'Despesa' : 'Receita',
                isExpense ? Icons.remove_circle : Icons.add_circle,
              ),
            ]),

            const SizedBox(height: 20),

            if (description.isNotEmpty)
              _buildDetailCard('Descrição', [
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white10,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    description,
                    style: const TextStyle(color: Colors.white70),
                  ),
                ),
              ]),

            const SizedBox(height: 20),

            // Ações
            _buildDetailCard('Ações', [
              _buildActionButton(
                'Editar Transação',
                Icons.edit,
                () => _editTransaction(context),
              ),
              const SizedBox(height: 10),
              _buildActionButton(
                'Duplicar Transação',
                Icons.copy,
                () => _duplicateTransaction(context),
              ),
              const SizedBox(height: 10),
              _buildActionButton(
                'Criar Recorrência',
                Icons.repeat,
                () => _createRecurrence(context),
              ),
              const SizedBox(height: 10),
              _buildActionButton(
                'Excluir Transação',
                Icons.delete,
                () => _deleteTransaction(context),
                isDestructive: true,
              ),
            ]),

            const SizedBox(height: 20),

            // Informações técnicas
            _buildDetailCard('Informações Técnicas', [
              _buildDetailRow(
                'ID',
                transaction['id'] ?? 'N/A',
                Icons.fingerprint,
              ),
              _buildDetailRow(
                'Criado em',
                _formatDateTime(date),
                Icons.access_time,
              ),
              _buildDetailRow('Status', 'Concluída', Icons.check_circle),
            ]),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailCard(String title, List<Widget> children) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF16213E),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.white10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 15),
          ...children,
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: Colors.white54, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(color: Colors.white54, fontSize: 12),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    String label,
    IconData icon,
    VoidCallback onTap, {
    bool isDestructive = false,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isDestructive
              ? Colors.red.withValues(alpha: 0.1)
              : Colors.white10,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isDestructive
                ? Colors.red.withValues(alpha: 0.3)
                : Colors.white10,
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isDestructive ? Colors.red : Colors.white70,
              size: 20,
            ),
            const SizedBox(width: 12),
            Text(
              label,
              style: TextStyle(
                color: isDestructive ? Colors.red : Colors.white70,
                fontSize: 16,
              ),
            ),
            const Spacer(),
            Icon(
              Icons.arrow_forward_ios,
              color: isDestructive ? Colors.red : Colors.white54,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  String _formatDateTime(DateTime date) {
    return '${_formatDate(date)} às ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _editTransaction(BuildContext context) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditTransactionScreen(transaction: transaction),
      ),
    );

    if (result != null && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Transação atualizada com sucesso!'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _duplicateTransaction(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213E),
        title: const Text(
          'Duplicar Transação',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          'Deseja criar uma cópia desta transação? Você poderá editar os dados depois.',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancelar',
              style: TextStyle(color: Colors.white70),
            ),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context); // Close dialog

              // Criar cópia da transação
              final duplicatedTransaction = {
                ...transaction,
                'id': DateTime.now().millisecondsSinceEpoch.toString(),
                'descricao': '${transaction['descricao']} - Cópia',
                'data': DateTime.now(),
                'dataCriacao': DateTime.now(),
              };

              // Navegar para edição da transação duplicada
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) =>
                      EditTransactionScreen(transaction: duplicatedTransaction),
                ),
              );

              if (result != null && context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Transação duplicada e editada com sucesso!'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            child: const Text(
              'Duplicar',
              style: TextStyle(color: Color(0xFF4CAF50)),
            ),
          ),
        ],
      ),
    );
  }

  void _createRecurrence(BuildContext context) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            CreateRecurringTransactionScreen(baseTransaction: transaction),
      ),
    );

    if (result != null && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Recorrência criada com sucesso!'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _deleteTransaction(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213E),
        title: const Text(
          'Excluir Transação',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          'Tem certeza que deseja excluir esta transação? Esta ação não pode ser desfeita.',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancelar',
              style: TextStyle(color: Colors.white70),
            ),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context); // Close dialog

              // Simular exclusão no backend
              await _simulateBackendDeletion();

              if (context.mounted) {
                Navigator.pop(context); // Return to previous screen
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Transação excluída com sucesso!'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            child: const Text('Excluir', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  Future<void> _simulateBackendDeletion() async {
    // Simular delay de rede
    await Future.delayed(const Duration(milliseconds: 500));

    // Aqui seria feita a chamada real para o backend
    // Exemplo: await TransactionService.deleteTransaction(transaction['id']);

    // Por enquanto, apenas simulamos o sucesso
    // Log seria feito aqui em produção
  }
}
