import 'package:flutter/material.dart';

/// Modelo de formulário para criação/edição de categoria
class CategoryFormModel {
  String? id;
  String nome;
  String descricao;
  String tipo; // "RECEITA" ou "DESPESA"
  int cor; // Cor em formato ARGB32
  int icone; // Código do ícone
  int ordem;
  String? idCategoriaPai;
  double? limiteGastos;
  String? periodoLimite;
  String? observacoes;

  CategoryFormModel({
    this.id,
    required this.nome,
    this.descricao = '',
    required this.tipo,
    this.cor = 0xFF4CAF50,
    this.icone = 0xE7FD,
    this.ordem = 0,
    this.idCategoriaPai,
    this.limiteGastos,
    this.periodoLimite,
    this.observacoes,
  });

  /// Cria uma instância a partir de JSON
  factory CategoryFormModel.fromJson(Map<String, dynamic> json) {
    return CategoryFormModel(
      id: json['id']?.toString(),
      nome: json['nome']?.toString() ?? '',
      descricao: json['descricao']?.toString() ?? '',
      tipo: json['tipo']?.toString() ?? 'DESPESA',
      cor: json['cor'] as int? ?? 0xFF4CAF50,
      icone: json['icone'] as int? ?? 0xE7FD,
      ordem: json['ordem'] as int? ?? 0,
      idCategoriaPai: json['idCategoriaPai']?.toString(),
      limiteGastos: json['limiteGastos'] != null
          ? (json['limiteGastos'] as num).toDouble()
          : null,
      periodoLimite: json['periodoLimite']?.toString(),
      observacoes: json['observacoes']?.toString(),
    );
  }

  /// Converte para JSON (formato da API)
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'nome': nome.trim(),
      'descricao': descricao.trim(),
      'tipo': _mapTipoToEnum(tipo), // Corrigido para usar valores corretos
      'cor': cor, // Já é int
      'icone': icone, // Já é int
      'ordem': ordem,
    };

    if (id != null) {
      json['id'] = id;
    }

    if (idCategoriaPai != null && idCategoriaPai!.isNotEmpty) {
      json['idCategoriaPai'] = idCategoriaPai;
    }

    if (limiteGastos != null) {
      json['limiteGastos'] = limiteGastos;
    }

    if (periodoLimite != null && periodoLimite!.isNotEmpty) {
      json['periodoLimite'] = _mapPeriodoLimiteToEnum(periodoLimite!);
    }

    if (observacoes != null && observacoes!.isNotEmpty) {
      json['observacoes'] = observacoes;
    }

    return json;
  }

  /// Mapeia tipo para enum da API
  int _mapTipoToEnum(String tipo) {
    switch (tipo.toUpperCase()) {
      case 'RECEITA':
        return 1; // TipoCategoria.Receita
      case 'DESPESA':
        return 2; // TipoCategoria.Despesa
      case 'META':
        return 3; // TipoCategoria.Meta
      default:
        return 2; // Despesa como padrão
    }
  }

  /// Mapeia período do limite para enum da API
  int _mapPeriodoLimiteToEnum(String periodo) {
    switch (periodo.toLowerCase()) {
      case 'diario':
      case 'diário':
        return 1; // PeriodoLimite.Diario
      case 'semanal':
        return 2; // PeriodoLimite.Semanal
      case 'mensal':
        return 3; // PeriodoLimite.Mensal
      case 'anual':
        return 4; // PeriodoLimite.Anual
      default:
        return 3; // Mensal como padrão
    }
  }

  /// Cria uma instância a partir de CategoryModel
  factory CategoryFormModel.fromCategoryModel(dynamic categoryModel) {
    return CategoryFormModel(
      id: categoryModel.id,
      nome: categoryModel.nome,
      descricao: categoryModel.descricao,
      tipo: categoryModel.tipo,
      cor: categoryModel.cor,
      icone: categoryModel.icone,
      ordem: categoryModel.ordem,
      idCategoriaPai: categoryModel.idCategoriaPai,
      limiteGastos: categoryModel.limiteGastos,
      periodoLimite: categoryModel.periodoLimite,
      observacoes: categoryModel.observacoes,
    );
  }

  /// Retorna a cor como objeto Color
  Color get colorValue => Color(cor);

  /// Retorna o ícone como IconData
  IconData get iconData => IconData(icone, fontFamily: 'MaterialIcons');

  /// Verifica se é uma categoria de receita
  bool get isReceita => tipo.toUpperCase() == 'RECEITA';

  /// Verifica se é uma categoria de despesa
  bool get isDespesa => tipo.toUpperCase() == 'DESPESA';

  /// Retorna o tipo formatado
  String get tipoFormatado => isReceita ? 'Receita' : 'Despesa';

  /// Retorna o limite de gastos formatado
  String? get limiteGastosFormatado {
    if (limiteGastos == null) return null;
    return 'R\$ ${limiteGastos!.toStringAsFixed(2).replaceAll('.', ',')}';
  }

  /// Valida se os dados do formulário estão corretos
  List<String> validate() {
    final errors = <String>[];

    if (nome.trim().isEmpty) {
      errors.add('Nome é obrigatório');
    } else if (nome.trim().length < 2) {
      errors.add('Nome deve ter pelo menos 2 caracteres');
    } else if (nome.trim().length > 100) {
      errors.add('Nome deve ter no máximo 100 caracteres');
    }

    if (descricao.length > 500) {
      errors.add('Descrição deve ter no máximo 500 caracteres');
    }

    if (tipo != 'RECEITA' && tipo != 'DESPESA') {
      errors.add('Tipo deve ser RECEITA ou DESPESA');
    }

    if (ordem < 0) {
      errors.add('Ordem deve ser um número positivo');
    }

    if (limiteGastos != null && limiteGastos! < 0) {
      errors.add('Limite de gastos deve ser um valor positivo');
    }

    if (limiteGastos != null &&
        (periodoLimite == null || periodoLimite!.isEmpty)) {
      errors.add('Período do limite é obrigatório quando há limite de gastos');
    }

    if (observacoes != null && observacoes!.length > 1000) {
      errors.add('Observações devem ter no máximo 1000 caracteres');
    }

    return errors;
  }

  /// Verifica se o formulário é válido
  bool get isValid => validate().isEmpty;

  /// Cria uma cópia com campos modificados
  CategoryFormModel copyWith({
    String? id,
    String? nome,
    String? descricao,
    String? tipo,
    int? cor,
    int? icone,
    int? ordem,
    String? idCategoriaPai,
    double? limiteGastos,
    String? periodoLimite,
    String? observacoes,
  }) {
    return CategoryFormModel(
      id: id ?? this.id,
      nome: nome ?? this.nome,
      descricao: descricao ?? this.descricao,
      tipo: tipo ?? this.tipo,
      cor: cor ?? this.cor,
      icone: icone ?? this.icone,
      ordem: ordem ?? this.ordem,
      idCategoriaPai: idCategoriaPai ?? this.idCategoriaPai,
      limiteGastos: limiteGastos ?? this.limiteGastos,
      periodoLimite: periodoLimite ?? this.periodoLimite,
      observacoes: observacoes ?? this.observacoes,
    );
  }

  @override
  String toString() {
    return 'CategoryFormModel(id: $id, nome: $nome, tipo: $tipo)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CategoryFormModel &&
        other.id == id &&
        other.nome == nome &&
        other.tipo == tipo &&
        other.cor == cor &&
        other.icone == icone;
  }

  @override
  int get hashCode {
    return Object.hash(id, nome, tipo, cor, icone);
  }
}
