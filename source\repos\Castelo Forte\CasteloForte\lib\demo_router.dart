import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'core/services/storage_service.dart';
import 'core/theme/app_theme.dart';
import 'core/utils/constants.dart';
import 'routes/app_router.dart';

/// Demo app para testar o sistema de rotas
class RouterDemoApp extends StatelessWidget {
  const RouterDemoApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: '${AppConstants.appName} - Router Demo',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      debugShowCheckedModeBanner: false,
      routerConfig: AppRouter.router,
    );
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Inicializa o serviço de storage
  await StorageService.init();
  
  runApp(const ProviderScope(child: RouterDemo<PERSON><PERSON>()));
}
