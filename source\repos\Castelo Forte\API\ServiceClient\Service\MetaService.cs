using AutoMapper;
using Microsoft.AspNetCore.Http;
using RepositoryClient.Interfaces;
using ServiceClient.Interfaces;
using ServiceClient.Repository.Generic;
using Shared.Entities.Client;
using Shared.ViewModels.Client;
using Shared.Enums;
using System.Linq;

namespace ServiceClient.Service
{
    /// <summary>
    /// Serviço para Meta no contexto Client (multi-tenant)
    /// Refatorado para conter apenas 6 métodos essenciais
    /// </summary>
    public class MetaService : GenericClientService<MetaViewModel, Meta>, IMetaService
    {
        private readonly IMetaRepository _metaRepository;
        private readonly ICategoriaService _categoriaService;

        public MetaService(
            IMetaRepository metaRepository,
            IHttpContextAccessor httpContextAccessor,
            IMapper mapper,
            ICategoriaService categoriaService) : base(metaRepository, httpContextAccessor, mapper)
        {
            _metaRepository = metaRepository ?? throw new ArgumentNullException(nameof(metaRepository));
            _categoriaService = categoriaService ?? throw new ArgumentNullException(nameof(categoriaService));
        }

        #region 6 Métodos Essenciais

        /// <summary>
        /// 1. GetById - Retrieve a specific goal by its ID
        /// </summary>
        public async Task<MetaViewModel?> GetByIdAsync(string id)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(id))
                    throw new ArgumentException("ID é obrigatório", nameof(id));

                return await base.BuscarPorIdAsync(id);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar meta por ID: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 2. GetAll - Retrieve all goals with filtering capabilities
        /// </summary>
        public async Task<MetaPaginatedResponseViewModel> GetAllAsync(MetaFilterViewModel? filtros = null)
        {
            try
            {
                // Validar filtros
                if (filtros != null && !filtros.IsValid())
                {
                    var erro = $"Filtros inválidos: {filtros.GetValidationError()}";
                    throw new ArgumentException(erro);
                }

                // Buscar todas as metas
                var todasMetas = await BuscarTodosAsync();
                var metasQuery = todasMetas.AsQueryable();

                // Aplicar filtros
                if (filtros != null)
                {
                    metasQuery = AplicarFiltros(metasQuery, filtros);
                }

                // Aplicar ordenação
                metasQuery = AplicarOrdenacao(metasQuery, filtros?.Ordenacao?.ToString(), filtros?.OrdenacaoDecrescente ?? false);

                // Calcular estatísticas
                var estatisticas = CalcularEstatisticasResultado(metasQuery);

                // Retornar todas as metas filtradas (sem paginação)
                var metasFiltradas = metasQuery.ToList();

                return new MetaPaginatedResponseViewModel
                {
                    Metas = metasFiltradas,
                    FiltrosAplicados = filtros,
                    Estatisticas = estatisticas
                };
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar metas: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 3. CreateOrUpdate - Combined method to create new goals or update existing ones
        /// </summary>
        public async Task<MetaViewModel?> CreateOrUpdateAsync(MetaViewModel meta)
        {
            try
            {
                if (meta == null)
                    throw new ArgumentNullException(nameof(meta));

                // Determinar se é criação ou atualização baseado na presença do ID
                var isUpdate = !string.IsNullOrWhiteSpace(meta.Id);

                if (isUpdate)
                {
                    // Verificar se a meta existe
                    var metaExistente = await base.BuscarPorIdAsync(meta.Id!);
                    if (metaExistente == null)
                        throw new ArgumentException("Meta não encontrada para atualização", nameof(meta));

                    // Atualizar
                    meta.DtaAlteracao = DateTime.UtcNow;
                    return await base.EditarAsync(meta);
                }
                else
                {
                    // Criar nova meta
                    meta.DataAbertura = DateTime.UtcNow;
                    meta.DtaCadastro = DateTime.UtcNow;
                    meta.FlgAtivo = true;
                    meta.Status = MetaStatus.Ativa;

                    return await base.AdicionarAsync(meta);
                }
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao criar/atualizar meta: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 4. Delete - Soft delete/deactivate a goal (set active flag to false)
        /// </summary>
        public async Task<bool> DeleteAsync(string id)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(id))
                    throw new ArgumentException("ID é obrigatório", nameof(id));

                var meta = await base.BuscarPorIdAsync(id);
                if (meta == null)
                    return false;

                meta.FlgAtivo = false;
                meta.DtaAlteracao = DateTime.UtcNow;

                var metaAtualizada = await base.EditarAsync(meta);
                return metaAtualizada != null;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao excluir meta: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 5. GetFilterOptions - Return available filter options for the GetAll method
        /// </summary>
        public async Task<MetaFilterOptionsViewModel> GetFilterOptionsAsync()
        {
            try
            {
                var todasMetas = await BuscarTodosAsync();
                var categorias = await _categoriaService.BuscarAtivasAsync();

                var filterOptions = new MetaFilterOptionsViewModel
                {
                    StatusDisponiveis = ObterStatusDisponiveis(todasMetas),
                    CategoriasDisponiveis = await ObterCategoriasDisponiveis(categorias, todasMetas),
                    OpcoesOrdenacao = ObterOpcoesOrdenacao(),
                    FaixaValoresAlvo = ObterFaixaValores(todasMetas),
                    FaixaDatasCriacao = ObterFaixaDatasCriacao(todasMetas),
                    FaixaDatasVencimento = ObterFaixaDatasVencimento(todasMetas),
                    Estatisticas = ObterEstatisticasGerais(todasMetas)
                };

                return filterOptions;
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao obter opções de filtro: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 6. CompleteOrUpdate - Mark a goal as completed or update its progress/status
        /// </summary>
        public async Task<MetaViewModel?> CompleteOrUpdateAsync(string id, decimal? novoProgresso = null, bool marcarComoConcluida = false)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(id))
                    throw new ArgumentException("ID é obrigatório", nameof(id));

                var meta = await base.BuscarPorIdAsync(id);
                if (meta == null)
                    throw new ArgumentException("Meta não encontrada", nameof(id));

                // Atualizar progresso se fornecido
                if (novoProgresso.HasValue)
                {
                    if (novoProgresso.Value < 0)
                        throw new ArgumentException("Progresso não pode ser negativo", nameof(novoProgresso));

                    meta.ProgressoAtual = novoProgresso.Value;
                }

                // Marcar como concluída se solicitado ou se atingiu o objetivo
                if (marcarComoConcluida || meta.ProgressoAtual >= meta.ValorAlvo)
                {
                    meta.Status = MetaStatus.Concluida;
                    meta.DataConclusaoReal = DateTime.UtcNow;
                    meta.ProgressoAtual = meta.ValorAlvo; // Garantir que está no valor alvo
                }

                meta.DtaAlteracao = DateTime.UtcNow;
                return await base.EditarAsync(meta);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao atualizar meta: {ex.Message}", ex);
            }
        }

        #endregion

        #region Métodos Auxiliares

        private IQueryable<MetaViewModel> AplicarFiltros(IQueryable<MetaViewModel> query, MetaFilterViewModel filtros)
        {
            // Filtro por status
            if (filtros.Status.HasValue)
                query = query.Where(m => m.Status == filtros.Status.Value);

            // Filtro por período de criação
            if (filtros.DataInicio.HasValue)
                query = query.Where(m => m.DtaCadastro >= filtros.DataInicio.Value);
            if (filtros.DataFim.HasValue)
                query = query.Where(m => m.DtaCadastro <= filtros.DataFim.Value);

            // Filtro por período de vencimento/conclusão
            if (filtros.DataVencimentoInicio.HasValue)
                query = query.Where(m => m.DataConclusao >= filtros.DataVencimentoInicio.Value);
            //if (filtros.DataVencimentoFim.HasValue)
            //    query = query.Where(m => m.DataConclusao <= filtros.DataVencimentoFim.Value);

            // Filtro por categorias
            if (filtros.CategoriasIds?.Any() == true)
                query = query.Where(m => m.CategoriasAssociadas.Any(c => filtros.CategoriasIds.Contains(c)));

            // Filtro apenas ativas (metas com FlgAtivo = true)
            if (filtros.ApenasAtivas.HasValue && filtros.ApenasAtivas.Value)
                query = query.Where(m => m.FlgAtivo);

            // Filtro apenas abertas (metas ativas, não concluídas e não vencidas)
            if (filtros.ApenasAbertas.HasValue && filtros.ApenasAbertas.Value)
            {
                query = query.Where(m => m.FlgAtivo &&
                                        m.Status == MetaStatus.Ativa &&
                                        m.PercentualProgresso < 100 &&
                                        !m.IsVencida);
            }

            // Filtro apenas mensais
            if (filtros.ApenasMensais.HasValue && filtros.ApenasMensais.Value)
                query = query.Where(m => m.IsMetaMensal);

            // Filtro por valor alvo
            if (filtros.ValorAlvoMinimo.HasValue)
                query = query.Where(m => m.ValorAlvo >= filtros.ValorAlvoMinimo.Value);
            if (filtros.ValorAlvoMaximo.HasValue)
                query = query.Where(m => m.ValorAlvo <= filtros.ValorAlvoMaximo.Value);

            // Filtro por progresso
            if (filtros.ProgressoMinimo.HasValue)
                query = query.Where(m => m.PercentualProgresso >= filtros.ProgressoMinimo.Value);
            if (filtros.ProgressoMaximo.HasValue)
                query = query.Where(m => m.PercentualProgresso <= filtros.ProgressoMaximo.Value);

            // Filtro por texto
            if (!string.IsNullOrWhiteSpace(filtros.TextoBusca))
            {
                var texto = filtros.TextoBusca.ToLower();
                query = query.Where(m => m.Nome.ToLower().Contains(texto) ||
                                        m.Descricao.ToLower().Contains(texto));
            }

            return query;
        }

        private IQueryable<MetaViewModel> AplicarOrdenacao(IQueryable<MetaViewModel> query, string? ordenacao, bool decrescente)
        {
            if (string.IsNullOrWhiteSpace(ordenacao))
                ordenacao = "DataCriacao";

            return ordenacao.ToLower() switch
            {
                "nome" => decrescente ? query.OrderByDescending(m => m.Nome) : query.OrderBy(m => m.Nome),
                "valoralvo" => decrescente ? query.OrderByDescending(m => m.ValorAlvo) : query.OrderBy(m => m.ValorAlvo),
                "progresso" => decrescente ? query.OrderByDescending(m => m.PercentualProgresso) : query.OrderBy(m => m.PercentualProgresso),
                "dataconclusao" => decrescente ? query.OrderByDescending(m => m.DataConclusao) : query.OrderBy(m => m.DataConclusao),
                "status" => decrescente ? query.OrderByDescending(m => m.Status) : query.OrderBy(m => m.Status),
                _ => decrescente ? query.OrderByDescending(m => m.DtaCadastro) : query.OrderBy(m => m.DtaCadastro)
            };
        }

        private EstatisticasResultado CalcularEstatisticasResultado(IQueryable<MetaViewModel> metas)
        {
            var listaMetasArray = metas.ToArray();

            return new EstatisticasResultado
            {
                TotalEncontradas = listaMetasArray.Length,
                QuantidadePorStatus = listaMetasArray
                    .GroupBy(m => m.StatusDescricao)
                    .ToDictionary(g => g.Key, g => g.Count()),
                ValorTotalMetas = listaMetasArray.Sum(m => m.ValorAlvo),
                ValorTotalAlcancado = listaMetasArray.Sum(m => m.ProgressoAtual),
                ProgressoMedio = listaMetasArray.Any() ? listaMetasArray.Average(m => m.PercentualProgresso) : 0,
                QuantidadePorCategoria = listaMetasArray
                    .SelectMany(m => m.CategoriasAssociadas)
                    .GroupBy(c => c)
                    .ToDictionary(g => g.Key, g => g.Count())
            };
        }

        private List<MetaStatusOption> ObterStatusDisponiveis(IEnumerable<MetaViewModel> metas)
        {
            return Enum.GetValues<MetaStatus>()
                .Select(status => new MetaStatusOption
                {
                    Valor = status,
                    Descricao = status.GetDescription(),
                    Quantidade = metas.Count(m => m.Status == status)
                })
                .ToList();
        }

        private async Task<List<CategoriaOption>> ObterCategoriasDisponiveis(IEnumerable<CategoriaViewModel?> categorias, IEnumerable<MetaViewModel> metas)
        {
            return categorias
                .Where(c => c != null)
                .Select(c => new CategoriaOption
                {
                    Id = c!.Id!,
                    Nome = c.Nome,
                    QuantidadeMetas = metas.Count(m => m.CategoriasAssociadas.Contains(c.Id!))
                })
                .ToList();
        }

        private List<OrdenacaoOption> ObterOpcoesOrdenacao()
        {
            return new List<OrdenacaoOption>
            {
                new() { Valor = MetaOrdenacao.DataCriacao, Descricao = "Data de Criação", Campo = "DataCriacao" },
                new() { Valor = MetaOrdenacao.Nome, Descricao = "Nome", Campo = "Nome" },
                new() { Valor = MetaOrdenacao.ValorAlvo, Descricao = "Valor Alvo", Campo = "ValorAlvo" },
                new() { Valor = MetaOrdenacao.ProgressoAtual, Descricao = "Progresso", Campo = "ProgressoAtual" },
                new() { Valor = MetaOrdenacao.DataVencimento, Descricao = "Data de Conclusão", Campo = "DataConclusao" },
                new() { Valor = MetaOrdenacao.Status, Descricao = "Status", Campo = "Status" }
            };
        }

        private FaixaValores ObterFaixaValores(IEnumerable<MetaViewModel> metas)
        {
            var valores = metas.Select(m => m.ValorAlvo).ToArray();
            return new FaixaValores
            {
                Minimo = valores.Any() ? valores.Min() : 0,
                Maximo = valores.Any() ? valores.Max() : 0,
                Medio = valores.Any() ? valores.Average() : 0
            };
        }

        private FaixaDatas ObterFaixaDatasCriacao(IEnumerable<MetaViewModel> metas)
        {
            var datas = metas.Select(m => m.DtaCadastro).ToArray();
            return new FaixaDatas
            {
                MaisAntiga = datas.Any() ? datas.Min() : DateTime.Now,
                MaisRecente = datas.Any() ? datas.Max() : DateTime.Now
            };
        }

        private FaixaDatas ObterFaixaDatasVencimento(IEnumerable<MetaViewModel> metas)
        {
            var datas = metas.Select(m => m.DataConclusao).ToArray();
            return new FaixaDatas
            {
                MaisAntiga = datas.Any() ? datas.Min() : DateTime.Now,
                MaisRecente = datas.Any() ? datas.Max() : DateTime.Now
            };
        }

        private EstatisticasMetas ObterEstatisticasGerais(IEnumerable<MetaViewModel> metas)
        {
            var metasArray = metas.ToArray();
            return new EstatisticasMetas
            {
                TotalMetas = metasArray.Length,
                MetasAtivas = metasArray.Count(m => m.FlgAtivo),
                MetasConcluidas = metasArray.Count(m => m.Status == MetaStatus.Concluida),
                MetasVencidas = metasArray.Count(m => m.IsVencida),
                ValorTotalMetas = metasArray.Sum(m => m.ValorAlvo),
                ValorTotalAlcancado = metasArray.Sum(m => m.ProgressoAtual),
                ProgressoMedio = metasArray.Any() ? metasArray.Average(m => m.PercentualProgresso) : 0
            };
        }

        #endregion


    }
}
