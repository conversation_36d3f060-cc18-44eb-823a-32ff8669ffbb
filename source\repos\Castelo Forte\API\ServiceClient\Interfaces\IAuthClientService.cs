using Shared.ViewModels.Client;

namespace ServiceClient.Interfaces
{
    /// <summary>
    /// Interface para serviços de autenticação do sistema Client
    /// </summary>
    public interface IAuthClientService
    {
        /// <summary>
        /// Realiza a autenticação do usuário
        /// </summary>
        /// <param name="loginRequest">Dados de login (CPF/email e senha)</param>
        /// <returns>Resposta com token e dados do usuário</returns>
        Task<LoginResponseViewModel> AutenticarAsync(LoginRequestViewModel loginRequest);

        /// <summary>
        /// Valida um token JWT
        /// </summary>
        /// <param name="token">Token a ser validado</param>
        /// <returns>Resposta com dados de validação</returns>
        Task<ValidateTokenResponseViewModel> ValidarTokenAsync(string token);

        /// <summary>
        /// Renova um token JWT
        /// </summary>
        /// <param name="tokenAtual">Token atual a ser renovado</param>
        /// <returns>Novo token e data de expiração</returns>
        Task<RefreshTokenResponseViewModel> RenovarTokenAsync(string tokenAtual);

        /// <summary>
        /// Realiza o logout do usuário
        /// </summary>
        /// <param name="token">Token a ser invalidado</param>
        /// <returns>True se o logout foi bem-sucedido</returns>
        Task<bool> LogoutAsync(string? token = null);

        /// <summary>
        /// Verifica se o usuário possui connection string configurada
        /// </summary>
        /// <param name="usuarioId">ID do usuário</param>
        /// <returns>True se possui connection string</returns>
        Task<bool> UsuarioPossuiConnectionStringAsync(string usuarioId);

        /// <summary>
        /// Gera uma nova connection string para o usuário
        /// </summary>
        /// <param name="usuarioId">ID do usuário</param>
        /// <returns>True se a connection string foi gerada com sucesso</returns>
        Task<bool> GerarConnectionStringUsuarioAsync(string usuarioId);

        /// <summary>
        /// Atualiza o último acesso do usuário
        /// </summary>
        /// <param name="usuarioId">ID do usuário</param>
        /// <returns>True se a atualização foi bem-sucedida</returns>
        Task<bool> AtualizarUltimoAcessoAsync(string usuarioId);

        /// <summary>
        /// Verifica se o usuário está ativo
        /// </summary>
        /// <param name="usuarioId">ID do usuário</param>
        /// <returns>True se o usuário está ativo</returns>
        Task<bool> UsuarioAtivoAsync(string usuarioId);
    }
}
