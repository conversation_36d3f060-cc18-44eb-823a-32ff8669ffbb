﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace ServiceAdmin.Interfaces.Generic
{
    public interface IGenericAdminService<TViewModel, TEntity>
        where TViewModel : class
        where TEntity : class
    {
        #region CONSULTAS

        Task<IEnumerable<TViewModel?>> BuscarTodosAsync();
        Task<IEnumerable<TViewModel?>> BuscarPorFiltroAsync(Expression<Func<TEntity?, bool>> expression);
        Task<IEnumerable<TViewModel?>> BuscarPorFiltroPaginadoAsync(Expression<Func<TEntity?, bool>> expression, int page, int pageSize);
        Task<TViewModel?> BuscarPorIdAsync(string id);
        Task<TViewModel?> BuscarPrimeiroPorFiltroAsync(Expression<Func<TEntity?, bool>> expression);
        Task<int> BuscarContagemTotalPorFiltroAsync(Expression<Func<TEntity?, bool>> expression);
        Task<int> BuscarContagemTotalAsync();
        #endregion

        #region OPERAÇÕES CRUD

        Task<TViewModel?> AdicionarAsync(TViewModel viewModel);
        Task<TViewModel?> EditarAsync(TViewModel viewModel);
        Task ExcluirAsync(TViewModel viewModel);
        Task ExcluirPorIdAsync(string id);
        #endregion

        #region CONVERSÕES

        TViewModel ConverteEntidadeParaViewModel(TEntity entity);
        TEntity ConverteViewModelParaEntidade(TViewModel viewModel);
        #endregion
    }
}
