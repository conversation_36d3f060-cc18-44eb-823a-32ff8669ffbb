import 'package:flutter/material.dart';
import '../../models/meta_filter_model.dart';

class GoalsDateFilter extends StatefulWidget {
  final MetaFilterModel? currentFilter;
  final Function(MetaFilterModel?) onFilterChanged;

  const GoalsDateFilter({
    super.key,
    this.currentFilter,
    required this.onFilterChanged,
  });

  @override
  State<GoalsDateFilter> createState() => _GoalsDateFilterState();
}

class _GoalsDateFilterState extends State<GoalsDateFilter> {
  DateTime? _startDate;
  DateTime? _endDate;
  bool _showOnlyOpenGoals = true;
  String _selectedDateFilter = 'all'; // all, thisMonth, thisYear, custom

  @override
  void initState() {
    super.initState();
    _initializeFromCurrentFilter();
  }

  void _initializeFromCurrentFilter() {
    if (widget.currentFilter != null) {
      _startDate = widget.currentFilter!.dataVencimentoInicio;
      _endDate = widget.currentFilter!.dataVencimentoFim;
      _showOnlyOpenGoals = widget.currentFilter!.apenasAbertas ?? true;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF16213E),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.filter_list,
                color: Colors.white.withValues(alpha: 0.7),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Filtros de Data',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.9),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              if (_hasActiveFilters())
                TextButton(
                  onPressed: _clearFilters,
                  child: Text(
                    'Limpar',
                    style: TextStyle(
                      color: const Color(0xFF4ECDC4),
                      fontSize: 12,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),

          // Quick filter options
          _buildQuickFilters(),
          const SizedBox(height: 16),

          // Custom date range
          if (_selectedDateFilter == 'custom') ...[
            _buildCustomDateRange(),
            const SizedBox(height: 16),
          ],

          // Open goals toggle
          _buildOpenGoalsToggle(),
          const SizedBox(height: 16),

          // Apply button
          _buildApplyButton(),
        ],
      ),
    );
  }

  Widget _buildQuickFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Período',
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.7),
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: [
            _buildFilterChip('Todos', 'all'),
            _buildFilterChip('Este Mês', 'thisMonth'),
            _buildFilterChip('Este Ano', 'thisYear'),
            _buildFilterChip('Personalizado', 'custom'),
          ],
        ),
      ],
    );
  }

  Widget _buildFilterChip(String label, String value) {
    final isSelected = _selectedDateFilter == value;
    return FilterChip(
      label: Text(
        label,
        style: TextStyle(
          color: isSelected ? Colors.white : Colors.black,
          fontSize: 12,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedDateFilter = value;
          _updateDateRangeFromSelection();
        });
      },
      backgroundColor: isSelected ? const Color(0xFF4ECDC4) : Colors.white,
      selectedColor: const Color(0xFF4ECDC4),
      side: BorderSide(
        color: isSelected
            ? const Color(0xFF4ECDC4)
            : Colors.white.withValues(alpha: 0.5),
      ),
    );
  }

  Widget _buildCustomDateRange() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Período Personalizado',
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.7),
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildDateField(
                'Data Inicial',
                _startDate,
                (date) => setState(() => _startDate = date),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDateField(
                'Data Final',
                _endDate,
                (date) => setState(() => _endDate = date),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateField(
    String label,
    DateTime? date,
    Function(DateTime?) onChanged,
  ) {
    return GestureDetector(
      onTap: () => _selectDate(context, date, onChanged),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.5),
                fontSize: 10,
              ),
            ),
            const SizedBox(height: 2),
            Text(
              date != null
                  ? '${date.day}/${date.month}/${date.year}'
                  : 'Selecionar',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.9),
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOpenGoalsToggle() {
    return Row(
      children: [
        Icon(
          Icons.flag_outlined,
          color: Colors.white.withValues(alpha: 0.7),
          size: 20,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            'Apenas metas abertas (ativas)',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: 14,
            ),
          ),
        ),
        Switch(
          value: _showOnlyOpenGoals,
          onChanged: (value) {
            setState(() {
              _showOnlyOpenGoals = value;
            });
          },
          activeColor: const Color(0xFF4ECDC4),
          inactiveThumbColor: Colors.white.withValues(alpha: 0.5),
          inactiveTrackColor: Colors.white.withValues(alpha: 0.2),
        ),
      ],
    );
  }

  Widget _buildApplyButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _applyFilters,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF4ECDC4),
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        child: const Text(
          'Aplicar Filtros',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
        ),
      ),
    );
  }

  void _updateDateRangeFromSelection() {
    final now = DateTime.now();
    switch (_selectedDateFilter) {
      case 'all':
        _startDate = null;
        _endDate = null;
        break;
      case 'thisMonth':
        _startDate = DateTime(now.year, now.month, 1);
        _endDate = DateTime(now.year, now.month + 1, 0);
        break;
      case 'thisYear':
        _startDate = DateTime(now.year, 1, 1);
        _endDate = DateTime(now.year, 12, 31);
        break;
      case 'custom':
        // Keep current dates
        break;
    }
  }

  Future<void> _selectDate(
    BuildContext context,
    DateTime? currentDate,
    Function(DateTime?) onChanged,
  ) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: currentDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.dark(
              primary: Color(0xFF4ECDC4),
              surface: Color(0xFF16213E),
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null) {
      onChanged(picked);
    }
  }

  bool _hasActiveFilters() {
    return _selectedDateFilter != 'all' ||
        _startDate != null ||
        _endDate != null ||
        !_showOnlyOpenGoals;
  }

  void _clearFilters() {
    setState(() {
      _selectedDateFilter = 'all';
      _startDate = null;
      _endDate = null;
      _showOnlyOpenGoals = true;
    });
    widget.onFilterChanged(null);
  }

  void _applyFilters() {
    final filter = MetaFilterModel(
      dataVencimentoInicio: _startDate,
      dataVencimentoFim: _endDate,
      apenasAbertas: _showOnlyOpenGoals,
    );
    widget.onFilterChanged(filter);
  }
}
