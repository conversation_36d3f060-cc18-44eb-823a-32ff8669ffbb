using CasteloForte.Controllers.BaseComplemento;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceClient.Interfaces;
using Shared.ViewModels.Client;
using System.Text.Json;

namespace CasteloForte.Controllers
{
    /// <summary>
    /// Controller para operações CRUD com Cartão
    /// </summary>
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class CartaoController : ControllerBaseComplemento<CartaoController>
    {
        private readonly ICartaoService _cartaoService;
        private readonly string _controllerName = "CartaoController";

        public CartaoController(
            ICartaoService cartaoService,
            ILogger<CartaoController> logger,
            ILogErroClientService? logErroClientService = null,
            IHistoricoUsuarioClientService? historicoUsuarioClientService = null)
            : base(logger, logErroClientService, historicoUsuarioClientService)
        {
            _cartaoService = cartaoService ?? throw new ArgumentNullException(nameof(cartaoService));
        }

        #region GET - Consultas

        /// <summary>
        /// Busca todos os cartões ativos
        /// </summary>
        /// <returns>Lista de cartões ativos</returns>
        [HttpGet]
        public async Task<IActionResult> BuscarTodos()
        {
            string variaveis = "";
            try
            {
                string metodo = _controllerName + " BuscarTodos";
                await RegistraAcao(metodo, "Busca de todos os cartões ativos", "", variaveis);

                LogInfo("Iniciando busca de todos os cartões ativos", nameof(BuscarTodos), _controllerName);

                var cartoes = await _cartaoService.BuscarTodosAtivosAsync();

                LogInfo($"Busca concluída. {cartoes.Count()} cartões encontrados", nameof(BuscarTodos), _controllerName);
                return CriarRespostaSucesso(cartoes, "Cartões recuperados com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        /// <summary>
        /// Busca cartão por ID
        /// </summary>
        /// <param name="id">ID do cartão</param>
        /// <returns>Cartão encontrado</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> BuscarPorId(string id)
        {
            string variaveis = JsonSerializer.Serialize(new { id });
            try
            {
                string metodo = _controllerName + " BuscarPorId";
                await RegistraAcao(metodo, "Busca de cartão por ID", "", variaveis);

                if (string.IsNullOrWhiteSpace(id))
                    return CriarRespostaErro("ID é obrigatório", "INVALID_ID");

                LogInfo($"Iniciando busca do cartão com ID: {id}", nameof(BuscarPorId), _controllerName);

                var cartao = await _cartaoService.BuscarPorIdAsync(id);

                if (cartao == null)
                {
                    LogInfo($"Cartão com ID {id} não encontrado", nameof(BuscarPorId), _controllerName);
                    return CriarRespostaNaoEncontrado("Cartão não encontrado");
                }

                LogInfo($"Cartão com ID {id} encontrado", nameof(BuscarPorId), _controllerName);
                return CriarRespostaSucesso(cartao, "Cartão recuperado com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        /// <summary>
        /// Busca cartões ativos com filtro por apelido
        /// </summary>
        /// <param name="apelido">Apelido do cartão para filtrar</param>
        /// <returns>Lista de cartões filtrados</returns>
        [HttpGet("filtrar/apelido/{apelido}")]
        public async Task<IActionResult> BuscarPorApelido(string apelido)
        {
            string variaveis = JsonSerializer.Serialize(new { apelido });
            try
            {
                string metodo = _controllerName + " BuscarPorApelido";
                await RegistraAcao(metodo, "Busca de cartões por apelido", "", variaveis);

                if (string.IsNullOrWhiteSpace(apelido))
                    return CriarRespostaErro("Apelido é obrigatório", "INVALID_APELIDO");

                LogInfo($"Iniciando busca de cartões por apelido: {apelido}", nameof(BuscarPorApelido), _controllerName);
                
                var cartoes = await _cartaoService.BuscarPorFiltroAtivosAsync(c => c.ApelidoCartao.Contains(apelido));
                
                LogInfo($"Busca por apelido concluída. {cartoes.Count()} cartões encontrados", nameof(BuscarPorApelido), _controllerName);
                return CriarRespostaSucesso(cartoes, "Cartões filtrados recuperados com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        /// <summary>
        /// Busca cartões ativos com filtro por nome
        /// </summary>
        /// <param name="nome">Nome do cartão para filtrar</param>
        /// <returns>Lista de cartões filtrados</returns>
        [HttpGet("filtrar/nome/{nome}")]
        public async Task<IActionResult> BuscarPorNome(string nome)
        {
            string variaveis = JsonSerializer.Serialize(new { nome });
            try
            {
                string metodo = _controllerName + " BuscarPorNome";
                await RegistraAcao(metodo, "Busca de cartões por nome", "", variaveis);

                if (string.IsNullOrWhiteSpace(nome))
                    return CriarRespostaErro("Nome é obrigatório", "INVALID_NOME");

                LogInfo($"Iniciando busca de cartões por nome: {nome}", nameof(BuscarPorNome), _controllerName);
                
                var cartoes = await _cartaoService.BuscarPorFiltroAtivosAsync(c => c.NomeCartao.Contains(nome));
                
                LogInfo($"Busca por nome concluída. {cartoes.Count()} cartões encontrados", nameof(BuscarPorNome), _controllerName);
                return CriarRespostaSucesso(cartoes, "Cartões filtrados recuperados com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        /// <summary>
        /// Busca cartões ativos com paginação
        /// </summary>
        /// <param name="page">Número da página (padrão: 1)</param>
        /// <param name="pageSize">Tamanho da página (padrão: 10)</param>
        /// <returns>Lista paginada de cartões</returns>
        [HttpGet("paginado")]
        public async Task<IActionResult> BuscarPaginado([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
        {
            string variaveis = JsonSerializer.Serialize(new { page, pageSize });
            try
            {
                string metodo = _controllerName + " BuscarPaginado";
                await RegistraAcao(metodo, "Busca paginada de cartões", "", variaveis);
                if (page <= 0)
                    return CriarRespostaErro("Número da página deve ser maior que zero", "INVALID_PAGE");

                if (pageSize <= 0 || pageSize > 100)
                    return CriarRespostaErro("Tamanho da página deve ser entre 1 e 100", "INVALID_PAGE_SIZE");

                LogInfo($"Iniciando busca paginada - Página: {page}, Tamanho: {pageSize}", nameof(BuscarPaginado), _controllerName);
                
                var cartoes = await _cartaoService.BuscarPorFiltroAtivosPaginadoAsync(c => true, page, pageSize);
                var total = await _cartaoService.BuscarContagemTotalAtivosAsync();
                
                var resultado = new
                {
                    cartoes = cartoes,
                    paginacao = new
                    {
                        paginaAtual = page,
                        tamanhoPagina = pageSize,
                        totalRegistros = total,
                        totalPaginas = (int)Math.Ceiling((double)total / pageSize)
                    }
                };

                LogInfo($"Busca paginada concluída. {cartoes.Count()} cartões na página {page}", nameof(BuscarPaginado), _controllerName);
                return CriarRespostaSucesso(resultado, "Cartões paginados recuperados com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        /// <summary>
        /// Busca contagem total de cartões ativos
        /// </summary>
        /// <returns>Número total de cartões ativos</returns>
        [HttpGet("contagem")]
        public async Task<IActionResult> BuscarContagem()
        {
            string variaveis = "";
            try
            {
                string metodo = _controllerName + " BuscarContagem";
                await RegistraAcao(metodo, "Busca de contagem total de cartões ativos", "", variaveis);

                LogInfo("Iniciando busca de contagem de cartões ativos", nameof(BuscarContagem), _controllerName);
                
                var total = await _cartaoService.BuscarContagemTotalAtivosAsync();
                
                LogInfo($"Contagem concluída. Total: {total} cartões ativos", nameof(BuscarContagem), _controllerName);
                return CriarRespostaSucesso(new { total }, "Contagem recuperada com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        #endregion

        #region POST - Criação

        /// <summary>
        /// Cria um novo cartão
        /// </summary>
        /// <param name="cartaoViewModel">Dados do cartão a ser criado</param>
        /// <returns>Cartão criado</returns>
        [HttpPost]
        public async Task<IActionResult> Criar([FromBody] CartaoViewModel cartaoViewModel)
        {
            string variaveis = JsonSerializer.Serialize(cartaoViewModel);
            try
            {
                if (cartaoViewModel == null)
                    return CriarRespostaErro("Dados do cartão são obrigatórios", "INVALID_DATA");

                if (!ModelState.IsValid)
                    return CriarRespostaErro("Dados inválidos", "VALIDATION_ERROR");

                LogInfo("Iniciando criação de novo cartão", nameof(Criar), _controllerName);
                
                var cartaoCriado = await _cartaoService.AdicionarAsync(cartaoViewModel);
                
                if (cartaoCriado == null)
                    return CriarRespostaErro("Falha ao criar cartão", "CREATE_FAILED");

                LogInfo($"Cartão criado com sucesso. ID: {cartaoCriado.Id}", nameof(Criar), _controllerName);
                return CreatedAtAction(nameof(BuscarPorId), new { id = cartaoCriado.Id }, 
                    CriarRespostaSucesso(cartaoCriado, "Cartão criado com sucesso").Value);
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        #endregion

        #region PUT - Atualização

        /// <summary>
        /// Atualiza um cartão existente
        /// </summary>
        /// <param name="id">ID do cartão a ser atualizado</param>
        /// <param name="cartaoViewModel">Dados atualizados do cartão</param>
        /// <returns>Cartão atualizado</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> Atualizar(string id, [FromBody] CartaoViewModel cartaoViewModel)
        {
            string variaveis = JsonSerializer.Serialize(cartaoViewModel);
            try
            {
                if (string.IsNullOrWhiteSpace(id))
                    return CriarRespostaErro("ID é obrigatório", "INVALID_ID");

                if (cartaoViewModel == null)
                    return CriarRespostaErro("Dados do cartão são obrigatórios", "INVALID_DATA");

                if (!ModelState.IsValid)
                    return CriarRespostaErro("Dados inválidos", "VALIDATION_ERROR");

                if (cartaoViewModel.Id != id)
                    return CriarRespostaErro("ID do cartão não confere com o ID da URL", "ID_MISMATCH");

                LogInfo($"Iniciando atualização do cartão com ID: {id}", nameof(Atualizar), _controllerName);

                // Verifica se o cartão existe
                var cartaoExistente = await _cartaoService.BuscarPorIdAsync(id);
                if (cartaoExistente == null)
                {
                    LogInfo($"Cartão com ID {id} não encontrado para atualização", nameof(Atualizar), _controllerName);
                    return CriarRespostaNaoEncontrado("Cartão não encontrado");
                }

                var cartaoAtualizado = await _cartaoService.EditarAsync(cartaoViewModel);

                if (cartaoAtualizado == null)
                    return CriarRespostaErro("Falha ao atualizar cartão", "UPDATE_FAILED");

                LogInfo($"Cartão com ID {id} atualizado com sucesso", nameof(Atualizar), _controllerName);
                return CriarRespostaSucesso(cartaoAtualizado, "Cartão atualizado com sucesso");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        #endregion

        #region DELETE - Exclusão (Soft Delete)

        /// <summary>
        /// Inativa um cartão (soft delete)
        /// </summary>
        /// <param name="id">ID do cartão a ser inativado</param>
        /// <returns>Confirmação da inativação</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> Inativar(string id)
        {
            string variaveis = JsonSerializer.Serialize(new { id });
            try
            {
                string metodo = _controllerName + " Inativar";
                await RegistraAcao(metodo, "Inativação de cartão", "", variaveis);

                if (string.IsNullOrWhiteSpace(id))
                    return CriarRespostaErro("ID é obrigatório", "INVALID_ID");

                LogInfo($"Iniciando inativação do cartão com ID: {id}", nameof(Inativar), _controllerName);

                // Verifica se o cartão existe
                var cartaoExistente = await _cartaoService.BuscarPorIdAsync(id);
                if (cartaoExistente == null)
                {
                    LogInfo($"Cartão com ID {id} não encontrado para inativação", nameof(Inativar), _controllerName);
                    return CriarRespostaNaoEncontrado("Cartão não encontrado");
                }

                var sucesso = await _cartaoService.InativarAsync(id);

                if (!sucesso)
                    return CriarRespostaErro("Falha ao inativar cartão", "DELETE_FAILED");

                LogInfo($"Cartão com ID {id} inativado com sucesso", nameof(Inativar), _controllerName);
                return CriarRespostaSucesso(null, "Cartão inativado com sucesso");
            }
            catch (InvalidOperationException ex)
            {
                return CriarRespostaErro(ex.Message, "ALREADY_INACTIVE");
            }
            catch (KeyNotFoundException ex)
            {
                return CriarRespostaNaoEncontrado(ex.Message);
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        /// <summary>
        /// Reativa um cartão inativo
        /// </summary>
        /// <param name="id">ID do cartão a ser reativado</param>
        /// <returns>Confirmação da reativação</returns>
        [HttpPatch("{id}/reativar")]
        public async Task<IActionResult> Reativar(string id)
        {
            string variaveis = JsonSerializer.Serialize(new { id });
            try
            {
                string metodo = _controllerName + " Reativar";
                await RegistraAcao(metodo, "Reativação de cartão", "", variaveis);

                if (string.IsNullOrWhiteSpace(id))
                    return CriarRespostaErro("ID é obrigatório", "INVALID_ID");

                LogInfo($"Iniciando reativação do cartão com ID: {id}", nameof(Reativar), _controllerName);

                var sucesso = await _cartaoService.ReativarAsync(id);

                if (!sucesso)
                    return CriarRespostaErro("Falha ao reativar cartão", "REACTIVATE_FAILED");

                LogInfo($"Cartão com ID {id} reativado com sucesso", nameof(Reativar), _controllerName);
                return CriarRespostaSucesso(null, "Cartão reativado com sucesso");
            }
            catch (InvalidOperationException ex)
            {
                return CriarRespostaErro(ex.Message, "ALREADY_ACTIVE");
            }
            catch (KeyNotFoundException ex)
            {
                return CriarRespostaNaoEncontrado(ex.Message);
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controllerName, variaveis);
            }
        }

        #endregion
    }
}
