﻿using CasteloForteAdmin.Controllers.ControllerBaseComplemento;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceAdmin.Interfaces;
using Shared.ViewModels.Admin;
using System.Text.Json;

namespace CasteloForteAdmin.Controllers
{
    /// <summary>
    /// Controller para operações públicas de usuários (cadastro, etc.)
    /// Separado das operações administrativas
    /// </summary>
    [AllowAnonymous]
    [Route("api/[controller]")]
    [ApiController]
    public class UsuarioPublicoController(
        IUsuarioService service,
        ILogErroAdminService logErroAdminService,
        IHistoricoUsuarioService historicoUsuarioService,
        ILogger<UsuarioPublicoController> logger
        ) : ControllerBaseComplemento<UsuarioPublicoController>(logErroAdminService, historicoUsuarioService, logger)
    {
        private readonly string _controller = "UsuarioPublicoController";
        private readonly IUsuarioService _service = service;

        #region Cadastro Público de Usuários

        /// <summary>
        /// Cadastro público de novos usuários no sistema
        /// </summary>
        /// <param name="model">Dados do usuário a ser cadastrado</param>
        /// <returns>Dados do usuário criado</returns>
        [HttpPost("cadastrar")]
        public async Task<IActionResult> CadastrarUsuario([FromBody] CadastroUsuarioViewModel model)
        {
            string variaveis = JsonSerializer.Serialize(model);
            try
            {
                string metodo = _controller + " CadastrarUsuario";
                await RegistraAcao(metodo, "Cadastro público de um novo usuário no sistema", "", variaveis);

                var usuarioCriado = await _service.CadastroUsuarioNovoComRetorno(model);

                if (usuarioCriado != null)
                {
                    return Ok(new {
                        success = true,
                        message = "Usuário cadastrado com sucesso",
                        data = new {
                            id = usuarioCriado.Id,
                            nome = usuarioCriado.Nome,
                            email = usuarioCriado.Email,
                            cpf = usuarioCriado.Cpf,
                            celular = usuarioCriado.Celular,
                            dtaCadastro = usuarioCriado.DtaCadastro,
                            flgAtivo = usuarioCriado.FlgAtivo
                        }
                    });
                }
                else
                {
                    throw new Exception("Falha ao cadastrar usuário");
                }
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controller, variaveis);
            }
        }

        /// <summary>
        /// Método legado para compatibilidade com sistemas existentes
        /// NOTA: Use o endpoint /cadastrar para novos desenvolvimentos
        /// </summary>
        [HttpPost("legacy")]
        [Obsolete("Use o endpoint /cadastrar para novos desenvolvimentos")]
        public async Task<IActionResult> CadastroUsuarioLegacy([FromBody] UsuarioViewModel model)
        {
            string variaveis = JsonSerializer.Serialize(model);
            try
            {
                string metodo = _controller + " CadastroUsuarioLegacy";
                await RegistraAcao(metodo, "Cadastro de usuário (método legado)", "", variaveis);

                if (await _service.CadastroUsuario(model))
                    return Ok();
                else
                    throw new Exception("Falha ao cadastrar usuário");
            }
            catch (Exception ex)
            {
                return await LogErro(ex, System.Reflection.MethodBase.GetCurrentMethod()!.Name, _controller, variaveis);
            }
        }
        #endregion

    }
}
