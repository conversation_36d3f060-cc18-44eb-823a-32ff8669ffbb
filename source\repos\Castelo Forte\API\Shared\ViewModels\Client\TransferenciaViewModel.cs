﻿using Shared.Enums;
using Shared.ViewModels.Base;
using System;
using System.ComponentModel.DataAnnotations;

namespace Shared.ViewModels.Client
{
    /// <summary>
    /// ViewModel para transferência/transação financeira
    /// </summary>
    public class TransferenciaViewModel : BaseViewModel
    {
        /// <summary>
        /// Descrição da transferência
        /// </summary>
        [Required]
        [StringLength(500)]
        public string Descricao { get; set; } = "";

        /// <summary>
        /// Valor da transferência
        /// </summary>
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "O valor deve ser maior que zero")]
        public decimal Valor { get; set; } = 0;

        /// <summary>
        /// Data da transferência
        /// </summary>
        [Required]
        public DateTime DataTransferencia { get; set; } = DateTime.Now;

        /// <summary>
        /// Data de processamento
        /// </summary>
        public DateTime? DataProcessamento { get; set; }

        #region Contas
        /// <summary>
        /// ID da conta de origem
        /// </summary>
        [Required]
        public string IdContaOrigem { get; set; } = "";

        /// <summary>
        /// Nome da conta de origem (para exibição)
        /// </summary>
        public string? NomeContaOrigem { get; set; }

        /// <summary>
        /// ID da conta de destino
        /// </summary>
        public string? IdContaDestino { get; set; }

        /// <summary>
        /// Nome da conta de destino (para exibição)
        /// </summary>
        public string? NomeContaDestino { get; set; }
        #endregion

        #region Categoria
        /// <summary>
        /// ID da categoria
        /// </summary>
        [Required]
        public string IdCategoria { get; set; } = "";

        /// <summary>
        /// Nome da categoria (para exibição)
        /// </summary>
        public string? NomeCategoria { get; set; }

        /// <summary>
        /// Cor da categoria (para exibição)
        /// </summary>
        public string? CorCategoria { get; set; }

        /// <summary>
        /// Ícone da categoria (para exibição)
        /// </summary>
        public string? IconeCategoria { get; set; }
        #endregion

        #region Status e Controle
        /// <summary>
        /// Status da transferência
        /// </summary>
        public TransferenciaStatus Status { get; set; } = TransferenciaStatus.Concluida;

        /// <summary>
        /// Descrição do status
        /// </summary>
        public string StatusDescricao => Status.GetDescription();

        /// <summary>
        /// Origem da transação
        /// </summary>
        public OrigemTransacao Origem { get; set; } = OrigemTransacao.Manual;

        /// <summary>
        /// Descrição da origem
        /// </summary>
        public string OrigemDescricao => Origem.GetDescription();

        /// <summary>
        /// Tipo de transação
        /// </summary>
        [Required]
        public string Tipo { get; set; } = "DESPESA";
        #endregion

        #region Metadados
        /// <summary>
        /// ID de referência externa
        /// </summary>
        public string? IdReferenciaExterna { get; set; }

        /// <summary>
        /// ID do registro Open Finance
        /// </summary>
        public string? IdRegistroOpenFinance { get; set; }

        /// <summary>
        /// Observações
        /// </summary>
        public string? Observacoes { get; set; }
        #endregion

        #region Pagamento
        /// <summary>
        /// Forma de pagamento
        /// </summary>
        public string FormaPagamento { get; set; } = "";

        /// <summary>
        /// Indica se é pagamento no crédito
        /// </summary>
        public bool FlgPagamentoCredito { get; set; } = false;
        #endregion

        #region Recorrência
        /// <summary>
        /// ID da transferência recorrente
        /// </summary>
        public string? IdTransferenciaRecorrente { get; set; }

        /// <summary>
        /// Nome da transferência recorrente (para exibição)
        /// </summary>
        public string? NomeTransferenciaRecorrente { get; set; }
        #endregion

        #region Propriedades Calculadas
        /// <summary>
        /// Verifica se é uma receita
        /// </summary>
        public bool IsReceita => Tipo.ToUpper() == "RECEITA";

        /// <summary>
        /// Verifica se é uma despesa
        /// </summary>
        public bool IsDespesa => Tipo.ToUpper() == "DESPESA";

        /// <summary>
        /// Verifica se é uma transferência entre contas
        /// </summary>
        public bool IsTransferencia => !string.IsNullOrEmpty(IdContaDestino);

        /// <summary>
        /// Verifica se foi processada com sucesso
        /// </summary>
        public bool IsProcessada => Status == TransferenciaStatus.Concluida;

        /// <summary>
        /// Verifica se pode ser editada
        /// </summary>
        public bool PodeEditar => Status == TransferenciaStatus.Pendente ||
                                  (Status == TransferenciaStatus.Concluida && Origem == OrigemTransacao.Manual);

        /// <summary>
        /// Verifica se pode ser cancelada
        /// </summary>
        public bool PodeCancelar => Status.PodeCancelar();

        /// <summary>
        /// Valor formatado para exibição
        /// </summary>
        public string ValorFormatado => $"R$ {Valor:N2}";
        #endregion

        // Propriedades de compatibilidade (DEPRECATED)
        [Obsolete("Use IsTransferencia")]
        public bool FlgTransferencia
        {
            get => IsTransferencia;
            set { /* Ignorado */ }
        }

        [Obsolete("Use IdTransferenciaRecorrente")]
        public bool FlgLancamentoFixo { get; set; } = false;

        [Obsolete("Use TransferenciaRecorrente")]
        public int? QtdMesesEntreLancamentos { get; set; } = 0;

        [Obsolete("Use TransferenciaRecorrente")]
        public int? DiaMesLancamento { get; set; } = 0;
    }
}
