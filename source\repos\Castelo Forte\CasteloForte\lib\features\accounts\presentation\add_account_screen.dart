import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/utils/navigation_helper.dart';
import '../data/accounts_service.dart';
import '../data/models/account_model.dart';
import '../data/constants/account_constants.dart';
// import 'widgets/icon_selector.dart'; // REMOVIDO - não utilizado
// import 'widgets/color_selector.dart'; // REMOVIDO - não utilizado

class AddAccountScreen extends StatefulWidget {
  const AddAccountScreen({super.key});

  @override
  State<AddAccountScreen> createState() => _AddAccountScreenState();
}

class _AddAccountScreenState extends State<AddAccountScreen> {
  final _formKey = GlobalKey<FormState>();
  final _bankNameController = TextEditingController();
  final _nicknameController = TextEditingController();
  final _initialBalanceController = TextEditingController();

  // Novos controladores
  final _agenciaController = TextEditingController();
  final _numeroContaController = TextEditingController();
  final _numeroBancoController = TextEditingController();

  String _selectedAccountType = 'Conta Corrente';
  List<String> _accountTypes = [];

  // Novos campos - valores padrão (seletores ocultos)
  final String _selectedIcon = 'account_balance';
  final String _selectedColor = 'blue';
  bool _isContaPj = false;

  @override
  void initState() {
    super.initState();
    _loadAccountTypes();
  }

  void _loadAccountTypes() {
    _accountTypes = AccountsService.getAccountTypes();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E), // Mesmo fundo das categorias
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => NavigationHelper.safeGoBack(context),
        ),
        title: const Text(
          'Nova Conta',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header informativo
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: const Color(0xFF16213E), // Mesmo fundo das categorias
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: const Color(0xFF4ECDC4).withValues(alpha: 0.3),
                  ),
                ),
                child: const Row(
                  children: [
                    Icon(
                      Icons.account_balance,
                      color: Color(0xFF4ECDC4),
                      size: 24,
                    ),
                    SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Adicione uma nova conta para começar a organizar suas finanças',
                        style: TextStyle(color: Colors.white70),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 30),

              // Nome do banco
              _buildTextField(
                controller: _bankNameController,
                label: 'Nome do Banco/Instituição',
                hint: 'Ex: Nubank, Itaú, Bradesco...',
                icon: Icons.business,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Nome do banco é obrigatório';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),

              // Tipo de conta
              _buildDropdownField(),
              const SizedBox(height: 20),

              // Apelido (opcional)
              _buildTextField(
                controller: _nicknameController,
                label: 'Apelido (opcional)',
                hint: 'Ex: Conta Principal, Conta Salário...',
                icon: Icons.label,
              ),
              const SizedBox(height: 20),

              // Saldo inicial
              _buildTextField(
                controller: _initialBalanceController,
                label: 'Saldo Inicial',
                hint: '0,00',
                icon: Icons.attach_money,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9.,]')),
                ],
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    final amount = double.tryParse(value.replaceAll(',', '.'));
                    if (amount == null) {
                      return 'Valor inválido';
                    }
                  }
                  return null;
                },
              ),
              const SizedBox(height: 30),

              // Seletor de ícone - OCULTO
              // IconSelector(
              //   selectedIcon: _selectedIcon,
              //   onIconSelected: (icon) {
              //     setState(() {
              //       _selectedIcon = icon;
              //     });
              //   },
              // ),
              // const SizedBox(height: 20),

              // Seletor de cor - OCULTO
              // ColorSelector(
              //   selectedColor: _selectedColor,
              //   onColorSelected: (color) {
              //     setState(() {
              //       _selectedColor = color;
              //     });
              //   },
              // ),
              const SizedBox(height: 20),

              // Agência
              _buildTextField(
                controller: _agenciaController,
                label: 'Agência',
                hint: 'Ex: 1234',
                icon: Icons.business,
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              ),
              const SizedBox(height: 20),

              // Número da conta
              _buildTextField(
                controller: _numeroContaController,
                label: 'Número da Conta',
                hint: 'Ex: 12345-6',
                icon: Icons.account_balance,
              ),
              const SizedBox(height: 20),

              // Número do banco
              _buildTextField(
                controller: _numeroBancoController,
                label: 'Número do Banco',
                hint: 'Ex: 001',
                icon: Icons.account_balance_wallet,
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              ),
              const SizedBox(height: 20),

              // Checkbox Conta PJ
              _buildContaPjCheckbox(),
              const SizedBox(height: 30),

              // Preview da conta
              _buildAccountPreview(),
              const SizedBox(height: 30),

              // Botões de ação
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => NavigationHelper.safeGoBack(context),
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: Colors.white54),
                        padding: const EdgeInsets.symmetric(vertical: 15),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'Cancelar',
                        style: TextStyle(color: Colors.white54),
                      ),
                    ),
                  ),
                  const SizedBox(width: 15),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _saveAccount,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF4ECDC4),
                        padding: const EdgeInsets.symmetric(vertical: 15),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'Salvar Conta',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          validator: validator,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(color: Colors.white54),
            prefixIcon: Icon(icon, color: Colors.white54),
            filled: true,
            fillColor: const Color(0xFF16213E), // Mesmo fundo das categorias
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFF4ECDC4)),
            ),
          ),
          onChanged: (value) => setState(() {}),
        ),
      ],
    );
  }

  Widget _buildContaPjCheckbox() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF16213E),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: const Color(0xFF4ECDC4).withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.business_center, color: const Color(0xFF4ECDC4), size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Conta Pessoa Jurídica',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Text(
                  'Marque se esta é uma conta empresarial',
                  style: TextStyle(color: Colors.white70, fontSize: 14),
                ),
              ],
            ),
          ),
          Switch(
            value: _isContaPj,
            onChanged: (value) {
              setState(() {
                _isContaPj = value;
              });
            },
            activeColor: const Color(0xFF4ECDC4),
            activeTrackColor: const Color(0xFF4ECDC4).withValues(alpha: 0.3),
            inactiveThumbColor: Colors.white54,
            inactiveTrackColor: Colors.white24,
          ),
        ],
      ),
    );
  }

  Widget _buildDropdownField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Tipo de Conta',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _selectedAccountType,
          style: const TextStyle(color: Colors.white),
          decoration: const InputDecoration(
            prefixIcon: Icon(Icons.category, color: Colors.white54),
            filled: true,
            fillColor: Color(0xFF16213E), // Mesmo fundo das categorias
            border: OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(12)),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(12)),
              borderSide: BorderSide(color: Color(0xFF4ECDC4)),
            ),
          ),
          dropdownColor: const Color(0xFF16213E),
          items: _accountTypes.map((String type) {
            return DropdownMenuItem<String>(
              value: type,
              child: Text(type, style: const TextStyle(color: Colors.white)),
            );
          }).toList(),
          onChanged: (String? newValue) {
            if (newValue != null) {
              setState(() {
                _selectedAccountType = newValue;
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildAccountPreview() {
    final bankName = _bankNameController.text.isEmpty
        ? 'Nome do Banco'
        : _bankNameController.text;
    final nickname = _nicknameController.text;
    final balance =
        double.tryParse(_initialBalanceController.text.replaceAll(',', '.')) ??
        0.0;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color:
            AccountConstants.getColorByName(
              _selectedColor,
            )?.withValues(alpha: 0.1) ??
            const Color(0xFF16213E),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color:
              AccountConstants.getColorByName(
                _selectedColor,
              )?.withValues(alpha: 0.5) ??
              const Color(0xFF4ECDC4).withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AccountConstants.getColorByName(_selectedColor),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      AccountConstants.getIconByName(_selectedIcon),
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        nickname.isNotEmpty ? nickname : bankName,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        _selectedAccountType,
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFF4ECDC4),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'Ativa',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          const Text(
            'Saldo Inicial',
            style: TextStyle(color: Colors.white70, fontSize: 14),
          ),
          Text(
            'R\$ ${balance.toStringAsFixed(2)}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 28,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  void _saveAccount() async {
    if (_formKey.currentState!.validate()) {
      try {
        // Debug: Print form values before creating AccountFormModel
        print('=== DEBUG: Form Values ===');
        print('Nome: "${_bankNameController.text}"');
        print('Apelido: "${_nicknameController.text}"');
        print('Tipo Conta: "$_selectedAccountType"');
        print('Saldo: "${_initialBalanceController.text}"');
        print('Agencia: "${_agenciaController.text}"');
        print('Numero Conta: "${_numeroContaController.text}"');
        print('Numero Banco: "${_numeroBancoController.text}"');
        print('Icone: "$_selectedIcon"');
        print('Cor: "$_selectedColor"');
        print('Conta PJ: $_isContaPj');

        final accountForm = AccountFormModel(
          nome: _bankNameController.text,
          tipoConta: _selectedAccountType,
          apelidoConta: _nicknameController.text,
          saldo:
              double.tryParse(
                _initialBalanceController.text.replaceAll(',', '.'),
              ) ??
              0.0,
          // Novos campos
          icone: _selectedIcon,
          cor: _selectedColor,
          agencia: _agenciaController.text.isNotEmpty
              ? _agenciaController.text
              : null,
          numeroConta: _numeroContaController.text.isNotEmpty
              ? _numeroContaController.text
              : null,
          numeroBanco: _numeroBancoController.text.isNotEmpty
              ? _numeroBancoController.text
              : null,
          contaPj: _isContaPj,
        );

        // Debug: Print AccountFormModel values
        print('=== DEBUG: AccountFormModel ===');
        print('Nome: "${accountForm.nome}"');
        print('Apelido: "${accountForm.apelidoConta}"');
        print('Tipo Conta: "${accountForm.tipoConta}"');
        print('Saldo: ${accountForm.saldo}');

        // Debug: Print JSON that will be sent
        final jsonData = accountForm.toJson();
        print('=== DEBUG: JSON to be sent ===');
        print(jsonData);

        final success = await AccountsService.createAccount(accountForm);

        if (mounted) {
          if (success) {
            Navigator.pop(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Conta adicionada com sucesso!'),
                backgroundColor: Color(0xFF4ECDC4),
              ),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Erro ao adicionar conta'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Erro ao adicionar conta: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
