import 'package:flutter/material.dart';

/// Constantes de tema para telas de listagem
/// Baseado no design system da AccountsListScreen
class ListingTheme {
  
  // ==================== CORES ====================
  
  /// Cores de fundo
  static const Color backgroundPrimary = Color(0xFF1A1A2E);
  static const Color backgroundSecondary = Color(0xFF16213E);
  
  /// Cor de destaque principal
  static const Color accent = Color(0xFF4ECDC4);
  
  /// Cores de texto
  static const Color textPrimary = Colors.white;
  static const Color textSecondary = Colors.white70;
  static const Color textTertiary = Colors.white54;
  static const Color textDisabled = Colors.white30;
  
  /// Cores de status
  static const Color success = Colors.green;
  static const Color error = Colors.red;
  static const Color warning = Colors.orange;
  static const Color inactive = Colors.grey;
  
  /// Cores por contexto
  static const Color accounts = Color(0xFF4ECDC4);
  static const Color categories = Color(0xFF4ECDC4);
  static const Color transactions = Color(0xFF9B59B6);
  static const Color reports = Color(0xFFE67E22);
  
  // ==================== DIMENSÕES ====================
  
  /// Border radius padrão
  static const double borderRadius = 15.0;
  
  /// Espaçamentos
  static const double spacingXS = 4.0;
  static const double spacingS = 8.0;
  static const double spacingM = 12.0;
  static const double spacingL = 16.0;
  static const double spacingXL = 20.0;
  static const double spacingXXL = 30.0;
  
  /// Margens padrão
  static const EdgeInsets marginAll = EdgeInsets.all(spacingXL);
  static const EdgeInsets marginHorizontal = EdgeInsets.symmetric(horizontal: spacingXL);
  static const EdgeInsets marginVertical = EdgeInsets.symmetric(vertical: spacingXL);
  
  /// Paddings padrão
  static const EdgeInsets paddingAll = EdgeInsets.all(spacingL);
  static const EdgeInsets paddingCard = EdgeInsets.all(spacingL);
  static const EdgeInsets paddingInput = EdgeInsets.symmetric(
    horizontal: spacingXL,
    vertical: 15,
  );
  
  /// Tamanhos de ícones
  static const double iconSmall = 16.0;
  static const double iconMedium = 24.0;
  static const double iconLarge = 80.0;
  
  /// Tamanhos de avatar
  static const double avatarSmall = 40.0;
  static const double avatarMedium = 50.0;
  static const double avatarLarge = 60.0;
  
  /// Elevações
  static const double elevationLow = 1.0;
  static const double elevationMedium = 2.0;
  static const double elevationHigh = 4.0;
  
  // ==================== TIPOGRAFIA ====================
  
  /// Títulos
  static const TextStyle titleLarge = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w500,
    color: textPrimary,
  );
  
  /// Títulos de cards
  static const TextStyle cardTitle = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: textPrimary,
  );
  
  /// Texto secundário
  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: textSecondary,
  );
  
  /// Labels pequenos
  static const TextStyle labelSmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );
  
  /// Texto muito pequeno
  static const TextStyle captionSmall = TextStyle(
    fontSize: 10,
    fontWeight: FontWeight.bold,
    color: textPrimary,
  );
  
  /// Hint text
  static const TextStyle hintText = TextStyle(
    color: textSecondary,
  );
  
  // ==================== DECORAÇÕES ====================
  
  /// Container de input
  static BoxDecoration inputDecoration = BoxDecoration(
    color: backgroundSecondary,
    borderRadius: BorderRadius.circular(borderRadius),
    border: Border.all(
      color: accent.withValues(alpha: 0.3),
    ),
  );
  
  /// Container de botão de ação
  static BoxDecoration actionButtonDecoration = BoxDecoration(
    color: accent,
    borderRadius: BorderRadius.circular(borderRadius),
  );
  
  /// Card ativo
  static BoxDecoration activeCardDecoration(Color accentColor) => BoxDecoration(
    color: backgroundSecondary,
    borderRadius: BorderRadius.circular(borderRadius),
    border: Border.all(
      color: accentColor.withValues(alpha: 0.3),
      width: 1,
    ),
  );
  
  /// Card inativo
  static BoxDecoration inactiveCardDecoration(Color accentColor) => BoxDecoration(
    color: backgroundSecondary.withValues(alpha: 0.7),
    borderRadius: BorderRadius.circular(borderRadius),
    border: Border.all(
      color: accentColor.withValues(alpha: 0.2),
      width: 1,
    ),
  );
  
  /// Avatar circular
  static BoxDecoration avatarDecoration(Color color, double size) => BoxDecoration(
    color: color.withValues(alpha: 0.2),
    borderRadius: BorderRadius.circular(size / 2),
  );
  
  // ==================== ESTILOS DE BOTÃO ====================
  
  /// Botão primário
  static ButtonStyle primaryButtonStyle = ElevatedButton.styleFrom(
    backgroundColor: accent,
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(borderRadius),
    ),
  );
  
  /// Botão de texto
  static ButtonStyle textButtonStyle = TextButton.styleFrom(
    foregroundColor: accent,
  );
  
  /// Botão de perigo
  static ButtonStyle dangerButtonStyle = TextButton.styleFrom(
    foregroundColor: error,
  );
  
  // ==================== MÉTODOS UTILITÁRIOS ====================
  
  /// Obtém cor por contexto
  static Color getContextColor(String context) {
    switch (context.toLowerCase()) {
      case 'accounts':
      case 'contas':
        return accounts;
      case 'categories':
      case 'categorias':
        return categories;
      case 'transactions':
      case 'transacoes':
        return transactions;
      case 'reports':
      case 'relatorios':
        return reports;
      default:
        return accent;
    }
  }
  
  /// Obtém cor de texto baseada no estado ativo
  static Color getTextColor(bool isActive, {double? alpha}) {
    if (isActive) {
      return alpha != null 
          ? textPrimary.withValues(alpha: alpha)
          : textPrimary;
    } else {
      return alpha != null 
          ? textPrimary.withValues(alpha: alpha * 0.6)
          : textPrimary.withValues(alpha: 0.6);
    }
  }
  
  /// Obtém opacidade baseada no estado ativo
  static double getOpacity(bool isActive) {
    return isActive ? 1.0 : 0.6;
  }
  
  /// Obtém elevação baseada no estado ativo
  static double getElevation(bool isActive) {
    return isActive ? elevationMedium : elevationLow;
  }
  
  // ==================== TEMAS DE APPBAR ====================
  
  /// AppBar transparente padrão
  static AppBarTheme get transparentAppBarTheme => const AppBarTheme(
    backgroundColor: Colors.transparent,
    elevation: 0,
    titleTextStyle: TextStyle(
      color: textPrimary,
      fontWeight: FontWeight.bold,
    ),
    iconTheme: IconThemeData(color: textPrimary),
  );
  
  // ==================== TEMA DE SNACKBAR ====================
  
  /// SnackBar de sucesso
  static SnackBarThemeData get successSnackBarTheme => const SnackBarThemeData(
    backgroundColor: success,
    contentTextStyle: TextStyle(color: textPrimary),
  );
  
  /// SnackBar de erro
  static SnackBarThemeData get errorSnackBarTheme => const SnackBarThemeData(
    backgroundColor: error,
    contentTextStyle: TextStyle(color: textPrimary),
  );
}
