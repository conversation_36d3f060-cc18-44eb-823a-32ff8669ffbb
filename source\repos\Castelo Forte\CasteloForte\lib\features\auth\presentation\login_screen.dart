import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/constants.dart';
import '../../../core/utils/validators.dart';
import '../../../core/widgets/auth_widgets.dart';
import '../data/auth_service.dart';

/// Tela de Login moderna e responsiva
class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _cpfController = TextEditingController();
  final _passwordController = TextEditingController();
  final _cpfFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();

  bool _isPasswordVisible = false;
  bool _isLoading = false;
  bool _rememberMe = false;

  @override
  void dispose() {
    _cpfController.dispose();
    _passwordController.dispose();
    _cpfFocusNode.dispose();
    _passwordFocusNode.dispose();
    super.dispose();
  }

  void _togglePasswordVisibility() {
    setState(() {
      _isPasswordVisible = !_isPasswordVisible;
    });
  }

  Future<void> _handleLogin() async {
    if (_formKey.currentState?.validate() ?? false) {
      setState(() {
        _isLoading = true;
      });

      try {
        // Realiza o login
        await AuthService.login(_cpfController.text, _passwordController.text);

        // Navega para o dashboard se o login for bem-sucedido
        if (mounted) {
          context.go(AppConstants.dashboardRoute);
        }
      } catch (e) {
        // Exibe mensagem de erro
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(e.toString()),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      } finally {
        // Desativa o loading
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  void _handleCreateAccount() {
    // Navega para a tela de cadastro usando go_router
    context.go(AppConstants.registerRoute);
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812), // iPhone X design size
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return Scaffold(
          backgroundColor: AppTheme.navyBlueColor,
          body: SafeArea(
            child: LayoutBuilder(
              builder: (context, constraints) {
                final isTablet = constraints.maxWidth > 600;
                final isDesktop = constraints.maxWidth > 1200;

                return Container(
                  width: double.infinity,
                  height: constraints.maxHeight,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        AppTheme.navyBlueColor,
                        AppTheme.navyBlueColor.withAlpha(230),
                      ],
                    ),
                  ),
                  child: SingleChildScrollView(
                    physics: const BouncingScrollPhysics(),
                    child: ResponsiveAuthContainer(
                      showBackground: isTablet || isDesktop,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          SizedBox(height: isDesktop ? 40.h : 20.h),

                          SizedBox(height: isDesktop ? 60.h : 40.h),

                          // Formulário de login
                          _buildLoginForm(),

                          SizedBox(height: 32.h),

                          // Link "Esqueci minha senha"
                          _buildForgotPasswordLink(),

                          SizedBox(height: 32.h),

                          // Divisor
                          const AuthDivider(text: 'ou entre com'),

                          SizedBox(height: 24.h),

                          // Botão Google
                          _buildGoogleButton(),

                          SizedBox(height: 40.h),

                          // Link criar conta
                          _buildCreateAccountLink(),

                          SizedBox(height: 24.h),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  /// Constrói o formulário de login
  Widget _buildLoginForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Campo de CPF
          ResponsiveAuthField(
            controller: _cpfController,
            focusNode: _cpfFocusNode,
            labelText: 'CPF',
            hintText: 'Digite seu CPF',
            keyboardType: TextInputType.number,
            textInputAction: TextInputAction.next,
            validator: Validators.validateCpf,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              TextFormatters.cpfFormatter,
            ],
          ),

          SizedBox(height: 20.h),

          // Campo de senha
          ResponsiveAuthField(
            controller: _passwordController,
            focusNode: _passwordFocusNode,
            labelText: 'Senha',
            hintText: 'Digite sua senha',
            obscureText: !_isPasswordVisible,
            textInputAction: TextInputAction.done,
            validator: (value) => Validators.validatePassword(
              value,
              minLength: AppConstants.minPasswordLength,
            ),
            suffixIcon: IconButton(
              icon: Icon(
                _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                color: AppTheme.snowWhiteColor,
              ),
              onPressed: _togglePasswordVisibility,
            ),
          ),

          SizedBox(height: 24.h),

          // Checkbox "Lembrar-me"
          ResponsiveAuthCheckbox(
            value: _rememberMe,
            onChanged: (value) {
              setState(() {
                _rememberMe = value ?? false;
              });
            },
            text: 'Lembrar-me',
          ),

          SizedBox(height: 32.h),

          // Botão de login
          ResponsiveAuthButton(
            text: 'Entrar',
            onPressed: _handleLogin,
            isLoading: _isLoading,
          ),
        ],
      ),
    );
  }

  /// Constrói o link "Esqueci minha senha"
  Widget _buildForgotPasswordLink() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isTablet = constraints.maxWidth > 600;
        final isDesktop = constraints.maxWidth > 1200;

        double horizontalPadding = 0;
        if (isDesktop) {
          horizontalPadding = constraints.maxWidth * 0.1;
        } else if (isTablet) {
          horizontalPadding = constraints.maxWidth * 0.05;
        }

        return Padding(
          padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
          child: Align(
            alignment: Alignment.centerRight,
            child: TextButton(
              onPressed: () {
                // TODO: Implementar "Esqueci minha senha"
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Funcionalidade será implementada em breve!'),
                    backgroundColor: AppTheme.warningColor,
                  ),
                );
              },
              child: Text(
                'Esqueci minha senha',
                style: TextStyle(
                  color: AppTheme.goldColor,
                  fontSize: isDesktop
                      ? 16.sp
                      : isTablet
                      ? 14.sp
                      : 12.sp,
                  decoration: TextDecoration.underline,
                  decorationColor: AppTheme.goldColor,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Constrói o botão do Google
  Widget _buildGoogleButton() {
    return ResponsiveAuthButton(
      text: 'Continuar com Google',
      isOutlined: true,
      icon: const Icon(
        Icons
            .g_mobiledata, // Temporário - será substituído pelo ícone do Google
        color: AppTheme.snowWhiteColor,
        size: 24,
      ),
      onPressed: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Login com Google será implementado em breve!'),
            backgroundColor: AppTheme.warningColor,
          ),
        );
      },
    );
  }

  /// Constrói o link "Crie uma conta"
  Widget _buildCreateAccountLink() {
    return AuthNavigationLink(
      text: 'Não tem uma conta? ',
      linkText: 'Crie uma conta',
      onPressed: _handleCreateAccount,
    );
  }
}
