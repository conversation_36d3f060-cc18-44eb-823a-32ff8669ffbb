﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using MongoDB.Driver;
using MongoDB.EntityFrameworkCore.Extensions;
using Shared.Entities.Admin;
using Shared.Models.Configuration;

namespace RepositoryAdmin.Configuration
{
    public class ContextBaseAdmin : DbContext
    {
        private readonly IMongoDatabase _mongoDatabase;

        static ContextBaseAdmin()
        {
            try
            {
                MongoDbClassMapConfiguration.Configure();
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro no ContextBaseAdmin: {ex.Message}", ex);
            }
        }

        public ContextBaseAdmin(DbContextOptions<ContextBaseAdmin> options, IOptions<MongoDBSettings> mongoSettings) : base(options)
        {
            var settings = mongoSettings.Value;
            var mongoClient = new MongoClient(settings.ConnectionString);
            _mongoDatabase = mongoClient.GetDatabase(settings.DatabaseNameAdmin);
        }

        public IMongoCollection<Usuario> UsuarioCollection =>
            _mongoDatabase.GetCollection<Usuario>("Usuario");
        public IMongoCollection<LogErroAdmin> LogsErrosAdminCollection =>
            _mongoDatabase.GetCollection<LogErroAdmin>("LogErroAdmin");
        public IMongoCollection<HistoricoUsuario> HistoricoUsuarioCollection =>
            _mongoDatabase.GetCollection<HistoricoUsuario>("HistoricoUsuario");

        public IMongoDatabase Database => _mongoDatabase;

        public IMongoCollection<T> GetCollection<T>(string collectionName)
        {
            return _mongoDatabase.GetCollection<T>(collectionName);
        }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            try
            {
                builder.Entity<Usuario>().ToCollection("Usuario");
                builder.Entity<LogErroAdmin>().ToCollection("LogErroAdmin");
                builder.Entity<HistoricoUsuario>().ToCollection("HistoricoUsuario");
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro context OnModelCreating: {ex.Message}", ex);
            }
        }
    }
}