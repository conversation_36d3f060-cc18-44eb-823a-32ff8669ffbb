using AutoMapper;
using Microsoft.AspNetCore.Http;
using RepositoryClient.Interfaces;
using ServiceClient.Interfaces;
using ServiceClient.Repository.Generic;
using Shared.Entities.Client;
using Shared.ViewModels.Client;
using System.Security.Claims;
using System.Security;

namespace ServiceClient.Service
{
    /// <summary>
    /// Serviço para logs de erro no contexto Client (multi-tenant)
    /// </summary>
    public class LogErroClientService : GenericClientService<LogErroClientViewModel, LogErroClient>, ILogErroClientService
    {
        private readonly ILogErroClientRepository _logErroRepository;

        public LogErroClientService(
            ILogErroClientRepository logErroRepository,
            IHttpContextAccessor httpContextAccessor,
            IMapper mapper) : base(logErroRepository, httpContextAccessor, mapper)
        {
            _logErroRepository = logErroRepository ?? throw new ArgumentNullException(nameof(logErroRepository));
        }

        /// <summary>
        /// Registra um erro no sistema
        /// </summary>
        /// <param name="ex">Exceção que ocorreu</param>
        /// <param name="metodo">Método onde ocorreu o erro</param>
        /// <param name="controller">Controller onde ocorreu o erro</param>
        /// <param name="variaveis">Variáveis relacionadas ao erro</param>
        /// <param name="informacoesAdicionais">Informações adicionais sobre o contexto</param>
        /// <returns>Task</returns>
        public async Task LogErro(Exception ex, string metodo, string controller, string variaveis, string? informacoesAdicionais = null)
        {
            try
            {
                var logErro = new LogErroClient
                {
                    DtaCadastro = DateTime.UtcNow,
                    Erro = ex.Message,
                    IdUsuario = IdUsuarioLogado,
                    Metodo = metodo,
                    Controller = controller,
                    Variaveis = variaveis,
                    StackTrace = ex.StackTrace,
                    TipoExcecao = ex.GetType().Name,
                    InformacoesAdicionais = informacoesAdicionais,
                    Severidade = DeterminarSeveridade(ex),
                    FlgAtivo = true
                };

                await _logErroRepository.AdicionarAsync(logErro);
            }
            catch (Exception)
            {
                // Em caso de erro ao registrar o log, não queremos quebrar o fluxo principal
                // Apenas registra no console para debug
                Console.WriteLine($"Erro ao registrar log de erro: {ex.Message}");
            }
        }

        /// <summary>
        /// Registra um erro no sistema com informações de contexto HTTP
        /// </summary>
        /// <param name="ex">Exceção que ocorreu</param>
        /// <param name="metodo">Método onde ocorreu o erro</param>
        /// <param name="controller">Controller onde ocorreu o erro</param>
        /// <param name="variaveis">Variáveis relacionadas ao erro</param>
        /// <param name="ipCliente">IP do cliente</param>
        /// <param name="userAgent">User Agent do cliente</param>
        /// <param name="informacoesAdicionais">Informações adicionais sobre o contexto</param>
        /// <returns>Task</returns>
        public async Task LogErroComContexto(Exception ex, string metodo, string controller, string variaveis,
            string? ipCliente = null, string? userAgent = null, string? informacoesAdicionais = null)
        {
            try
            {
                // Obtém informações do contexto HTTP se não foram fornecidas
                if (string.IsNullOrEmpty(ipCliente) && _httpContextAccessor.HttpContext != null)
                {
                    ipCliente = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress?.ToString();
                }

                if (string.IsNullOrEmpty(userAgent) && _httpContextAccessor.HttpContext != null)
                {
                    userAgent = _httpContextAccessor.HttpContext.Request.Headers["User-Agent"].FirstOrDefault();
                }

                var logErro = new LogErroClient
                {
                    DtaCadastro = DateTime.UtcNow,
                    Erro = ex.Message,
                    IdUsuario = IdUsuarioLogado,
                    Metodo = metodo,
                    Controller = controller,
                    Variaveis = variaveis,
                    StackTrace = ex.StackTrace,
                    TipoExcecao = ex.GetType().Name,
                    InformacoesAdicionais = informacoesAdicionais,
                    Severidade = DeterminarSeveridade(ex),
                    IpCliente = ipCliente,
                    UserAgent = userAgent,
                    FlgAtivo = true
                };

                await _logErroRepository.AdicionarAsync(logErro);
            }
            catch (Exception)
            {
                // Em caso de erro ao registrar o log, não queremos quebrar o fluxo principal
                Console.WriteLine($"Erro ao registrar log de erro com contexto: {ex.Message}");
            }
        }

        /// <summary>
        /// Busca logs de erro por usuário
        /// </summary>
        /// <param name="idUsuario">ID do usuário</param>
        /// <returns>Lista de logs de erro do usuário</returns>
        public async Task<IEnumerable<LogErroClientViewModel>> BuscarPorUsuarioAsync(string idUsuario)
        {
            var logs = await _logErroRepository.BuscarPorUsuarioAsync(idUsuario);
            return logs.Select(ConverteEntidadeParaViewModel);
        }

        /// <summary>
        /// Busca logs de erro por período
        /// </summary>
        /// <param name="dataInicio">Data de início</param>
        /// <param name="dataFim">Data de fim</param>
        /// <returns>Lista de logs de erro no período</returns>
        public async Task<IEnumerable<LogErroClientViewModel>> BuscarPorPeriodoAsync(DateTime dataInicio, DateTime dataFim)
        {
            var logs = await _logErroRepository.BuscarPorPeriodoAsync(dataInicio, dataFim);
            return logs.Select(ConverteEntidadeParaViewModel);
        }

        /// <summary>
        /// Busca logs de erro por severidade
        /// </summary>
        /// <param name="severidade">Severidade do erro</param>
        /// <returns>Lista de logs de erro com a severidade especificada</returns>
        public async Task<IEnumerable<LogErroClientViewModel>> BuscarPorSeveridadeAsync(string severidade)
        {
            var logs = await _logErroRepository.BuscarPorSeveridadeAsync(severidade);
            return logs.Select(ConverteEntidadeParaViewModel);
        }

        /// <summary>
        /// Determina a severidade do erro baseado no tipo da exceção
        /// </summary>
        /// <param name="ex">Exceção</param>
        /// <returns>Severidade do erro</returns>
        private static string DeterminarSeveridade(Exception ex)
        {
            return ex switch
            {
                ArgumentException or ArgumentNullException or ArgumentOutOfRangeException => "Warning",
                UnauthorizedAccessException or SecurityException => "Error",
                OutOfMemoryException or StackOverflowException => "Critical",
                _ => "Error"
            };
        }
    }
}
