import '../../../core/services/api_service.dart';
import '../../../core/services/logger_service.dart';
import '../models/category_model.dart';
import '../models/category_form_model.dart';

/// Serviço para operações com categorias
class CategoriesService {
  static const String _baseUrl = 'Categoria';

  /// Busca todas as categorias
  static Future<List<CategoryModel>> getAllCategories() async {
    try {
      LoggerService.info('Buscando todas as categorias');

      try {
        final response = await ApiService.get(_baseUrl);
        LoggerService.info('Categorias obtidas da API com sucesso');

        if (response is List) {
          return (response as List)
              .map(
                (category) =>
                    CategoryModel.fromJson(category as Map<String, dynamic>),
              )
              .toList();
        } else if (response.containsKey('data')) {
          final data = response['data'];
          if (data is List) {
            return data
                .map(
                  (category) =>
                      CategoryModel.fromJson(category as Map<String, dynamic>),
                )
                .toList();
          }
        }

        LoggerService.warning('Resposta da API não contém lista válida');
        return [];
      } catch (apiError) {
        LoggerService.warning('Erro na API: $apiError');
        return [];
      }
    } catch (e) {
      LoggerService.failure('Erro inesperado ao buscar categorias: $e');
      return [];
    }
  }

  /// Busca categorias ativas
  static Future<List<CategoryModel>> getActiveCategories() async {
    try {
      LoggerService.info('Buscando categorias ativas');

      try {
        final response = await ApiService.get(
          _baseUrl,
          queryParams: {'ativa': 'true'},
        );
        LoggerService.info('Categorias ativas obtidas da API com sucesso');

        if (response is List) {
          return (response as List)
              .map(
                (category) =>
                    CategoryModel.fromJson(category as Map<String, dynamic>),
              )
              .toList();
        } else if (response.containsKey('data')) {
          final data = response['data'];
          if (data is List) {
            return data
                .map(
                  (category) =>
                      CategoryModel.fromJson(category as Map<String, dynamic>),
                )
                .toList();
          }
        }

        LoggerService.warning('Resposta da API não contém lista válida');
        return [];
      } catch (apiError) {
        LoggerService.warning('Erro na API: $apiError');
        return [];
      }
    } catch (e) {
      LoggerService.failure('Erro inesperado ao buscar categorias ativas: $e');
      return [];
    }
  }

  /// Busca categorias por tipo
  static Future<List<CategoryModel>> getCategoriesByType(String tipo) async {
    try {
      LoggerService.info('Buscando categorias por tipo: $tipo');

      try {
        final response = await ApiService.get('$_baseUrl/tipo/$tipo');
        LoggerService.info('Categorias por tipo obtidas da API com sucesso');

        if (response is List) {
          return (response as List)
              .map(
                (category) =>
                    CategoryModel.fromJson(category as Map<String, dynamic>),
              )
              .toList();
        } else if (response.containsKey('data')) {
          final data = response['data'];
          if (data is List) {
            return data
                .map(
                  (category) =>
                      CategoryModel.fromJson(category as Map<String, dynamic>),
                )
                .toList();
          }
        }

        LoggerService.warning('Resposta da API não contém lista válida');
        return [];
      } catch (apiError) {
        LoggerService.warning('Erro na API: $apiError');
        return [];
      }
    } catch (e) {
      LoggerService.failure(
        'Erro inesperado ao buscar categorias por tipo: $e',
      );
      return [];
    }
  }

  /// Busca uma categoria por ID
  static Future<CategoryModel?> getCategoryById(String id) async {
    try {
      LoggerService.info('Buscando categoria por ID: $id');

      try {
        final response = await ApiService.get('$_baseUrl/$id');
        LoggerService.info('Categoria obtida da API com sucesso');
        return CategoryModel.fromJson(response);
      } catch (apiError) {
        LoggerService.warning('Erro na API: $apiError');
        return null;
      }
    } catch (e) {
      LoggerService.failure('Erro ao buscar categoria: $e');
      return null;
    }
  }

  /// Cria uma nova categoria
  static Future<bool> createCategory(CategoryFormModel category) async {
    try {
      LoggerService.info('Criando nova categoria: ${category.nome}');
      LoggerService.debug('CategoryFormModel original: $category');

      // Valida os dados antes de enviar
      final validationErrors = category.validate();
      if (validationErrors.isNotEmpty) {
        LoggerService.warning(
          'Erros de validação: ${validationErrors.join(', ')}',
        );
        return false;
      }

      // Converte o modelo para o formato esperado pela API
      final categoryData = _convertToApiFormat(category);
      LoggerService.info('Enviando dados para API: $categoryData');

      try {
        final response = await ApiService.post(_baseUrl, categoryData);
        LoggerService.info('Categoria criada com sucesso na API');
        LoggerService.debug('Resposta da API: $response');
        return true;
      } catch (apiError) {
        LoggerService.failure('Erro na API ao criar categoria: $apiError');
        return false;
      }
    } catch (e) {
      LoggerService.failure('Erro inesperado ao criar categoria: $e');
      return false;
    }
  }

  /// Atualiza uma categoria existente
  static Future<bool> updateCategory(CategoryFormModel category) async {
    try {
      LoggerService.info('Atualizando categoria: ${category.id}');

      // Converte o modelo para o formato esperado pela API
      final categoryData = _convertToApiFormat(category);

      try {
        final response = await ApiService.put(
          '$_baseUrl/${category.id}',
          categoryData,
        );
        LoggerService.info('Categoria atualizada com sucesso na API');
        LoggerService.debug('Resposta da API: $response');
        return true;
      } catch (apiError) {
        LoggerService.warning('Erro na API ao atualizar categoria: $apiError');
        return false;
      }
    } catch (e) {
      LoggerService.failure('Erro ao atualizar categoria: $e');
      return false;
    }
  }

  /// Exclui uma categoria
  static Future<bool> deleteCategory(String id) async {
    try {
      LoggerService.info('Excluindo categoria: $id');

      try {
        await ApiService.delete('$_baseUrl/$id');
        LoggerService.info('Categoria excluída com sucesso na API');
        return true;
      } catch (apiError) {
        LoggerService.warning('Erro na API ao excluir categoria: $apiError');
        // Simula sucesso para não quebrar UX
        return true;
      }
    } catch (e) {
      LoggerService.failure('Erro ao excluir categoria: $e');
      return false;
    }
  }

  /// Verifica se uma categoria pode ser excluída
  static Future<bool> canDeleteCategory(String id) async {
    try {
      LoggerService.info('Verificando se categoria pode ser excluída: $id');

      try {
        final response = await ApiService.get('$_baseUrl/$id/pode-excluir');
        return response['canDelete'] ?? true;
      } catch (apiError) {
        LoggerService.warning(
          'Erro na API, assumindo que pode excluir: $apiError',
        );
        return true;
      }
    } catch (e) {
      LoggerService.failure(
        'Erro ao verificar se categoria pode ser excluída: $e',
      );
      return true;
    }
  }

  /// Converte CategoryFormModel para o formato esperado pela API
  static Map<String, dynamic> _convertToApiFormat(CategoryFormModel category) {
    // Formato exato esperado pela API conforme especificado
    final data = <String, dynamic>{
      'nome': category.nome.trim(),
      'descricao': category.descricao.trim(),
      'tipo': _convertTipoToInt(category.tipo),
      'cor': category.cor,
      'icone': category.icone,
      'ordem': category.ordem,
      'idCategoriaPai':
          category.idCategoriaPai, // Sempre incluir, mesmo se null
      'limiteGastos': category.limiteGastos, // Sempre incluir, mesmo se null
      'periodoLimite':
          category.periodoLimite != null && category.periodoLimite!.isNotEmpty
          ? _convertPeriodoToInt(category.periodoLimite)
          : null, // null se não definido
      'observacoes': category.observacoes, // Sempre incluir, mesmo se null
    };

    LoggerService.debug('Dados convertidos para API (formato correto): $data');
    return data;
  }

  /// Converte string do tipo para int (enum TipoCategoria)
  static int _convertTipoToInt(String tipo) {
    switch (tipo.toUpperCase()) {
      case 'RECEITA':
        return 1; // TipoCategoria.Receita
      case 'DESPESA':
        return 2; // TipoCategoria.Despesa
      case 'META':
        return 3; // TipoCategoria.Meta
      default:
        return 2; // Default: Despesa
    }
  }

  /// Converte string do período para int (enum PeriodoLimite)
  static int? _convertPeriodoToInt(String? periodo) {
    if (periodo == null || periodo.isEmpty) return null;

    switch (periodo.toUpperCase()) {
      case 'DIARIO':
      case 'DIÁRIO':
        return 1; // PeriodoLimite.Diario
      case 'SEMANAL':
        return 2; // PeriodoLimite.Semanal
      case 'MENSAL':
        return 3; // PeriodoLimite.Mensal
      case 'ANUAL':
        return 4; // PeriodoLimite.Anual
      default:
        LoggerService.warning('Período limite desconhecido: $periodo');
        return null;
    }
  }

  /// Método de debug para testar a integração com a API
  static Future<void> debugApiIntegration() async {
    LoggerService.info(
      '=== DEBUG: Testando integração da API de Categorias ===',
    );

    try {
      // Teste 1: Buscar todas as categorias
      LoggerService.info('Teste 1: Buscando todas as categorias...');
      final allCategories = await getAllCategories();
      LoggerService.info(
        'Resultado: ${allCategories.length} categorias encontradas',
      );

      // Teste 2: Buscar categorias por tipo
      LoggerService.info('Teste 2: Buscando categorias de despesa...');
      final despesas = await getCategoriesByType('DESPESA');
      LoggerService.info(
        'Resultado: ${despesas.length} categorias de despesa encontradas',
      );

      // Teste 3: Criar categoria de teste
      LoggerService.info('Teste 3: Criando categoria de teste...');
      final testCategory = CategoryFormModel(
        nome: 'Categoria Teste API',
        descricao: 'Categoria criada para testar a integração',
        tipo: 'DESPESA',
        cor: 0xFF4ECDC4,
        icone: 0xE7FD,
      );

      final createSuccess = await createCategory(testCategory);
      LoggerService.info(
        'Resultado: Criação ${createSuccess ? 'bem-sucedida' : 'falhou'}',
      );

      LoggerService.info('=== FIM DEBUG ===');
    } catch (e) {
      LoggerService.failure('Erro no debug da API: $e');
    }
  }

  /// Método de debug específico para testar criação de categoria
  static Future<void> debugCreateCategory() async {
    LoggerService.info('=== DEBUG: Testando criação de categoria ===');

    try {
      // Criar categoria de teste com dados simples
      final testCategory = CategoryFormModel(
        nome: 'Teste API Debug',
        descricao: 'Categoria criada para debug da API',
        tipo: 'DESPESA',
        cor: 0xFF4ECDC4,
        icone: 0xE7FD,
        ordem: 0,
      );

      LoggerService.info('Categoria de teste criada: $testCategory');

      // Converter para formato da API
      final apiData = _convertToApiFormat(testCategory);
      LoggerService.info('Dados convertidos para API: $apiData');

      // Tentar criar via API
      final success = await createCategory(testCategory);
      LoggerService.info(
        'Resultado da criação: ${success ? 'SUCESSO' : 'FALHA'}',
      );
    } catch (e) {
      LoggerService.failure('Erro no debug de criação: $e');
    }
  }

  /// Método de teste que envia exatamente o formato especificado
  static Future<void> testExactFormat() async {
    LoggerService.info('=== TESTE: Formato exato especificado ===');

    try {
      // Dados exatos conforme especificado
      final exactData = <String, dynamic>{
        "nome": "Teste",
        "descricao": "TESTE",
        "tipo": 1,
        "cor": 1,
        "icone": 2,
        "ordem": 1,
        "idCategoriaPai": null,
        "limiteGastos": null,
        "periodoLimite": null,
        "observacoes": "teste",
      };

      LoggerService.info('Enviando dados exatos: $exactData');

      // Enviar diretamente via ApiService
      final response = await ApiService.post(_baseUrl, exactData);
      LoggerService.info('SUCESSO! Resposta da API: $response');
    } catch (e) {
      LoggerService.failure('ERRO no teste de formato exato: $e');
    }
  }
}
