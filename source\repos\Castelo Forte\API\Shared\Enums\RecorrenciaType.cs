using System.ComponentModel;

namespace Shared.Enums
{
    /// <summary>
    /// Tipos de recorrência para transações
    /// </summary>
    public enum RecorrenciaType
    {
        [Description("Diária")]
        Diaria = 1,

        [Description("Semanal")]
        Semanal = 2,

        [Description("Quinzenal")]
        Quinzenal = 3,

        [Description("Mensal")]
        Mensal = 4,

        [Description("Bimestral")]
        Bimestral = 5,

        [Description("Trimestral")]
        Trimestral = 6,

        [Description("Semestral")]
        Semestral = 7,

        [Description("Anual")]
        Anual = 8,

        [Description("Personalizada")]
        Personalizada = 9
    }

    /// <summary>
    /// Status da transação recorrente
    /// </summary>
    public enum RecorrenciaStatus
    {
        [Description("Ativa")]
        Ativa = 1,

        [Description("Pausada")]
        Pausada = 2,

        [Description("Concluída")]
        Concluida = 3,

        [Description("Cancelada")]
        Cancelada = 4
    }

    /// <summary>
    /// Extensões para os enums de recorrência
    /// </summary>
    public static class RecorrenciaExtensions
    {
        /// <summary>
        /// Retorna a descrição do tipo de recorrência
        /// </summary>
        public static string GetDescription(this RecorrenciaType tipo)
        {
            var field = tipo.GetType().GetField(tipo.ToString());
            var attribute = (DescriptionAttribute?)Attribute.GetCustomAttribute(field!, typeof(DescriptionAttribute));
            return attribute?.Description ?? tipo.ToString();
        }

        /// <summary>
        /// Retorna a descrição do status da recorrência
        /// </summary>
        public static string GetDescription(this RecorrenciaStatus status)
        {
            var field = status.GetType().GetField(status.ToString());
            var attribute = (DescriptionAttribute?)Attribute.GetCustomAttribute(field!, typeof(DescriptionAttribute));
            return attribute?.Description ?? status.ToString();
        }

        /// <summary>
        /// Retorna o número de dias para o tipo de recorrência
        /// </summary>
        public static int GetDias(this RecorrenciaType tipo)
        {
            return tipo switch
            {
                RecorrenciaType.Diaria => 1,
                RecorrenciaType.Semanal => 7,
                RecorrenciaType.Quinzenal => 15,
                RecorrenciaType.Mensal => 30,
                RecorrenciaType.Bimestral => 60,
                RecorrenciaType.Trimestral => 90,
                RecorrenciaType.Semestral => 180,
                RecorrenciaType.Anual => 365,
                RecorrenciaType.Personalizada => 0, // Definido pelo usuário
                _ => 30 // Default mensal
            };
        }

        /// <summary>
        /// Calcula a próxima data de execução
        /// </summary>
        public static DateTime CalcularProximaExecucao(this RecorrenciaType tipo, DateTime dataBase, int? diasPersonalizados = null)
        {
            return tipo switch
            {
                RecorrenciaType.Diaria => dataBase.AddDays(1),
                RecorrenciaType.Semanal => dataBase.AddDays(7),
                RecorrenciaType.Quinzenal => dataBase.AddDays(15),
                RecorrenciaType.Mensal => dataBase.AddMonths(1),
                RecorrenciaType.Bimestral => dataBase.AddMonths(2),
                RecorrenciaType.Trimestral => dataBase.AddMonths(3),
                RecorrenciaType.Semestral => dataBase.AddMonths(6),
                RecorrenciaType.Anual => dataBase.AddYears(1),
                RecorrenciaType.Personalizada => dataBase.AddDays(diasPersonalizados ?? 30),
                _ => dataBase.AddMonths(1) // Default mensal
            };
        }

        /// <summary>
        /// Verifica se a recorrência está ativa
        /// </summary>
        public static bool IsAtiva(this RecorrenciaStatus status)
        {
            return status == RecorrenciaStatus.Ativa;
        }

        /// <summary>
        /// Verifica se pode gerar transações
        /// </summary>
        public static bool PodeGerarTransacoes(this RecorrenciaStatus status)
        {
            return status == RecorrenciaStatus.Ativa;
        }

        /// <summary>
        /// Converte string para RecorrenciaType
        /// </summary>
        public static RecorrenciaType FromString(string tipo)
        {
            return tipo.ToLower() switch
            {
                "diária" or "diaria" or "daily" => RecorrenciaType.Diaria,
                "semanal" or "weekly" => RecorrenciaType.Semanal,
                "quinzenal" or "biweekly" => RecorrenciaType.Quinzenal,
                "mensal" or "monthly" => RecorrenciaType.Mensal,
                "bimestral" or "bimonthly" => RecorrenciaType.Bimestral,
                "trimestral" or "quarterly" => RecorrenciaType.Trimestral,
                "semestral" or "semiannual" => RecorrenciaType.Semestral,
                "anual" or "yearly" => RecorrenciaType.Anual,
                "personalizada" or "custom" => RecorrenciaType.Personalizada,
                _ => RecorrenciaType.Mensal // Default
            };
        }

        /// <summary>
        /// Converte string para RecorrenciaStatus
        /// </summary>
        public static RecorrenciaStatus StatusFromString(string status)
        {
            return status.ToLower() switch
            {
                "ativa" or "active" => RecorrenciaStatus.Ativa,
                "pausada" or "paused" => RecorrenciaStatus.Pausada,
                "concluída" or "concluida" or "completed" => RecorrenciaStatus.Concluida,
                "cancelada" or "cancelled" => RecorrenciaStatus.Cancelada,
                _ => RecorrenciaStatus.Ativa // Default
            };
        }
    }
}
