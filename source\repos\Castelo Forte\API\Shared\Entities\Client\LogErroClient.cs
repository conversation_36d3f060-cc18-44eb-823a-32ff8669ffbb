﻿using MongoDB.Bson.Serialization.Attributes;
using Shared.Entities.Base;

namespace Shared.Entities.Client
{
    /// <summary>
    /// Entidade para armazenar logs de erro no contexto Client (multi-tenant)
    /// </summary>
    public class LogErroClient : BaseEntidade
    {
        /// <summary>
        /// Mensagem de erro
        /// </summary>
        public string Erro { get; set; } = "";

        /// <summary>
        /// Método onde ocorreu o erro
        /// </summary>
        public string Metodo { get; set; } = "";

        /// <summary>
        /// Controller onde ocorreu o erro
        /// </summary>
        public string Controller { get; set; } = "";

        /// <summary>
        /// Variáveis relacionadas ao erro (JSON serializado)
        /// </summary>
        public string Variaveis { get; set; } = "";

        /// <summary>
        /// ID do usuário que estava executando a operação
        /// </summary>
        public string? IdUsuario { get; set; }

        /// <summary>
        /// Stack trace completo do erro
        /// </summary>
        public string? StackTrace { get; set; }

        /// <summary>
        /// Tipo da exceção
        /// </summary>
        public string? TipoExcecao { get; set; }

        /// <summary>
        /// Informações adicionais sobre o contexto do erro
        /// </summary>
        public string? InformacoesAdicionais { get; set; }

        /// <summary>
        /// Severidade do erro (Info, Warning, Error, Critical)
        /// </summary>
        public string Severidade { get; set; } = "Error";

        /// <summary>
        /// IP do cliente que gerou o erro
        /// </summary>
        public string? IpCliente { get; set; }

        /// <summary>
        /// User Agent do cliente
        /// </summary>
        public string? UserAgent { get; set; }
    }
}
