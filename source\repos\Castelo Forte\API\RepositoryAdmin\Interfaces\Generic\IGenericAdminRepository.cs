﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace RepositoryAdmin.Interfaces.Generic
{
    public interface IGenericAdminRepository<TEntidade> where TEntidade : class
    {
        Task<IEnumerable<TEntidade?>> BuscarTodosAsync();
        Task<IEnumerable<TEntidade?>> BuscarPorFiltroAsync(Expression<Func<TEntidade?, bool>> expression);
        Task<IEnumerable<TEntidade?>> BuscarPorFiltroPaginadoAsync(Expression<Func<TEntidade?, bool>> expression, int page, int pageSize);
        Task<TEntidade?> BuscarPorIdAsync(string Id);
        Task<TEntidade?> BuscarPrimeiroPorFiltroAsync(Expression<Func<TEntidade?, bool>> expression);
        Task<int> BuscarContagemTotalPorFiltroAsync(Expression<Func<TEntidade?, bool>> expression);
        Task<int> BuscarContagemTotalAsync();
        Task<TEntidade?> AdicionarAsync(TEntidade Object);
        Task<TEntidade?> EditarAsync(TEntidade Object);
        Task ExcluirAsync(TEntidade Object);
    }
}
