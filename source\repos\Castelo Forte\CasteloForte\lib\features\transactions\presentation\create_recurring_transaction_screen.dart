import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CreateRecurringTransactionScreen extends StatefulWidget {
  final Map<String, dynamic> baseTransaction;

  const CreateRecurringTransactionScreen({
    super.key,
    required this.baseTransaction,
  });

  @override
  State<CreateRecurringTransactionScreen> createState() => _CreateRecurringTransactionScreenState();
}

class _CreateRecurringTransactionScreenState extends State<CreateRecurringTransactionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();
  final _occurrencesController = TextEditingController();

  String _frequency = 'MENSAL';
  DateTime _startDate = DateTime.now();
  DateTime? _endDate;
  bool _hasEndDate = false;
  bool _hasOccurrenceLimit = false;

  final List<String> _frequencies = [
    'DIARIO',
    'SEMANAL',
    'QUINZENAL',
    'MENSAL',
    'BIMESTRAL',
    'TRIMESTRAL',
    'SEMESTRAL',
    'ANUAL',
  ];

  final Map<String, String> _frequencyLabels = {
    'DIARIO': 'Diário',
    'SEMANAL': 'Semanal',
    'QUINZENAL': 'Quinzenal',
    'MENSAL': 'Mensal',
    'BIMESTRAL': 'Bimestral',
    'TRIMESTRAL': 'Trimestral',
    'SEMESTRAL': 'Semestral',
    'ANUAL': 'Anual',
  };

  @override
  void initState() {
    super.initState();
    _loadBaseTransactionData();
  }

  void _loadBaseTransactionData() {
    _descriptionController.text = widget.baseTransaction['descricao'] ?? '';
    _amountController.text = (widget.baseTransaction['valor']?.abs() ?? 0.0).toString();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Criar Recorrência',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header informativo
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: const Color(0xFF16213E),
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(color: Colors.purple.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.repeat,
                      color: Colors.purple,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'Configure uma transação recorrente baseada na transação selecionada',
                        style: TextStyle(color: Colors.white70),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 30),

              // Descrição
              _buildTextField(
                controller: _descriptionController,
                label: 'Descrição da Recorrência',
                hint: 'Ex: Salário mensal, Aluguel...',
                icon: Icons.description,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Descrição é obrigatória';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),

              // Valor
              _buildTextField(
                controller: _amountController,
                label: 'Valor',
                hint: '0,00',
                icon: Icons.attach_money,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9.,]')),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Valor é obrigatório';
                  }
                  final amount = double.tryParse(value.replaceAll(',', '.'));
                  if (amount == null || amount <= 0) {
                    return 'Valor deve ser maior que zero';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),

              // Frequência
              _buildFrequencyDropdown(),
              const SizedBox(height: 20),

              // Data de início
              _buildDateField(
                label: 'Data de Início',
                date: _startDate,
                onTap: _selectStartDate,
              ),
              const SizedBox(height: 20),

              // Opções de fim
              _buildEndOptions(),
              const SizedBox(height: 30),

              // Preview da recorrência
              _buildRecurrencePreview(),
              const SizedBox(height: 30),

              // Botões de ação
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: Colors.white54),
                        padding: const EdgeInsets.symmetric(vertical: 15),
                      ),
                      child: const Text(
                        'Cancelar',
                        style: TextStyle(color: Colors.white54),
                      ),
                    ),
                  ),
                  const SizedBox(width: 15),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _createRecurrence,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.purple,
                        padding: const EdgeInsets.symmetric(vertical: 15),
                      ),
                      child: const Text(
                        'Criar Recorrência',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          validator: validator,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(color: Colors.white54),
            prefixIcon: Icon(icon, color: Colors.white54),
            filled: true,
            fillColor: const Color(0xFF16213E),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.purple),
            ),
          ),
          onChanged: (value) => setState(() {}),
        ),
      ],
    );
  }

  Widget _buildFrequencyDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Frequência',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _frequency,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            prefixIcon: const Icon(Icons.schedule, color: Colors.white54),
            filled: true,
            fillColor: const Color(0xFF16213E),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.purple),
            ),
          ),
          dropdownColor: const Color(0xFF16213E),
          items: _frequencies.map((frequency) {
            return DropdownMenuItem<String>(
              value: frequency,
              child: Text(
                _frequencyLabels[frequency]!,
                style: const TextStyle(color: Colors.white),
              ),
            );
          }).toList(),
          onChanged: (String? newValue) {
            if (newValue != null) {
              setState(() => _frequency = newValue);
            }
          },
        ),
      ],
    );
  }

  Widget _buildDateField({
    required String label,
    required DateTime date,
    required VoidCallback onTap,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF16213E),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(Icons.calendar_today, color: Colors.white54),
                const SizedBox(width: 12),
                Text(
                  _formatDate(date),
                  style: const TextStyle(color: Colors.white),
                ),
                const Spacer(),
                const Icon(Icons.arrow_drop_down, color: Colors.white54),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEndOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Opções de Término',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 12),
        
        // Sem data de fim
        RadioListTile<String>(
          title: const Text('Sem data de fim', style: TextStyle(color: Colors.white)),
          subtitle: const Text('A recorrência continuará indefinidamente', style: TextStyle(color: Colors.white70, fontSize: 12)),
          value: 'none',
          groupValue: _hasEndDate ? (_hasOccurrenceLimit ? 'occurrences' : 'date') : 'none',
          onChanged: (value) {
            setState(() {
              _hasEndDate = false;
              _hasOccurrenceLimit = false;
            });
          },
          activeColor: Colors.purple,
        ),
        
        // Data de fim
        RadioListTile<String>(
          title: const Text('Definir data de fim', style: TextStyle(color: Colors.white)),
          value: 'date',
          groupValue: _hasEndDate ? (_hasOccurrenceLimit ? 'occurrences' : 'date') : 'none',
          onChanged: (value) {
            setState(() {
              _hasEndDate = true;
              _hasOccurrenceLimit = false;
            });
          },
          activeColor: Colors.purple,
        ),
        
        if (_hasEndDate && !_hasOccurrenceLimit)
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
            child: _buildDateField(
              label: 'Data de Fim',
              date: _endDate ?? DateTime.now().add(const Duration(days: 365)),
              onTap: _selectEndDate,
            ),
          ),
        
        // Número de ocorrências
        RadioListTile<String>(
          title: const Text('Limitar número de ocorrências', style: TextStyle(color: Colors.white)),
          value: 'occurrences',
          groupValue: _hasEndDate ? (_hasOccurrenceLimit ? 'occurrences' : 'date') : 'none',
          onChanged: (value) {
            setState(() {
              _hasEndDate = true;
              _hasOccurrenceLimit = true;
            });
          },
          activeColor: Colors.purple,
        ),
        
        if (_hasOccurrenceLimit)
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 16),
            child: _buildTextField(
              controller: _occurrencesController,
              label: 'Número de Ocorrências',
              hint: 'Ex: 12',
              icon: Icons.repeat,
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              validator: (value) {
                if (_hasOccurrenceLimit && (value == null || value.isEmpty)) {
                  return 'Número de ocorrências é obrigatório';
                }
                if (_hasOccurrenceLimit && int.tryParse(value!) == null) {
                  return 'Número inválido';
                }
                return null;
              },
            ),
          ),
      ],
    );
  }

  Widget _buildRecurrencePreview() {
    final amount = double.tryParse(_amountController.text.replaceAll(',', '.')) ?? 0.0;
    final description = _descriptionController.text.isEmpty ? 'Transação recorrente' : _descriptionController.text;
    final isExpense = widget.baseTransaction['isExpense'] == true;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF16213E),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.purple.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.repeat,
                color: Colors.purple,
                size: 24,
              ),
              const SizedBox(width: 12),
              const Text(
                'Preview da Recorrência',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          Text(
            description,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${isExpense ? '-' : '+'}R\$ ${amount.toStringAsFixed(2)}',
            style: TextStyle(
              color: isExpense ? Colors.red : Colors.green,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Frequência: ${_frequencyLabels[_frequency]}',
            style: const TextStyle(color: Colors.white70),
          ),
          Text(
            'Início: ${_formatDate(_startDate)}',
            style: const TextStyle(color: Colors.white70),
          ),
          if (_hasEndDate && !_hasOccurrenceLimit && _endDate != null)
            Text(
              'Fim: ${_formatDate(_endDate!)}',
              style: const TextStyle(color: Colors.white70),
            ),
          if (_hasOccurrenceLimit && _occurrencesController.text.isNotEmpty)
            Text(
              'Ocorrências: ${_occurrencesController.text}x',
              style: const TextStyle(color: Colors.white70),
            ),
          if (!_hasEndDate)
            const Text(
              'Sem data de fim',
              style: TextStyle(color: Colors.white70),
            ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  Future<void> _selectStartDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _startDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.dark(
              primary: Colors.purple,
              surface: Color(0xFF16213E),
            ),
          ),
          child: child!,
        );
      },
    );

    if (date != null) {
      setState(() => _startDate = date);
    }
  }

  Future<void> _selectEndDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _endDate ?? _startDate.add(const Duration(days: 365)),
      firstDate: _startDate.add(const Duration(days: 1)),
      lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.dark(
              primary: Colors.purple,
              surface: Color(0xFF16213E),
            ),
          ),
          child: child!,
        );
      },
    );

    if (date != null) {
      setState(() => _endDate = date);
    }
  }

  void _createRecurrence() {
    if (_formKey.currentState!.validate()) {
      final recurrenceData = {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'descricao': _descriptionController.text,
        'valor': widget.baseTransaction['isExpense'] == true 
            ? -(double.tryParse(_amountController.text.replaceAll(',', '.')) ?? 0.0)
            : (double.tryParse(_amountController.text.replaceAll(',', '.')) ?? 0.0),
        'isExpense': widget.baseTransaction['isExpense'],
        'frequencia': _frequency,
        'dataInicio': _startDate,
        'dataFim': _hasEndDate && !_hasOccurrenceLimit ? _endDate : null,
        'numeroOcorrencias': _hasOccurrenceLimit ? int.tryParse(_occurrencesController.text) : null,
        'contaId': widget.baseTransaction['contaId'],
        'categoriaId': widget.baseTransaction['categoriaId'],
        'ativa': true,
        'dataCriacao': DateTime.now(),
      };

      Navigator.pop(context, recurrenceData);
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Recorrência criada com sucesso!'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}
