﻿using Shared.Enums;
using Shared.ViewModels.Base;
using System;

namespace Shared.ViewModels.Client
{
    /// <summary>
    /// ViewModel consolidado para contas bancárias e cartões de crédito
    /// </summary>
    public class ContaViewModel : BaseViewModel
    {
        /// <summary>
        /// Nome da instituição financeira
        /// </summary>
        public string Nome { get; set; } = "";

        /// <summary>
        /// Apelido personalizado
        /// </summary>
        public string ApelidoConta { get; set; } = "";

        /// <summary>
        /// Tipo da conta
        /// </summary>
        public AccountType TipoConta { get; set; } = AccountType.ContaCorrente;

        /// <summary>
        /// Descrição do tipo da conta
        /// </summary>
        public string TipoContaDescricao => TipoConta.GetDescription();

        /// <summary>
        /// Saldo atual ou limite disponível
        /// </summary>
        public decimal Saldo { get; set; } = 0;

        /// <summary>
        /// Indica se está ativa
        /// </summary>
        public bool Ativa { get; set; } = true;

        /// <summary>
        /// Data da última alteração
        /// </summary>
        public DateTime? DtaAlteracao { get; set; }

        /// <summary>
        /// Ícone da conta para exibição na interface
        /// </summary>
        public string? Icone { get; set; }

        /// <summary>
        /// Cor da conta para exibição na interface
        /// </summary>
        public string? Cor { get; set; }

        /// <summary>
        /// Número da agência
        /// </summary>
        public string? Agencia { get; set; }

        /// <summary>
        /// Número da conta
        /// </summary>
        public string? NumeroConta { get; set; }

        /// <summary>
        /// Número do banco
        /// </summary>
        public string? NumeroBanco { get; set; }

        /// <summary>
        /// Indica se é uma conta pessoa jurídica
        /// </summary>
        public bool ContaPj { get; set; } = false;





        // Propriedades específicas para cartões de crédito
        /// <summary>
        /// Últimos 4 dígitos do cartão
        /// </summary>
        public string? UltimosDigitos { get; set; }

        /// <summary>
        /// Data de validade do cartão
        /// </summary>
        public DateTime? DataValidade { get; set; }

        /// <summary>
        /// Dia do fechamento da fatura
        /// </summary>
        public int? DiaFechamento { get; set; }

        /// <summary>
        /// Dia do vencimento da fatura
        /// </summary>
        public int? DiaVencimento { get; set; }

        /// <summary>
        /// Bandeira do cartão
        /// </summary>
        public string? Bandeira { get; set; }

        /// <summary>
        /// Limite total do cartão
        /// </summary>
        public decimal? LimiteTotal { get; set; }

        /// <summary>
        /// Limite utilizado do cartão
        /// </summary>
        public decimal? LimiteUtilizado { get; set; }

        /// <summary>
        /// Verifica se é um cartão de crédito
        /// </summary>
        public bool IsCartaoCredito => TipoConta == AccountType.CartaoCredito;

        /// <summary>
        /// Limite disponível para cartões
        /// </summary>
        public decimal LimiteDisponivel => IsCartaoCredito ? (LimiteTotal ?? 0) - (LimiteUtilizado ?? 0) : 0;

        /// <summary>
        /// Nome de exibição
        /// </summary>
        public string NomeExibicao => !string.IsNullOrEmpty(ApelidoConta) ? ApelidoConta : Nome;

        /// <summary>
        /// Número mascarado do cartão
        /// </summary>
        public string? NumeroMascarado => IsCartaoCredito && !string.IsNullOrEmpty(UltimosDigitos)
            ? $"**** **** **** {UltimosDigitos}"
            : null;
    }
}
