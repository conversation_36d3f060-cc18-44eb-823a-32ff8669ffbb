namespace Shared.ViewModels.Admin
{
    /// <summary>
    /// ViewModel para resultados paginados
    /// </summary>
    /// <typeparam name="T">Tipo dos dados retornados</typeparam>
    public class ResultadoPaginadoViewModel<T>
    {
        /// <summary>
        /// Lista de dados da página atual
        /// </summary>
        public IEnumerable<T> Dados { get; set; } = new List<T>();

        /// <summary>
        /// Página atual
        /// </summary>
        public int PaginaAtual { get; set; }

        /// <summary>
        /// Total de páginas
        /// </summary>
        public int TotalPaginas { get; set; }

        /// <summary>
        /// Total de registros
        /// </summary>
        public int TotalRegistros { get; set; }

        /// <summary>
        /// Quantidade de itens por página
        /// </summary>
        public int ItensPorPagina { get; set; }

        /// <summary>
        /// Indica se há página anterior
        /// </summary>
        public bool TemPaginaAnterior => PaginaAtual > 1;

        /// <summary>
        /// Indica se há próxima página
        /// </summary>
        public bool TemProximaPagina => PaginaAtual < TotalPaginas;

        /// <summary>
        /// Número da primeira página
        /// </summary>
        public int PrimeiraPagina => 1;

        /// <summary>
        /// Número da última página
        /// </summary>
        public int UltimaPagina => TotalPaginas;

        /// <summary>
        /// Construtor
        /// </summary>
        public ResultadoPaginadoViewModel()
        {
        }

        /// <summary>
        /// Construtor com parâmetros
        /// </summary>
        /// <param name="dados">Lista de dados</param>
        /// <param name="totalRegistros">Total de registros</param>
        /// <param name="paginaAtual">Página atual</param>
        /// <param name="itensPorPagina">Itens por página</param>
        public ResultadoPaginadoViewModel(IEnumerable<T> dados, int totalRegistros, int paginaAtual, int itensPorPagina)
        {
            Dados = dados;
            TotalRegistros = totalRegistros;
            PaginaAtual = paginaAtual;
            ItensPorPagina = itensPorPagina;
            TotalPaginas = (int)Math.Ceiling((double)totalRegistros / itensPorPagina);
        }
    }
}
