/// Modelo de dados para conta bancária completa
/// Compatível com a API ContaViewModel do backend
class ContaViewModel {
  final String id;
  final String nome; // Nome da instituição financeira
  final String apelidoConta; // Apelido personalizado
  final String tipoConta; // Tipo da conta
  final String tipoContaDescricao; // Descrição do tipo
  final double saldo;
  final bool ativa;
  final DateTime? dtaCadastro;
  final DateTime? dtaAlteracao;
  final bool flgAtivo;

  // Novos campos adicionados
  final String? icone; // Ícone da conta
  final String? cor; // Cor da conta
  final String? agencia; // Número da agência
  final String? numeroConta; // Número da conta
  final String? numeroBanco; // Número do banco
  final bool contaPj; // Checkbox conta PJ

  // Propriedades específicas para cartões de crédito
  final String? ultimosDigitos;
  final DateTime? dataValidade;
  final int? diaFechamento;
  final int? diaVencimento;
  final String? bandeira;
  final double? limiteTotal;
  final double? limiteUtilizado;

  const ContaViewModel({
    required this.id,
    required this.nome,
    required this.apelidoConta,
    required this.tipoConta,
    required this.tipoContaDescricao,
    required this.saldo,
    required this.ativa,
    this.dtaCadastro,
    this.dtaAlteracao,
    required this.flgAtivo,
    // Novos campos
    this.icone,
    this.cor,
    this.agencia,
    this.numeroConta,
    this.numeroBanco,
    this.contaPj = false,
    // Campos de cartão de crédito
    this.ultimosDigitos,
    this.dataValidade,
    this.diaFechamento,
    this.diaVencimento,
    this.bandeira,
    this.limiteTotal,
    this.limiteUtilizado,
  });

  /// Cria uma instância a partir de JSON da API
  factory ContaViewModel.fromJson(Map<String, dynamic> json) {
    return ContaViewModel(
      id: json['id']?.toString() ?? '',
      nome: json['nome']?.toString() ?? '',
      apelidoConta: json['apelidoConta']?.toString() ?? '',
      tipoConta: json['tipoConta']?.toString() ?? '',
      tipoContaDescricao: json['tipoContaDescricao']?.toString() ?? '',
      saldo: (json['saldo'] as num?)?.toDouble() ?? 0.0,
      ativa: json['ativa'] as bool? ?? true,
      dtaCadastro: json['dtaCadastro'] != null
          ? DateTime.parse(json['dtaCadastro'].toString())
          : null,
      dtaAlteracao: json['dtaAlteracao'] != null
          ? DateTime.parse(json['dtaAlteracao'].toString())
          : null,
      flgAtivo: json['flgAtivo'] as bool? ?? true,
      // Novos campos
      icone: json['icone']?.toString(),
      cor: json['cor']?.toString(),
      agencia: json['agencia']?.toString(),
      numeroConta: json['numeroConta']?.toString(),
      numeroBanco: json['numeroBanco']?.toString(),
      contaPj: json['contaPj'] as bool? ?? false,
      // Campos de cartão de crédito
      ultimosDigitos: json['ultimosDigitos']?.toString(),
      dataValidade: json['dataValidade'] != null
          ? DateTime.parse(json['dataValidade'].toString())
          : null,
      diaFechamento: json['diaFechamento'] as int?,
      diaVencimento: json['diaVencimento'] as int?,
      bandeira: json['bandeira']?.toString(),
      limiteTotal: (json['limiteTotal'] as num?)?.toDouble(),
      limiteUtilizado: (json['limiteUtilizado'] as num?)?.toDouble(),
    );
  }

  /// Converte para JSON para envio à API
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nome': nome,
      'apelidoConta': apelidoConta,
      'tipoConta': tipoConta,
      'saldo': saldo,
      'ativa': ativa,
      'dtaCadastro': dtaCadastro?.toIso8601String(),
      'dtaAlteracao': dtaAlteracao?.toIso8601String(),
      'flgAtivo': flgAtivo,
      // Novos campos
      if (icone != null) 'icone': icone,
      if (cor != null) 'cor': cor,
      if (agencia != null) 'agencia': agencia,
      if (numeroConta != null) 'numeroConta': numeroConta,
      if (numeroBanco != null) 'numeroBanco': numeroBanco,
      'contaPj': contaPj,
      // Campos de cartão de crédito
      if (ultimosDigitos != null) 'ultimosDigitos': ultimosDigitos,
      if (dataValidade != null) 'dataValidade': dataValidade!.toIso8601String(),
      if (diaFechamento != null) 'diaFechamento': diaFechamento,
      if (diaVencimento != null) 'diaVencimento': diaVencimento,
      if (bandeira != null) 'bandeira': bandeira,
      if (limiteTotal != null) 'limiteTotal': limiteTotal,
      if (limiteUtilizado != null) 'limiteUtilizado': limiteUtilizado,
    };
  }

  /// Retorna o nome de exibição (apelido ou nome da instituição)
  String get nomeExibicao => apelidoConta.isNotEmpty ? apelidoConta : nome;

  /// Compatibilidade com código existente
  String get nomeBanco => nome;
  String get apelido => apelidoConta;
  DateTime get dataCriacao => dtaCadastro ?? DateTime.now();
  DateTime? get dataUltimaAtualizacao => dtaAlteracao;

  /// Retorna o saldo formatado
  String get saldoFormatado {
    return 'R\$ ${saldo.toStringAsFixed(2).replaceAll('.', ',')}';
  }

  /// Verifica se é conta corrente
  bool get isContaCorrente => tipoConta.toLowerCase().contains('corrente');

  /// Verifica se é conta poupança
  bool get isContaPoupanca => tipoConta.toLowerCase().contains('poupança');

  /// Verifica se é cartão de crédito
  bool get isCartaoCredito =>
      tipoConta.toLowerCase().contains('cartão') ||
      tipoConta.toLowerCase().contains('credito');

  /// Limite disponível para cartões
  double get limiteDisponivel =>
      isCartaoCredito ? (limiteTotal ?? 0) - (limiteUtilizado ?? 0) : 0;

  /// Cria uma cópia com campos modificados
  ContaViewModel copyWith({
    String? id,
    String? nome,
    String? apelidoConta,
    String? tipoConta,
    String? tipoContaDescricao,
    double? saldo,
    bool? ativa,
    DateTime? dtaCadastro,
    DateTime? dtaAlteracao,
    bool? flgAtivo,
    // Novos campos
    String? icone,
    String? cor,
    String? agencia,
    String? numeroConta,
    String? numeroBanco,
    bool? contaPj,
    // Campos de cartão de crédito
    String? ultimosDigitos,
    DateTime? dataValidade,
    int? diaFechamento,
    int? diaVencimento,
    String? bandeira,
    double? limiteTotal,
    double? limiteUtilizado,
  }) {
    return ContaViewModel(
      id: id ?? this.id,
      nome: nome ?? this.nome,
      apelidoConta: apelidoConta ?? this.apelidoConta,
      tipoConta: tipoConta ?? this.tipoConta,
      tipoContaDescricao: tipoContaDescricao ?? this.tipoContaDescricao,
      saldo: saldo ?? this.saldo,
      ativa: ativa ?? this.ativa,
      dtaCadastro: dtaCadastro ?? this.dtaCadastro,
      dtaAlteracao: dtaAlteracao ?? this.dtaAlteracao,
      flgAtivo: flgAtivo ?? this.flgAtivo,
      // Novos campos
      icone: icone ?? this.icone,
      cor: cor ?? this.cor,
      agencia: agencia ?? this.agencia,
      numeroConta: numeroConta ?? this.numeroConta,
      numeroBanco: numeroBanco ?? this.numeroBanco,
      contaPj: contaPj ?? this.contaPj,
      // Campos de cartão de crédito
      ultimosDigitos: ultimosDigitos ?? this.ultimosDigitos,
      dataValidade: dataValidade ?? this.dataValidade,
      diaFechamento: diaFechamento ?? this.diaFechamento,
      diaVencimento: diaVencimento ?? this.diaVencimento,
      bandeira: bandeira ?? this.bandeira,
      limiteTotal: limiteTotal ?? this.limiteTotal,
      limiteUtilizado: limiteUtilizado ?? this.limiteUtilizado,
    );
  }

  @override
  String toString() {
    return 'AccountModel(id: $id, nome: $nome, apelidoConta: $apelidoConta, tipoConta: $tipoConta, saldo: $saldo, ativa: $ativa)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ContaViewModel &&
        other.id == id &&
        other.nome == nome &&
        other.apelidoConta == apelidoConta &&
        other.tipoConta == tipoConta &&
        other.saldo == saldo &&
        other.ativa == ativa &&
        other.dtaCadastro == dtaCadastro &&
        other.dtaAlteracao == dtaAlteracao;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      nome,
      apelidoConta,
      tipoConta,
      saldo,
      ativa,
      dtaCadastro,
      dtaAlteracao,
    );
  }
}

/// Modelo para criação/edição de conta
class AccountFormModel {
  final String? id;
  final String nome; // Nome da instituição financeira
  final String tipoConta;
  final double saldo; // Saldo inicial ou atual
  final String apelidoConta; // Apelido personalizado

  // Novos campos adicionados
  final String? icone; // Ícone da conta
  final String? cor; // Cor da conta
  final String? agencia; // Número da agência
  final String? numeroConta; // Número da conta
  final String? numeroBanco; // Número do banco
  final bool contaPj; // Checkbox conta PJ

  // Propriedades específicas para cartões de crédito
  final String? ultimosDigitos;
  final DateTime? dataValidade;
  final int? diaFechamento;
  final int? diaVencimento;
  final String? bandeira;
  final double? limiteTotal;

  const AccountFormModel({
    this.id,
    required this.nome,
    required this.tipoConta,
    required this.saldo,
    this.apelidoConta = '',
    // Novos campos
    this.icone,
    this.cor,
    this.agencia,
    this.numeroConta,
    this.numeroBanco,
    this.contaPj = false,
    // Campos de cartão de crédito
    this.ultimosDigitos,
    this.dataValidade,
    this.diaFechamento,
    this.diaVencimento,
    this.bandeira,
    this.limiteTotal,
  });

  /// Converte string do tipo de conta para número do enum
  int _getAccountTypeNumber(String tipoConta) {
    switch (tipoConta.toLowerCase()) {
      case 'conta poupança':
      case 'poupança':
      case 'poupanca':
        return 1; // ContaPoupanca
      case 'conta corrente':
      case 'corrente':
        return 2; // ContaCorrente
      case 'cartão de crédito':
      case 'cartao de credito':
      case 'cartão':
      case 'cartao':
        return 3; // CartaoCredito
      default:
        return 2; // Default: ContaCorrente
    }
  }

  /// Converte para JSON para envio à API
  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'nome': nome,
      'apelidoConta': apelidoConta,
      'tipoConta': _getAccountTypeNumber(tipoConta),
      'saldo': saldo,
      'ativa': true,
      'flgAtivo': true,
      // Novos campos
      if (icone != null) 'icone': icone,
      if (cor != null) 'cor': cor,
      if (agencia != null) 'agencia': agencia,
      if (numeroConta != null) 'numeroConta': numeroConta,
      if (numeroBanco != null) 'numeroBanco': numeroBanco,
      'contaPj': contaPj,
      // Campos de cartão de crédito
      if (ultimosDigitos != null) 'ultimosDigitos': ultimosDigitos,
      if (dataValidade != null) 'dataValidade': dataValidade!.toIso8601String(),
      if (diaFechamento != null) 'diaFechamento': diaFechamento,
      if (diaVencimento != null) 'diaVencimento': diaVencimento,
      if (bandeira != null) 'bandeira': bandeira,
      if (limiteTotal != null) 'limiteTotal': limiteTotal,
    };
  }

  /// Cria uma instância a partir de AccountModel para edição
  factory AccountFormModel.fromAccount(ContaViewModel account) {
    return AccountFormModel(
      id: account.id,
      nome: account.nome,
      tipoConta: account.tipoConta,
      saldo: account.saldo,
      apelidoConta: account.apelidoConta,
      // Novos campos
      icone: account.icone,
      cor: account.cor,
      agencia: account.agencia,
      numeroConta: account.numeroConta,
      numeroBanco: account.numeroBanco,
      contaPj: account.contaPj,
      // Campos de cartão de crédito
      ultimosDigitos: account.ultimosDigitos,
      dataValidade: account.dataValidade,
      diaFechamento: account.diaFechamento,
      diaVencimento: account.diaVencimento,
      bandeira: account.bandeira,
      limiteTotal: account.limiteTotal,
    );
  }

  /// Compatibilidade com código existente
  String get nomeBanco => nome;
  String get apelido => apelidoConta;
  double get saldoInicial => saldo;
}
