using ServiceClient.Interfaces.Generic;
using Shared.Entities.Client;
using Shared.ViewModels.Client;
using System.Linq.Expressions;

namespace ServiceClient.Interfaces
{
    /// <summary>
    /// Interface específica para operações com Cartão, incluindo soft delete e filtros para registros ativos
    /// </summary>
    public interface ICartaoService : IGenericClientService<CartaoViewModel, Cartao>
    {
        /// <summary>
        /// Busca todos os cartões ativos (FlgAtivo = true)
        /// </summary>
        /// <returns>Lista de cartões ativos</returns>
        Task<IEnumerable<CartaoViewModel?>> BuscarTodosAtivosAsync();

        /// <summary>
        /// Busca cartões ativos com filtro personalizado
        /// </summary>
        /// <param name="expression">Expressão de filtro adicional</param>
        /// <returns>Lista de cartões ativos que atendem ao filtro</returns>
        Task<IEnumerable<CartaoViewModel?>> BuscarPorFiltroAtivosAsync(Expression<Func<Cartao?, bool>> expression);

        /// <summary>
        /// Busca cartões ativos com filtro personalizado e paginação
        /// </summary>
        /// <param name="expression">Expressão de filtro adicional</param>
        /// <param name="page">Número da página</param>
        /// <param name="pageSize">Tamanho da página</param>
        /// <returns>Lista paginada de cartões ativos que atendem ao filtro</returns>
        Task<IEnumerable<CartaoViewModel?>> BuscarPorFiltroAtivosPaginadoAsync(Expression<Func<Cartao?, bool>> expression, int page, int pageSize);

        /// <summary>
        /// Inativa um cartão (soft delete) definindo FlgAtivo = false
        /// </summary>
        /// <param name="id">ID do cartão a ser inativado</param>
        /// <returns>True se a inativação foi bem-sucedida</returns>
        Task<bool> InativarAsync(string id);

        /// <summary>
        /// Reativa um cartão definindo FlgAtivo = true
        /// </summary>
        /// <param name="id">ID do cartão a ser reativado</param>
        /// <returns>True se a reativação foi bem-sucedida</returns>
        Task<bool> ReativarAsync(string id);

        /// <summary>
        /// Busca contagem total de cartões ativos
        /// </summary>
        /// <returns>Número total de cartões ativos</returns>
        Task<int> BuscarContagemTotalAtivosAsync();

        /// <summary>
        /// Busca contagem de cartões ativos por filtro
        /// </summary>
        /// <param name="expression">Expressão de filtro</param>
        /// <returns>Número de cartões ativos que atendem ao filtro</returns>
        Task<int> BuscarContagemTotalPorFiltroAtivosAsync(Expression<Func<Cartao?, bool>> expression);
    }
}
