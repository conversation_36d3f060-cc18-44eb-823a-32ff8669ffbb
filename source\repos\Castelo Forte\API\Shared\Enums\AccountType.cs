using System.ComponentModel;

namespace Shared.Enums
{
    /// <summary>
    /// Tipos de conta disponíveis no sistema
    /// </summary>
    public enum AccountType
    {
        [Description("Conta Poupança")]
        ContaPoupanca = 1,

        [Description("Conta Corrente")]
        ContaCorrente = 2,

        [Description("Cartão de Crédito")]
        CartaoCredito = 3
    }

    /// <summary>
    /// Extensões para o enum AccountType
    /// </summary>
    public static class AccountTypeExtensions
    {
        /// <summary>
        /// Retorna a descrição do tipo de conta
        /// </summary>
        public static string GetDescription(this AccountType accountType)
        {
            var field = accountType.GetType().GetField(accountType.ToString());
            var attribute = (DescriptionAttribute?)Attribute.GetCustomAttribute(field!, typeof(DescriptionAttribute));
            return attribute?.Description ?? accountType.ToString();
        }

        /// <summary>
        /// Verifica se é um cartão de crédito
        /// </summary>
        public static bool IsCartaoCredito(this AccountType accountType)
        {
            return accountType == AccountType.CartaoCredito;
        }

        /// <summary>
        /// Verifica se é uma conta bancária (poupança ou corrente)
        /// </summary>
        public static bool IsContaBancaria(this AccountType accountType)
        {
            return accountType == AccountType.ContaPoupanca || accountType == AccountType.ContaCorrente;
        }

        /// <summary>
        /// Converte string para AccountType
        /// </summary>
        public static AccountType FromString(string tipo)
        {
            return tipo.ToLower() switch
            {
                "conta poupança" or "poupança" or "poupanca" => AccountType.ContaPoupanca,
                "conta corrente" or "corrente" => AccountType.ContaCorrente,
                "cartão de crédito" or "cartao de credito" or "cartão" or "cartao" => AccountType.CartaoCredito,
                _ => AccountType.ContaCorrente // Default
            };
        }
    }
}
