using RepositoryClient.Configuration.Interfaces;
using System.Security.Claims;

namespace CasteloForte.Middleware
{
    /// <summary>
    /// Middleware para configurar automaticamente o contexto multi-tenant após a autenticação
    /// </summary>
    public class TenantMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<TenantMiddleware> _logger;

        public TenantMiddleware(RequestDelegate next, ILogger<TenantMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, IContextoMultiTenantService contextoMultiTenant)
        {
            try
            {
                // Verifica se o usuário está autenticado
                if (context.User?.Identity?.IsAuthenticated == true)
                {
                    await ConfigurarContextoSeNecessario(context, contextoMultiTenant);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Erro ao configurar contexto multi-tenant: {Message}", ex.Message);
                // Não bloqueia a requisição, apenas loga o erro
            }

            await _next(context);
        }

        private async Task ConfigurarContextoSeNecessario(HttpContext context, IContextoMultiTenantService contextoMultiTenant)
        {
            try
            {
                // Obtém o ID do usuário do token JWT
                var usuarioId = ObterUsuarioIdDoToken(context);
                
                if (string.IsNullOrEmpty(usuarioId))
                {
                    _logger.LogWarning("UsuarioId não encontrado no token JWT");
                    return;
                }

                // Verifica se o contexto já está configurado
                if (contextoMultiTenant.TemContextoUsuario())
                {
                    _logger.LogDebug("Contexto multi-tenant já configurado para usuário {UsuarioId}", usuarioId);
                    return;
                }

                // Configura o contexto multi-tenant automaticamente
                _logger.LogInformation("Configurando contexto multi-tenant para usuário {UsuarioId}", usuarioId);
                await contextoMultiTenant.ConfigurarContextoUsuarioAsync(usuarioId);
                
                _logger.LogInformation("Contexto multi-tenant configurado com sucesso para usuário {UsuarioId}", usuarioId);
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning("Acesso não autorizado ao configurar contexto: {Message}", ex.Message);
                // Não relança a exceção para não quebrar o fluxo
            }
            catch (ApplicationException ex)
            {
                _logger.LogError(ex, "Erro de aplicação ao configurar contexto multi-tenant: {Message}", ex.Message);
                // Não relança a exceção para não quebrar o fluxo
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro inesperado ao configurar contexto multi-tenant: {Message}", ex.Message);
                // Não relança a exceção para não quebrar o fluxo
            }
        }

        private static string? ObterUsuarioIdDoToken(HttpContext context)
        {
            // Tenta obter o UsuarioId de diferentes claims possíveis
            var claims = new[]
            {
                "UsuarioId",           // Claim principal usado no JWT
                "IdUsuario",           // Claim alternativo para compatibilidade
                ClaimTypes.NameIdentifier, // Claim padrão do ASP.NET Core
                "sub"                  // Claim padrão do JWT (subject)
            };

            foreach (var claimType in claims)
            {
                var claim = context.User.FindFirst(claimType);
                if (claim != null && !string.IsNullOrEmpty(claim.Value))
                {
                    return claim.Value;
                }
            }

            return null;
        }
    }

    /// <summary>
    /// Extensão para registrar o TenantMiddleware
    /// </summary>
    public static class TenantMiddlewareExtensions
    {
        /// <summary>
        /// Adiciona o middleware de configuração automática do contexto multi-tenant
        /// </summary>
        /// <param name="builder">Application builder</param>
        /// <returns>Application builder</returns>
        public static IApplicationBuilder UseTenantMiddleware(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<TenantMiddleware>();
        }
    }
}
