using System.ComponentModel.DataAnnotations;

namespace Shared.ViewModels.Client
{
    /// <summary>
    /// ViewModel para cartão de crédito na dashboard do cliente
    /// DEPRECATED: Use DashboardContaViewModel com TipoConta = CartaoCredito
    /// </summary>
    [Obsolete("Use DashboardContaViewModel com TipoConta = CartaoCredito")]
    public class DashboardCartaoViewModel
    {
        /// <summary>
        /// Identificador único do cartão
        /// </summary>
        [Required]
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Nome do cartão (ex: "Castelo Bank", "Nubank")
        /// </summary>
        [Required]
        [StringLength(100)]
        public string NomeCartao { get; set; } = string.Empty;

        /// <summary>
        /// Últimos 4 dígitos do cartão (ex: "1234")
        /// </summary>
        [Required]
        [StringLength(4, MinimumLength = 4)]
        public string UltimosDigitos { get; set; } = string.Empty;

        /// <summary>
        /// Bandeira do cartão (ex: "VISA", "MASTER")
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Bandeira { get; set; } = string.Empty;

        /// <summary>
        /// Indica se o cartão está ativo
        /// </summary>
        [Required]
        public bool Ativo { get; set; } = true;
    }
}
