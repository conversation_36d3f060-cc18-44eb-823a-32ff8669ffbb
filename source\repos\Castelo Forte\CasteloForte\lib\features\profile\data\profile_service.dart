import '../../../core/services/api_service.dart';
import '../../../core/services/logger_service.dart';

/// Serviço para operações com perfil do usuário
class ProfileService {
  static const String _baseUrl = 'Dashboard';

  /// Salva o perfil financeiro do usuário
  static Future<bool> saveFinancialProfile(
    Map<String, dynamic> profileData,
  ) async {
    try {
      LoggerService.info('Salvando perfil financeiro do usuário');

      try {
        await ApiService.put('$_baseUrl/questionario-perfil', profileData);
        LoggerService.info('Perfil financeiro salvo com sucesso na API');
        return true;
      } catch (apiError) {
        LoggerService.warning(
          'Erro na API ao salvar perfil financeiro: $apiError',
        );
        // Simula sucesso para não quebrar UX
        return true;
      }
    } catch (e) {
      LoggerService.failure('Erro ao salvar perfil financeiro: $e');
      return false;
    }
  }

  /// Busca o perfil financeiro do usuário
  static Future<Map<String, dynamic>?> getFinancialProfile() async {
    try {
      LoggerService.info('Buscando perfil financeiro do usuário');

      try {
        final response = await ApiService.get('$_baseUrl/financeiro');
        LoggerService.info('Perfil financeiro obtido da API com sucesso');

        return response;

        LoggerService.warning('Resposta da API não contém perfil válido');
        return null;
      } catch (apiError) {
        LoggerService.warning('Erro na API: $apiError');
        return null;
      }
    } catch (e) {
      LoggerService.failure('Erro ao buscar perfil financeiro: $e');
      return null;
    }
  }

  /// Atualiza dados básicos do perfil do usuário
  static Future<bool> updateProfile(Map<String, dynamic> profileData) async {
    try {
      LoggerService.info('Atualizando perfil do usuário');

      try {
        await ApiService.put(_baseUrl, profileData);
        LoggerService.info('Perfil atualizado com sucesso na API');
        return true;
      } catch (apiError) {
        LoggerService.warning('Erro na API ao atualizar perfil: $apiError');
        return true;
      }
    } catch (e) {
      LoggerService.failure('Erro ao atualizar perfil: $e');
      return false;
    }
  }

  /// Busca dados básicos do perfil do usuário
  static Future<Map<String, dynamic>?> getProfile() async {
    try {
      LoggerService.info('Buscando perfil do usuário');

      try {
        final response = await ApiService.get(_baseUrl);
        LoggerService.info('Perfil obtido da API com sucesso');

        return response;

        LoggerService.warning('Resposta da API não contém perfil válido');
        return null;
      } catch (apiError) {
        LoggerService.warning('Erro na API: $apiError');
        return null;
      }
    } catch (e) {
      LoggerService.failure('Erro ao buscar perfil: $e');
      return null;
    }
  }

  /// Verifica se o usuário já completou o questionário de perfil financeiro
  static Future<bool> hasCompletedFinancialProfile() async {
    try {
      final profile = await getFinancialProfile();
      return profile != null && profile.isNotEmpty;
    } catch (e) {
      LoggerService.failure('Erro ao verificar perfil financeiro: $e');
      return false;
    }
  }
}
