using RepositoryClient.Interfaces.Generic;
using Shared.Entities.Client;

namespace RepositoryClient.Interfaces
{
    /// <summary>
    /// Interface para repositório de histórico de usuário no contexto Client (multi-tenant)
    /// </summary>
    public interface IHistoricoUsuarioClientRepository : IGenericClientRepository<HistoricoUsuarioClient>
    {
        /// <summary>
        /// Busca histórico por usuário
        /// </summary>
        /// <param name="idUsuario">ID do usuário</param>
        /// <returns>Lista de histórico do usuário</returns>
        Task<IEnumerable<HistoricoUsuarioClient>> BuscarPorUsuarioAsync(string idUsuario);

        /// <summary>
        /// Busca histórico por período
        /// </summary>
        /// <param name="dataInicio">Data de início</param>
        /// <param name="dataFim">Data de fim</param>
        /// <returns>Lista de histórico no período</returns>
        Task<IEnumerable<HistoricoUsuarioClient>> BuscarPorPeriodoAsync(DateTime dataInicio, DateTime dataFim);

        /// <summary>
        /// Busca histórico por tipo de operação
        /// </summary>
        /// <param name="tipoOperacao">Tipo da operação</param>
        /// <returns>Lista de histórico do tipo especificado</returns>
        Task<IEnumerable<HistoricoUsuarioClient>> BuscarPorTipoOperacaoAsync(string tipoOperacao);

        /// <summary>
        /// Busca histórico por controller
        /// </summary>
        /// <param name="controller">Nome do controller</param>
        /// <returns>Lista de histórico do controller</returns>
        Task<IEnumerable<HistoricoUsuarioClient>> BuscarPorControllerAsync(string controller);
    }
}
