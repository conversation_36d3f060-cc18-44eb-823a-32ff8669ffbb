using ServiceClient.Interfaces.Generic;
using Shared.Entities.Client;
using Shared.ViewModels.Client;

namespace ServiceClient.Interfaces
{
    /// <summary>
    /// Interface para serviço de Conta no contexto Client (multi-tenant)
    /// </summary>
    public interface IContaService : IGenericClientService<ContaViewModel, Conta>
    {
        /// <summary>
        /// Busca contas por tipo
        /// </summary>
        /// <param name="tipo">Tipo da conta</param>
        /// <returns>Lista de contas do tipo especificado</returns>
        Task<IEnumerable<ContaViewModel>> BuscarPorTipoAsync(string tipo);

        /// <summary>
        /// Busca contas por nome
        /// </summary>
        /// <param name="nome">Nome da conta</param>
        /// <returns>Lista de contas com o nome especificado</returns>
        Task<IEnumerable<ContaViewModel>> BuscarPorNomeAsync(string nome);

        /// <summary>
        /// Busca contas ativas
        /// </summary>
        /// <returns>Lista de contas ativas</returns>
        Task<IEnumerable<ContaViewModel>> BuscarAtivasAsync();

        /// <summary>
        /// Busca contas com filtros opcionais
        /// </summary>
        /// <param name="search">Filtro de busca por nome ou apelido</param>
        /// <param name="ativa">Filtro por status ativo/inativo (opcional)</param>
        /// <returns>Lista de contas filtradas</returns>
        Task<IEnumerable<ContaViewModel>> BuscarComFiltrosAsync(string? search = null, bool? ativa = null);

        /// <summary>
        /// Calcula o saldo total de todas as contas ativas
        /// </summary>
        /// <returns>Saldo total</returns>
        Task<decimal> CalcularSaldoTotalAsync();

        /// <summary>
        /// Atualiza o saldo de uma conta
        /// </summary>
        /// <param name="idConta">ID da conta</param>
        /// <param name="novoSaldo">Novo saldo</param>
        /// <returns>True se a atualização foi bem-sucedida</returns>
        Task<bool> AtualizarSaldoAsync(string idConta, decimal novoSaldo);

        /// <summary>
        /// Atualiza uma conta existente
        /// </summary>
        /// <param name="conta">Dados atualizados da conta</param>
        /// <returns>Conta atualizada</returns>
        Task<ContaViewModel?> AtualizarAsync(ContaViewModel conta);

        /// <summary>
        /// Inativa uma conta
        /// </summary>
        /// <param name="idConta">ID da conta</param>
        /// <returns>True se a inativação foi bem-sucedida</returns>
        Task<bool> InativarAsync(string idConta);
    }
}
