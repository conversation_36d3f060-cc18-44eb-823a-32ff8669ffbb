import 'package:flutter/material.dart';

/// Modelo de dados para categoria de lançamento
class CategoriaModel {
  final String id;
  final String nome;
  final String icone;
  final String cor;
  final String tipo; // "RECEITA" ou "DESPESA"

  const CategoriaModel({
    required this.id,
    required this.nome,
    required this.icone,
    required this.cor,
    required this.tipo,
  });

  /// Cria uma instância a partir de JSON
  factory CategoriaModel.fromJson(Map<String, dynamic> json) {
    return CategoriaModel(
      id: json['id']?.toString() ?? '',
      nome: json['nome']?.toString() ?? '',
      icone: json['icone']?.toString() ?? 'category',
      cor: json['cor']?.toString() ?? '#FFD700',
      tipo: json['tipo']?.toString() ?? 'DESPESA',
    );
  }

  /// Converte para JSON
  Map<String, dynamic> toJson() {
    return {'id': id, 'nome': nome, 'icone': icone, 'cor': cor, 'tipo': tipo};
  }

  /// Retorna a cor como objeto Color
  Color get colorValue {
    try {
      // Remove o # se presente e adiciona FF para opacidade total
      String colorString = cor.replaceAll('#', '');
      if (colorString.length == 6) {
        colorString = 'FF$colorString';
      }
      return Color(int.parse(colorString, radix: 16));
    } catch (e) {
      // Retorna cor padrão em caso de erro
      return const Color(0xFFFFD700); // Dourado
    }
  }

  /// Retorna o ícone como IconData
  IconData get iconData {
    // Mapeamento de strings para ícones
    switch (icone.toLowerCase()) {
      case 'food':
      case 'alimentacao':
        return Icons.restaurant;
      case 'transport':
      case 'transporte':
        return Icons.directions_car;
      case 'shopping':
      case 'compras':
        return Icons.shopping_cart;
      case 'health':
      case 'saude':
        return Icons.local_hospital;
      case 'education':
      case 'educacao':
        return Icons.school;
      case 'entertainment':
      case 'lazer':
        return Icons.movie;
      case 'salary':
      case 'salario':
        return Icons.work;
      case 'investment':
      case 'investimento':
        return Icons.trending_up;
      case 'home':
      case 'casa':
        return Icons.home;
      case 'bills':
      case 'contas':
        return Icons.receipt;
      default:
        return Icons.category;
    }
  }

  /// Verifica se é uma categoria de receita
  bool get isReceita => tipo.toUpperCase() == 'RECEITA';

  /// Verifica se é uma categoria de despesa
  bool get isDespesa => tipo.toUpperCase() == 'DESPESA';

  /// Cria uma cópia com campos modificados
  CategoriaModel copyWith({
    String? id,
    String? nome,
    String? icone,
    String? cor,
    String? tipo,
  }) {
    return CategoriaModel(
      id: id ?? this.id,
      nome: nome ?? this.nome,
      icone: icone ?? this.icone,
      cor: cor ?? this.cor,
      tipo: tipo ?? this.tipo,
    );
  }

  @override
  String toString() {
    return 'CategoriaModel(id: $id, nome: $nome, icone: $icone, cor: $cor, tipo: $tipo)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CategoriaModel &&
        other.id == id &&
        other.nome == nome &&
        other.icone == icone &&
        other.cor == cor &&
        other.tipo == tipo;
  }

  @override
  int get hashCode {
    return Object.hash(id, nome, icone, cor, tipo);
  }
}
