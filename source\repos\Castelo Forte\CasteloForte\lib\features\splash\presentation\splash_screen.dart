import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../auth/data/auth_service.dart';
import '../../auth/presentation/password_reentry_screen.dart';
import '../../../core/services/storage_service.dart';
import '../../../core/services/secure_storage_service.dart';
import '../../../core/services/app_lifecycle_manager.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/constants.dart';

/// Tela de splash
class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _opacityAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    // Configuração das animações
    _controller = AnimationController(
      vsync: this,
      duration: AppConstants.defaultAnimationDuration,
    );

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeIn));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOutBack));

    // Inicia a animação
    _controller.forward();

    // Inicializa os serviços e navega para a próxima tela
    _initializeAndNavigate();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  /// Inicializa os serviços e navega para a próxima tela
  Future<void> _initializeAndNavigate() async {
    // Garante que a splash screen seja exibida por pelo menos o tempo mínimo
    await Future.wait([
      Future.delayed(AppConstants.splashMinDuration),
      _initializeServices(),
    ]);

    if (mounted) {
      await _handleAuthentication();
    }
  }

  /// Manipula a lógica de autenticação e re-autenticação
  Future<void> _handleAuthentication() async {
    try {
      // Verifica se o usuário está autenticado
      final isAuthenticated = await AuthService.isAuthenticated();

      if (!isAuthenticated) {
        // Usuário não autenticado - vai para login
        if (mounted) {
          context.go(AppConstants.loginRoute);
        }
        return;
      }

      // Verifica se precisa de re-autenticação
      final needsReAuth = await AuthService.needsReAuthentication();

      if (needsReAuth) {
        // Precisa de re-autenticação - obtém informações do usuário
        final userInfo = await AuthService.getReAuthenticationInfo();
        final lastRoute = await SecureStorageService.getLastRoute();

        if (userInfo != null && mounted) {
          // Navega para tela de re-entrada de senha
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => PasswordReentryScreen(
                userInfo: userInfo,
                targetRoute: lastRoute,
              ),
            ),
          );
          return;
        }
      }

      // Usuário autenticado e não precisa de re-autenticação
      if (mounted) {
        context.go(AppConstants.dashboardRoute);
      }
    } catch (e) {
      // Em caso de erro, vai para login
      if (mounted) {
        context.go(AppConstants.loginRoute);
      }
    }
  }

  /// Inicializa os serviços necessários
  Future<void> _initializeServices() async {
    // Inicializa o serviço de armazenamento
    await StorageService.init();

    // Inicializa o gerenciador de ciclo de vida
    await AppLifecycleManager.instance.initialize();

    // Inicializa o serviço de autenticação
    await AuthService.init();
  }

  @override
  Widget build(BuildContext context) {
    // Tamanho do logo
    final logoSize = 340.0;

    return Scaffold(
      backgroundColor: AppTheme.navyBlueColor,
      body: Center(
        child: AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            return Opacity(
              opacity: _opacityAnimation.value,
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: child,
              ),
            );
          },
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo sem fundo amarelo
              ClipOval(
                child: Container(
                  width: logoSize,
                  height: logoSize,
                  color: AppTheme.navyBlueColor, // Mesma cor do fundo
                  child: Image.asset(
                    'assets/images/logo.png',
                    width: logoSize,
                    height: logoSize,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
              const SizedBox(height: 40),
              // Indicador de carregamento estilizado
              SizedBox(
                width: 50,
                height: 50,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppTheme.goldColor),
                  strokeWidth: 3,
                  backgroundColor: AppTheme.navyBlueColor.withValues(
                    alpha: 0.3,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
