import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../theme/app_theme.dart';

/// Widget responsivo para campos de formulário de autenticação
class ResponsiveAuthField extends StatelessWidget {
  final TextEditingController controller;
  final String labelText;
  final String hintText;
  final TextInputType keyboardType;
  final TextInputAction textInputAction;
  final bool obscureText;
  final Widget? suffixIcon;
  final String? Function(String?)? validator;
  final List<TextInputFormatter>? inputFormatters;
  final VoidCallback? onTap;
  final bool readOnly;
  final int? maxLines;
  final FocusNode? focusNode;

  const ResponsiveAuthField({
    super.key,
    required this.controller,
    required this.labelText,
    required this.hintText,
    this.keyboardType = TextInputType.text,
    this.textInputAction = TextInputAction.next,
    this.obscureText = false,
    this.suffixIcon,
    this.validator,
    this.inputFormatters,
    this.onTap,
    this.readOnly = false,
    this.maxLines = 1,
    this.focusNode,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Adapta o padding baseado no tamanho da tela
        final isTablet = constraints.maxWidth > 600;
        final isDesktop = constraints.maxWidth > 1200;

        double horizontalPadding = 0;
        if (isDesktop) {
          horizontalPadding = constraints.maxWidth * 0.1;
        } else if (isTablet) {
          horizontalPadding = constraints.maxWidth * 0.05;
        }

        return Padding(
          padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
          child: TextFormField(
            controller: controller,
            keyboardType: keyboardType,
            textInputAction: textInputAction,
            obscureText: obscureText,
            validator: validator,
            inputFormatters: inputFormatters,
            onTap: onTap,
            readOnly: readOnly,
            maxLines: maxLines,
            focusNode: focusNode,
            style: TextStyle(
              color: AppTheme.snowWhiteColor,
              fontSize: isDesktop ? 16.sp : 14.sp,
            ),
            decoration: InputDecoration(
              labelText: labelText,
              labelStyle: TextStyle(
                color: AppTheme.snowWhiteColor,
                fontSize: isDesktop ? 14.sp : 12.sp,
              ),
              hintText: hintText,
              hintStyle: TextStyle(
                color: AppTheme.snowWhiteColor.withAlpha(179),
                fontSize: isDesktop ? 14.sp : 12.sp,
              ),
              filled: true,
              fillColor: Colors.transparent,
              suffixIcon: suffixIcon,
              contentPadding: EdgeInsets.symmetric(
                horizontal: isDesktop ? 20.w : 16.w,
                vertical: isDesktop ? 20.h : 16.h,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: const BorderSide(
                  color: AppTheme.snowWhiteColor,
                  width: 1,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: const BorderSide(
                  color: AppTheme.snowWhiteColor,
                  width: 1,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: const BorderSide(
                  color: AppTheme.goldColor,
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: const BorderSide(
                  color: AppTheme.errorColor,
                  width: 1,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: const BorderSide(
                  color: AppTheme.errorColor,
                  width: 2,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Widget responsivo para botões de autenticação
class ResponsiveAuthButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isOutlined;
  final Widget? icon;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const ResponsiveAuthButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.isOutlined = false,
    this.icon,
    this.backgroundColor,
    this.foregroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isTablet = constraints.maxWidth > 600;
        final isDesktop = constraints.maxWidth > 1200;

        double horizontalPadding = 0;
        if (isDesktop) {
          horizontalPadding = constraints.maxWidth * 0.1;
        } else if (isTablet) {
          horizontalPadding = constraints.maxWidth * 0.05;
        }

        final buttonHeight = isDesktop
            ? 60.h
            : isTablet
            ? 56.h
            : 52.h;
        final fontSize = isDesktop
            ? 18.sp
            : isTablet
            ? 16.sp
            : 14.sp;

        Widget buttonChild = isLoading
            ? SizedBox(
                width: 20.w,
                height: 20.h,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    isOutlined
                        ? AppTheme.snowWhiteColor
                        : AppTheme.navyBlueColor,
                  ),
                ),
              )
            : Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (icon != null) ...[icon!, SizedBox(width: 8.w)],
                  Text(
                    text,
                    style: TextStyle(
                      fontSize: fontSize,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              );

        return Padding(
          padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
          child: SizedBox(
            width: double.infinity,
            height: buttonHeight,
            child: isOutlined
                ? OutlinedButton(
                    onPressed: isLoading ? null : onPressed,
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(
                        color: AppTheme.snowWhiteColor,
                        width: 1,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      backgroundColor: backgroundColor ?? Colors.transparent,
                      foregroundColor:
                          foregroundColor ?? AppTheme.snowWhiteColor,
                    ),
                    child: buttonChild,
                  )
                : ElevatedButton(
                    onPressed: isLoading ? null : onPressed,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: backgroundColor ?? AppTheme.goldColor,
                      foregroundColor:
                          foregroundColor ?? AppTheme.navyBlueColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      elevation: 0,
                    ),
                    child: buttonChild,
                  ),
          ),
        );
      },
    );
  }
}

/// Widget responsivo para logo da aplicação
class ResponsiveAuthLogo extends StatelessWidget {
  final String? title;
  final String? subtitle;

  const ResponsiveAuthLogo({super.key, this.title, this.subtitle});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isTablet = constraints.maxWidth > 600;
        final isDesktop = constraints.maxWidth > 1200;

        double logoSize = 200;
        if (isDesktop) {
          logoSize = 300;
        } else if (isTablet) {
          logoSize = 250;
        }

        return Column(
          children: [
            // Logo
            Image.asset(
              'assets/images/logo.png',
              width: logoSize.w,
              height: logoSize.h,
              fit: BoxFit.contain,
              semanticLabel: 'Logo do Castelo Forte',
            ),

            if (title != null) ...[
              SizedBox(height: 16.h),
              Text(
                title!,
                style: TextStyle(
                  color: AppTheme.snowWhiteColor,
                  fontSize: isDesktop
                      ? 32.sp
                      : isTablet
                      ? 28.sp
                      : 24.sp,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],

            if (subtitle != null) ...[
              SizedBox(height: 8.h),
              Text(
                subtitle!,
                style: TextStyle(
                  color: AppTheme.snowWhiteColor.withAlpha(204),
                  fontSize: isDesktop
                      ? 18.sp
                      : isTablet
                      ? 16.sp
                      : 14.sp,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        );
      },
    );
  }
}

/// Widget responsivo para container de autenticação
class ResponsiveAuthContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final bool showBackground;

  const ResponsiveAuthContainer({
    super.key,
    required this.child,
    this.padding,
    this.showBackground = true,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isTablet = constraints.maxWidth > 600;
        final isDesktop = constraints.maxWidth > 1200;

        // Calcula o padding responsivo
        double horizontalPadding = 24;
        double verticalPadding = 40;

        if (isDesktop) {
          horizontalPadding = constraints.maxWidth * 0.15;
          verticalPadding = 60;
        } else if (isTablet) {
          horizontalPadding = constraints.maxWidth * 0.1;
          verticalPadding = 50;
        }

        Widget content = Padding(
          padding:
              padding ??
              EdgeInsets.symmetric(
                horizontal: horizontalPadding.w,
                vertical: verticalPadding.h,
              ),
          child: child,
        );

        // Em desktop/tablet, adiciona um container com fundo semi-transparente
        if (showBackground && (isTablet || isDesktop)) {
          content = Center(
            child: Container(
              constraints: BoxConstraints(maxWidth: isDesktop ? 600 : 500),
              margin: EdgeInsets.symmetric(horizontal: 32.w, vertical: 32.h),
              decoration: BoxDecoration(
                color: AppTheme.navyBlueColor.withAlpha(230),
                borderRadius: BorderRadius.circular(24.r),
                border: Border.all(
                  color: AppTheme.goldColor.withAlpha(77),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(51),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: content,
            ),
          );
        }

        return content;
      },
    );
  }
}

/// Widget para divisor com texto
class AuthDivider extends StatelessWidget {
  final String text;

  const AuthDivider({super.key, required this.text});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isTablet = constraints.maxWidth > 600;
        final isDesktop = constraints.maxWidth > 1200;

        double horizontalPadding = 0;
        if (isDesktop) {
          horizontalPadding = constraints.maxWidth * 0.1;
        } else if (isTablet) {
          horizontalPadding = constraints.maxWidth * 0.05;
        }

        return Padding(
          padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
          child: Row(
            children: [
              const Expanded(
                child: Divider(color: AppTheme.snowWhiteColor, thickness: 1),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Text(
                  text,
                  style: TextStyle(
                    color: AppTheme.snowWhiteColor,
                    fontSize: isDesktop
                        ? 16.sp
                        : isTablet
                        ? 14.sp
                        : 12.sp,
                  ),
                ),
              ),
              const Expanded(
                child: Divider(color: AppTheme.snowWhiteColor, thickness: 1),
              ),
            ],
          ),
        );
      },
    );
  }
}

/// Widget para links de navegação
class AuthNavigationLink extends StatelessWidget {
  final String text;
  final String linkText;
  final VoidCallback onPressed;

  const AuthNavigationLink({
    super.key,
    required this.text,
    required this.linkText,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isTablet = constraints.maxWidth > 600;
        final isDesktop = constraints.maxWidth > 1200;

        final fontSize = isDesktop
            ? 16.sp
            : isTablet
            ? 14.sp
            : 12.sp;

        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              text,
              style: TextStyle(
                color: AppTheme.snowWhiteColor,
                fontSize: fontSize,
              ),
            ),
            TextButton(
              onPressed: onPressed,
              style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              child: Text(
                linkText,
                style: TextStyle(
                  color: AppTheme.goldColor,
                  fontWeight: FontWeight.bold,
                  decoration: TextDecoration.underline,
                  decorationColor: AppTheme.goldColor,
                  fontSize: fontSize,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

/// Widget para checkbox responsivo
class ResponsiveAuthCheckbox extends StatelessWidget {
  final bool value;
  final ValueChanged<bool?> onChanged;
  final String text;

  const ResponsiveAuthCheckbox({
    super.key,
    required this.value,
    required this.onChanged,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isTablet = constraints.maxWidth > 600;
        final isDesktop = constraints.maxWidth > 1200;

        double horizontalPadding = 0;
        if (isDesktop) {
          horizontalPadding = constraints.maxWidth * 0.1;
        } else if (isTablet) {
          horizontalPadding = constraints.maxWidth * 0.05;
        }

        final fontSize = isDesktop
            ? 16.sp
            : isTablet
            ? 14.sp
            : 12.sp;

        return Padding(
          padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
          child: Row(
            children: [
              Checkbox(
                value: value,
                onChanged: onChanged,
                checkColor: AppTheme.navyBlueColor,
                fillColor: WidgetStateProperty.resolveWith<Color>((
                  Set<WidgetState> states,
                ) {
                  if (states.contains(WidgetState.disabled)) {
                    return AppTheme.snowWhiteColor.withAlpha(128);
                  }
                  return AppTheme.goldColor;
                }),
              ),
              Expanded(
                child: Text(
                  text,
                  style: TextStyle(
                    color: AppTheme.snowWhiteColor,
                    fontSize: fontSize,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
