using System.ComponentModel.DataAnnotations;

namespace Shared.ViewModels.Client
{
    /// <summary>
    /// ViewModel de resposta principal da dashboard do cliente
    /// Corresponde exatamente ao DashboardResponseModel do Flutter
    /// </summary>
    public class DashboardResponseViewModel
    {
        /// <summary>
        /// Valor total (soma de todos os saldos das contas do usuário)
        /// </summary>
        [Required]
        public decimal ValorTotal { get; set; }

        /// <summary>
        /// Flag que controla se o botão "Começar Questionário do Perfil Financeiro" deve ser exibido
        /// </summary>
        [Required]
        public bool ExibirQuestionarioPerfil { get; set; } = true;

        /// <summary>
        /// Lista de contas bancárias do usuário
        /// </summary>
        [Required]
        public List<DashboardContaViewModel> Contas { get; set; } = new List<DashboardContaViewModel>();

        /// <summary>
        /// Lista de cartões de crédito do usuário
        /// </summary>
        [Required]
        public List<DashboardCartaoViewModel> Cartoes { get; set; } = new List<DashboardCartaoViewModel>();

        /// <summary>
        /// Lista dos últimos lançamentos financeiros
        /// </summary>
        [Required]
        public List<DashboardLancamentoViewModel> UltimosLancamentos { get; set; } = new List<DashboardLancamentoViewModel>();

        /// <summary>
        /// Lista de categorias disponíveis
        /// </summary>
        [Required]
        public List<DashboardCategoriaViewModel> Categorias { get; set; } = new List<DashboardCategoriaViewModel>();

        /// <summary>
        /// Construtor padrão
        /// </summary>
        public DashboardResponseViewModel()
        {
            Contas = new List<DashboardContaViewModel>();
            Cartoes = new List<DashboardCartaoViewModel>();
            UltimosLancamentos = new List<DashboardLancamentoViewModel>();
            Categorias = new List<DashboardCategoriaViewModel>();
        }

        /// <summary>
        /// Construtor com parâmetros
        /// </summary>
        /// <param name="valorTotal">Valor total dos saldos</param>
        /// <param name="exibirQuestionarioPerfil">Flag do questionário de perfil</param>
        /// <param name="contas">Lista de contas</param>
        /// <param name="cartoes">Lista de cartões</param>
        /// <param name="ultimosLancamentos">Lista de lançamentos</param>
        /// <param name="categorias">Lista de categorias</param>
        public DashboardResponseViewModel(
            decimal valorTotal,
            bool exibirQuestionarioPerfil,
            List<DashboardContaViewModel> contas,
            List<DashboardCartaoViewModel> cartoes,
            List<DashboardLancamentoViewModel> ultimosLancamentos,
            List<DashboardCategoriaViewModel> categorias)
        {
            ValorTotal = valorTotal;
            ExibirQuestionarioPerfil = exibirQuestionarioPerfil;
            Contas = contas ?? new List<DashboardContaViewModel>();
            Cartoes = cartoes ?? new List<DashboardCartaoViewModel>();
            UltimosLancamentos = ultimosLancamentos ?? new List<DashboardLancamentoViewModel>();
            Categorias = categorias ?? new List<DashboardCategoriaViewModel>();
        }
    }
}
