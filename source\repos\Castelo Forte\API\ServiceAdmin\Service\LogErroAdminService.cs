﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using RepositoryAdmin.Interfaces;
using RepositoryAdmin.Repositories;
using RepositoryClient.Configuration.Interfaces;
using ServiceAdmin.Interfaces;
using ServiceAdmin.Repository.Generic;
using Shared.Entities.Admin;
using Shared.ViewModels.Admin;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ServiceAdmin.Service
{
    public class LogErroAdminService(
        ILogErroAdminRepository logErroAdminRepository,
        IHttpContextAccessor httpContextAccessor,
        IContextoMultiTenantService contextoMultiTenant,
        IMapper mapper) : GenericAdminService<LogErroAdminViewModel, LogErroAdmin>(logErroAdminRepository, httpContextAccessor, mapper), ILogErroAdminService
    {
        public async Task LogErro(Exception ex, string metodo, string controller, string variaveis)
        {
            try
            {
                LogErroAdmin erro = new LogErroAdmin()
                {
                    DtaCadastro = DateTime.Now,
                    Erro = ex.Message,
                    IdUsuario = IdUsuarioLogado,
                    Metodo = metodo,
                    Controller = controller,
                    Variaveis = variaveis
                };

                await _repository.AdicionarAsync(erro);

            }
            catch (Exception)
            {
                throw;
            }
        }

        /// <summary>
        /// Busca logs de erro por período
        /// </summary>
        /// <param name="dataInicio">Data de início</param>
        /// <param name="dataFim">Data de fim</param>
        /// <returns>Lista de logs de erro no período</returns>
        public async Task<IEnumerable<LogErroAdminViewModel>> BuscarPorPeriodoAsync(DateTime dataInicio, DateTime dataFim)
        {
            try
            {
                var logs = await BuscarTodosAsync();
                return logs.Where(l => l.DtaCadastro >= dataInicio && l.DtaCadastro <= dataFim);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar logs por período: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca logs de erro por usuário
        /// </summary>
        /// <param name="idUsuario">ID do usuário</param>
        /// <returns>Lista de logs de erro do usuário</returns>
        public async Task<IEnumerable<LogErroAdminViewModel>> BuscarPorUsuarioAsync(string idUsuario)
        {
            try
            {
                var logs = await BuscarTodosAsync();
                return logs.Where(l => l.IdUsuarioLogado == idUsuario);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar logs por usuário: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca logs de erro com filtros e paginação
        /// </summary>
        /// <param name="filtro">Filtros para busca</param>
        /// <returns>Resultado paginado de logs de erro</returns>
        public async Task<ResultadoPaginadoViewModel<LogErroAdminViewModel>> BuscarLogsFiltradosAsync(FiltroLogErroViewModel filtro)
        {
            try
            {
                var logs = await BuscarTodosAsync();
                var query = logs.AsQueryable();

                // Aplicar filtros
                if (filtro.DataInicio.HasValue)
                    query = query.Where(l => l.DtaCadastro >= filtro.DataInicio.Value);

                if (filtro.DataFim.HasValue)
                    query = query.Where(l => l.DtaCadastro <= filtro.DataFim.Value);

                if (!string.IsNullOrWhiteSpace(filtro.IdUsuario))
                    query = query.Where(l => l.IdUsuarioLogado == filtro.IdUsuario);

                if (!string.IsNullOrWhiteSpace(filtro.Controller))
                    query = query.Where(l => l.Controller.Contains(filtro.Controller, StringComparison.OrdinalIgnoreCase));

                if (!string.IsNullOrWhiteSpace(filtro.Metodo))
                    query = query.Where(l => l.Metodo.Contains(filtro.Metodo, StringComparison.OrdinalIgnoreCase));

                if (!string.IsNullOrWhiteSpace(filtro.TextoErro))
                    query = query.Where(l => l.Erro.Contains(filtro.TextoErro, StringComparison.OrdinalIgnoreCase));

                // Aplicar ordenação
                query = filtro.OrdenarPor?.ToLower() switch
                {
                    "erro" => filtro.DirecaoOrdenacao?.ToUpper() == "ASC" ?
                        query.OrderBy(l => l.Erro) : query.OrderByDescending(l => l.Erro),
                    "controller" => filtro.DirecaoOrdenacao?.ToUpper() == "ASC" ?
                        query.OrderBy(l => l.Controller) : query.OrderByDescending(l => l.Controller),
                    "metodo" => filtro.DirecaoOrdenacao?.ToUpper() == "ASC" ?
                        query.OrderBy(l => l.Metodo) : query.OrderByDescending(l => l.Metodo),
                    _ => query.OrderByDescending(l => l.DtaCadastro)
                };

                var totalRegistros = query.Count();
                var dadosPaginados = query
                    .Skip((filtro.Pagina - 1) * filtro.ItensPorPagina)
                    .Take(filtro.ItensPorPagina)
                    .ToList();

                return new ResultadoPaginadoViewModel<LogErroAdminViewModel>(
                    dadosPaginados, totalRegistros, filtro.Pagina, filtro.ItensPorPagina);
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar logs filtrados: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Busca logs de erro por controller
        /// </summary>
        /// <param name="controller">Nome do controller</param>
        /// <returns>Lista de logs de erro do controller</returns>
        public async Task<IEnumerable<LogErroAdminViewModel>> BuscarPorControllerAsync(string controller)
        {
            try
            {
                var logs = await BuscarTodosAsync();
                return logs.Where(l => l.Controller.Equals(controller, StringComparison.OrdinalIgnoreCase));
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao buscar logs por controller: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Obtém estatísticas dos logs de erro
        /// </summary>
        /// <returns>Estatísticas dos logs</returns>
        public async Task<object> ObterEstatisticasLogsAsync()
        {
            try
            {
                var logs = await BuscarTodosAsync();
                var totalLogs = logs.Count();

                var hoje = DateTime.Today;
                var logsHoje = logs.Count(l => l.DtaCadastro?.Date == hoje);
                var logsUltimos7Dias = logs.Count(l => l.DtaCadastro >= hoje.AddDays(-7));
                var logsUltimos30Dias = logs.Count(l => l.DtaCadastro >= hoje.AddDays(-30));

                var errosPorController = logs
                    .GroupBy(l => l.Controller)
                    .Select(g => new { Controller = g.Key, Quantidade = g.Count() })
                    .OrderByDescending(x => x.Quantidade)
                    .Take(10)
                    .ToList();

                var errosPorMetodo = logs
                    .GroupBy(l => l.Metodo)
                    .Select(g => new { Metodo = g.Key, Quantidade = g.Count() })
                    .OrderByDescending(x => x.Quantidade)
                    .Take(10)
                    .ToList();

                return new
                {
                    TotalLogs = totalLogs,
                    LogsHoje = logsHoje,
                    LogsUltimos7Dias = logsUltimos7Dias,
                    LogsUltimos30Dias = logsUltimos30Dias,
                    ErrosPorController = errosPorController,
                    ErrosPorMetodo = errosPorMetodo,
                    DataUltimaAtualizacao = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Erro ao obter estatísticas dos logs: {ex.Message}", ex);
            }
        }
    }
}
