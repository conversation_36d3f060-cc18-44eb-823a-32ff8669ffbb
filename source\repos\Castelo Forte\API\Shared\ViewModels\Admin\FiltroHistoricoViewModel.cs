using System.ComponentModel.DataAnnotations;

namespace Shared.ViewModels.Admin
{
    /// <summary>
    /// ViewModel para filtrar histórico de ações no sistema Admin
    /// </summary>
    public class FiltroHistoricoViewModel
    {
        /// <summary>
        /// Data de início para filtro por período
        /// </summary>
        [DataType(DataType.Date)]
        public DateTime? DataInicio { get; set; }

        /// <summary>
        /// Data de fim para filtro por período
        /// </summary>
        [DataType(DataType.Date)]
        public DateTime? DataFim { get; set; }

        /// <summary>
        /// ID do usuário para filtrar histórico específico
        /// </summary>
        public string? IdUsuario { get; set; }

        /// <summary>
        /// Nome do método para filtrar histórico
        /// </summary>
        public string? Metodo { get; set; }

        /// <summary>
        /// Tipo de ação realizada
        /// </summary>
        public string? Acao { get; set; }

        /// <summary>
        /// Texto para busca nas ações
        /// </summary>
        public string? TextoBusca { get; set; }

        /// <summary>
        /// Número da página para paginação
        /// </summary>
        public int Pagina { get; set; } = 1;

        /// <summary>
        /// Quantidade de itens por página
        /// </summary>
        public int ItensPorPagina { get; set; } = 10;

        /// <summary>
        /// Campo para ordenação
        /// </summary>
        public string? OrdenarPor { get; set; } = "DtaCadastro";

        /// <summary>
        /// Direção da ordenação (ASC ou DESC)
        /// </summary>
        public string? DirecaoOrdenacao { get; set; } = "DESC";
    }
}
