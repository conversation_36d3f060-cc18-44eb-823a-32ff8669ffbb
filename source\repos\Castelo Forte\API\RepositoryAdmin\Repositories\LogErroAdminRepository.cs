﻿using RepositoryAdmin.Configuration;
using RepositoryAdmin.Interfaces;
using RepositoryAdmin.Repositories.Generic;
using Shared.Entities.Admin;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RepositoryAdmin.Repositories
{
    public class LogErroAdminRepository(ContextBaseAdmin context) : GenericAdminRepository<LogErroAdmin>(context, context.LogsErrosAdminCollection), ILogErroAdminRepository
    {
    }
}
