using ServiceClient.Interfaces.Generic;
using Shared.Entities.Client;
using Shared.ViewModels.Client;

namespace ServiceClient.Interfaces
{
    /// <summary>
    /// Interface para serviço de logs de erro no contexto Client (multi-tenant)
    /// </summary>
    public interface ILogErroClientService : IGenericClientService<LogErroClientViewModel, LogErroClient>
    {
        /// <summary>
        /// Registra um erro no sistema
        /// </summary>
        /// <param name="ex">Exceção que ocorreu</param>
        /// <param name="metodo">Método onde ocorreu o erro</param>
        /// <param name="controller">Controller onde ocorreu o erro</param>
        /// <param name="variaveis">Variáveis relacionadas ao erro</param>
        /// <param name="informacoesAdicionais">Informações adicionais sobre o contexto</param>
        /// <returns>Task</returns>
        Task LogErro(Exception ex, string metodo, string controller, string variaveis, string? informacoesAdicionais = null);

        /// <summary>
        /// Registra um erro no sistema com informações de contexto HTTP
        /// </summary>
        /// <param name="ex">Exceção que ocorreu</param>
        /// <param name="metodo">Método onde ocorreu o erro</param>
        /// <param name="controller">Controller onde ocorreu o erro</param>
        /// <param name="variaveis">Variáveis relacionadas ao erro</param>
        /// <param name="ipCliente">IP do cliente</param>
        /// <param name="userAgent">User Agent do cliente</param>
        /// <param name="informacoesAdicionais">Informações adicionais sobre o contexto</param>
        /// <returns>Task</returns>
        Task LogErroComContexto(Exception ex, string metodo, string controller, string variaveis, 
            string? ipCliente = null, string? userAgent = null, string? informacoesAdicionais = null);

        /// <summary>
        /// Busca logs de erro por usuário
        /// </summary>
        /// <param name="idUsuario">ID do usuário</param>
        /// <returns>Lista de logs de erro do usuário</returns>
        Task<IEnumerable<LogErroClientViewModel>> BuscarPorUsuarioAsync(string idUsuario);

        /// <summary>
        /// Busca logs de erro por período
        /// </summary>
        /// <param name="dataInicio">Data de início</param>
        /// <param name="dataFim">Data de fim</param>
        /// <returns>Lista de logs de erro no período</returns>
        Task<IEnumerable<LogErroClientViewModel>> BuscarPorPeriodoAsync(DateTime dataInicio, DateTime dataFim);

        /// <summary>
        /// Busca logs de erro por severidade
        /// </summary>
        /// <param name="severidade">Severidade do erro</param>
        /// <returns>Lista de logs de erro com a severidade especificada</returns>
        Task<IEnumerable<LogErroClientViewModel>> BuscarPorSeveridadeAsync(string severidade);
    }
}
