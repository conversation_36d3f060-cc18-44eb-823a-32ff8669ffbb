import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'constants.dart';

/// Helper para navegação entre telas
class NavigationHelper {
  /// Navega para a tela de login
  static void goToLogin(BuildContext context) {
    context.go(AppConstants.loginRoute);
  }

  /// Navega para a tela de cadastro
  static void goToRegister(BuildContext context) {
    context.go(AppConstants.registerRoute);
  }

  /// Navega para a tela de dashboard
  static void goToDashboard(BuildContext context) {
    context.go(AppConstants.dashboardRoute);
  }

  /// Navega para a tela de perfil
  static void goToProfile(BuildContext context) {
    context.go(AppConstants.profileRoute);
  }

  /// Navega para a tela de configurações
  static void goToSettings(BuildContext context) {
    context.go(AppConstants.settingsRoute);
  }

  /// Navega para a tela de segurança
  static void goToSecurity(BuildContext context) {
    context.go(AppConstants.securityRoute);
  }

  /// Navega para a tela de lançamentos
  static void goToTransactions(BuildContext context) {
    context.go(AppConstants.transactionsRoute);
  }

  /// Navega para a tela de relatórios
  static void goToReports(BuildContext context) {
    context.go(AppConstants.reportsRoute);
  }

  /// Navega para a tela de metas
  static void goToGoals(BuildContext context) {
    context.go(AppConstants.goalsRoute);
  }

  /// Navega para a tela de categorias
  static void goToCategories(BuildContext context) {
    context.go(AppConstants.categoriesRoute);
  }

  /// Navega para a tela de contas
  static void goToAccounts(BuildContext context) {
    context.go(AppConstants.accountsRoute);
  }

  /// Navega para a tela de criação/edição de conta
  static void goToAccountForm(BuildContext context, {String? accountId}) {
    if (accountId != null) {
      context.go('${AppConstants.accountFormRoute}?id=$accountId');
    } else {
      context.go(AppConstants.accountFormRoute);
    }
  }

  /// Navega para a tela de cartões
  static void goToCards(BuildContext context) {
    context.go(AppConstants.cardsRoute);
  }

  /// Navega para a tela de criação/edição de cartão
  static void goToCardForm(BuildContext context, {String? cardId}) {
    if (cardId != null) {
      context.go('${AppConstants.cardFormRoute}?id=$cardId');
    } else {
      context.go(AppConstants.cardFormRoute);
    }
  }

  /// Navega para a tela de ajuda
  static void goToHelp(BuildContext context) {
    context.go(AppConstants.helpRoute);
  }

  /// Navega para a tela de notificações
  static void goToNotifications(BuildContext context) {
    context.go(AppConstants.notificationsRoute);
  }

  /// Volta para a tela anterior de forma segura
  static void safeGoBack(BuildContext context) {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else {
      // Se não pode fazer pop, vai para o dashboard
      context.go(AppConstants.dashboardRoute);
    }
  }

  /// Volta para a tela anterior ou vai para uma rota específica
  static void safeGoBackOr(BuildContext context, String fallbackRoute) {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else {
      context.go(fallbackRoute);
    }
  }

  /// Verifica se pode fazer pop
  static bool canGoBack(BuildContext context) {
    return Navigator.canPop(context);
  }

  /// Fecha um dialog ou modal de forma segura
  static void safeCloseDialog(BuildContext context) {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    }
  }
}
