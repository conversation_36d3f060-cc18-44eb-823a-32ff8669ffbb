import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../../features/profile/presentation/financial_profile_questionnaire_screen.dart';

/// Card para exibir saldo disponível
class BalanceCard extends StatelessWidget {
  final double balance;
  final bool showBalance;
  final VoidCallback? onToggleVisibility;

  const BalanceCard({
    super.key,
    required this.balance,
    this.showBalance = true,
    this.onToggleVisibility,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppTheme.navyBlueColor, Color(0xFF003366)],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.navyBlueColor.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Saldo disponível',
                style: TextStyle(color: Colors.white70, fontSize: 16),
              ),
              if (onToggleVisibility != null)
                IconButton(
                  onPressed: onToggleVisibility,
                  icon: Icon(
                    showBalance ? Icons.visibility : Icons.visibility_off,
                    color: Colors.white70,
                    size: 20,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            showBalance
                ? 'R\$ ${balance.toStringAsFixed(2).replaceAll('.', ',')}'
                : 'R\$ ••••••',
            style: const TextStyle(
              color: AppTheme.goldColor,
              fontSize: 32,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

/// Card para perfil financeiro
class FinancialProfileCard extends StatelessWidget {
  const FinancialProfileCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.navyBlueColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.assessment_outlined,
            color: AppTheme.goldColor,
            size: 48,
          ),
          const SizedBox(height: 16),
          const Text(
            'Descubra seu Perfil Financeiro',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          const Text(
            'Responda algumas perguntas para\nentendermos melhor seus objetivos.',
            style: TextStyle(color: Colors.white70, fontSize: 14),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) =>
                        const FinancialProfileQuestionnaireScreen(),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.goldColor,
                foregroundColor: AppTheme.navyBlueColor,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Começar Questionário',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Card para conta bancária
class AccountCard extends StatelessWidget {
  final String bankName;
  final String accountType;
  final double balance;
  final VoidCallback? onTap;

  const AccountCard({
    super.key,
    required this.bankName,
    required this.accountType,
    required this.balance,
    this.onTap,
  });

  // Função para obter ícone do banco
  IconData _getBankIcon(String bankName) {
    switch (bankName.toLowerCase()) {
      case 'nubank':
        return Icons.credit_card;
      case 'banco do brasil':
        return Icons.account_balance;
      case 'itaú':
      case 'itau':
        return Icons.business;
      case 'caixa':
        return Icons.home;
      case 'bradesco':
        return Icons.monetization_on;
      case 'santander':
        return Icons.local_atm;
      default:
        return Icons.account_balance_wallet;
    }
  }

  // Função para obter cor do banco
  Color _getBankColor(String bankName) {
    switch (bankName.toLowerCase()) {
      case 'nubank':
        return const Color(0xFF8A05BE);
      case 'banco do brasil':
        return const Color(0xFFFDD835);
      case 'itaú':
      case 'itau':
        return const Color(0xFFFF6D00);
      case 'caixa':
        return const Color(0xFF0277BD);
      case 'bradesco':
        return const Color(0xFFD32F2F);
      case 'santander':
        return const Color(0xFFE53935);
      default:
        return AppTheme.goldColor;
    }
  }

  @override
  Widget build(BuildContext context) {
    final bankColor = _getBankColor(bankName);
    final bankIcon = _getBankIcon(bankName);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: MediaQuery.of(context).size.width > 600 ? 200 : 160,
        height: 120,
        margin: const EdgeInsets.only(right: 16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme.navyBlueColor,
              AppTheme.navyBlueColor.withValues(alpha: 0.8),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: bankColor.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(color: bankColor.withValues(alpha: 0.4), width: 1),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            children: [
              // Ícone do banco
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: bankColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(bankIcon, color: bankColor, size: 20),
              ),

              // Nome do banco
              Text(
                bankName,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 13,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),

              // Tipo da conta
              Text(
                accountType,
                style: const TextStyle(color: Colors.white70, fontSize: 10),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),

              // Saldo
              Container(
                width: double.infinity,
                alignment: Alignment.center,
                child: Text(
                  'R\$ ${balance.toStringAsFixed(2).replaceAll('.', ',')}',
                  style: TextStyle(
                    color: bankColor,
                    fontSize: MediaQuery.of(context).size.width > 600 ? 14 : 12,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Card para cartão de crédito
class CreditCardCard extends StatelessWidget {
  final String cardName;
  final String lastFourDigits;
  final VoidCallback? onTap;

  const CreditCardCard({
    super.key,
    required this.cardName,
    required this.lastFourDigits,
    this.onTap,
  });

  // Função para obter gradiente do cartão baseado no nome
  List<Color> _getCardGradient(String cardName) {
    switch (cardName.toLowerCase()) {
      case 'castelo bank':
        return [const Color(0xFF1A237E), const Color(0xFF3F51B5)];
      case 'nubank':
        return [const Color(0xFF8A05BE), const Color(0xFFBA68C8)];
      case 'itaú':
      case 'itau':
        return [const Color(0xFFFF6D00), const Color(0xFFFF9800)];
      case 'bradesco':
        return [const Color(0xFFD32F2F), const Color(0xFFE57373)];
      case 'santander':
        return [const Color(0xFFE53935), const Color(0xFFEF5350)];
      default:
        return [AppTheme.navyBlueColor, AppTheme.charcoalGrayColor];
    }
  }

  // Função para obter ícone da bandeira
  Widget _getCardBrandIcon(String cardName) {
    // Simulando diferentes bandeiras baseado no nome
    if (cardName.toLowerCase().contains('castelo')) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(4),
        ),
        child: const Text(
          'VISA',
          style: TextStyle(
            color: Color(0xFF1A1F71),
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    } else {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(4),
        ),
        child: const Text(
          'MASTER',
          style: TextStyle(
            color: Color(0xFFEB001B),
            fontSize: 9,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final cardGradient = _getCardGradient(cardName);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: MediaQuery.of(context).size.width > 600 ? 220 : 180,
        height: 120,
        margin: const EdgeInsets.only(right: 16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: cardGradient,
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: cardGradient[0].withValues(alpha: 0.4),
              blurRadius: 12,
              offset: const Offset(0, 6),
            ),
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header com chip e bandeira
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Chip do cartão
                  Container(
                    width: 24,
                    height: 18,
                    decoration: BoxDecoration(
                      color: Colors.amber.withValues(alpha: 0.8),
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(
                        color: Colors.amber.shade700,
                        width: 1,
                      ),
                    ),
                    child: Container(
                      margin: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: Colors.amber.shade600,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),

                  // Bandeira do cartão
                  _getCardBrandIcon(cardName),
                ],
              ),

              const Spacer(),

              // Número do cartão
              Center(
                child: Text(
                  '**** **** **** $lastFourDigits',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: MediaQuery.of(context).size.width > 600 ? 16 : 14,
                    fontWeight: FontWeight.w600,
                    letterSpacing: MediaQuery.of(context).size.width > 600
                        ? 2
                        : 1,
                    fontFamily: 'monospace',
                  ),
                ),
              ),

              const SizedBox(height: 8),

              // Nome do cartão centralizado
              Center(
                child: Text(
                  cardName.toUpperCase(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 1,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Card de ação para adicionar nova conta
class AddAccountCard extends StatelessWidget {
  final VoidCallback? onTap;

  const AddAccountCard({super.key, this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: MediaQuery.of(context).size.width > 600 ? 200 : 160,
        height: 120,
        margin: const EdgeInsets.only(
          right: 8,
        ), // Margem reduzida para melhor scroll
        decoration: BoxDecoration(
          color: AppTheme.navyBlueColor.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: AppTheme.goldColor.withValues(alpha: 0.6),
            width: 2,
            style: BorderStyle.solid,
          ),
          boxShadow: [
            BoxShadow(
              color: AppTheme.goldColor.withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Ícone de adicionar
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppTheme.goldColor.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(Icons.add, color: AppTheme.goldColor, size: 24),
            ),

            const SizedBox(height: 12),

            // Texto
            const Text(
              'Adicionar\nConta',
              style: TextStyle(
                color: AppTheme.goldColor,
                fontSize: 13,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }
}

/// Card de ação para adicionar novo cartão
class AddCreditCardCard extends StatelessWidget {
  final VoidCallback? onTap;

  const AddCreditCardCard({super.key, this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: MediaQuery.of(context).size.width > 600 ? 220 : 180,
        height: 120,
        margin: const EdgeInsets.only(
          right: 8,
        ), // Margem reduzida para melhor scroll
        decoration: BoxDecoration(
          color: AppTheme.navyBlueColor.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: AppTheme.goldColor.withValues(alpha: 0.6),
            width: 2,
            style: BorderStyle.solid,
          ),
          boxShadow: [
            BoxShadow(
              color: AppTheme.goldColor.withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Ícone de adicionar
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppTheme.goldColor.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.add_card,
                color: AppTheme.goldColor,
                size: 24,
              ),
            ),

            const SizedBox(height: 12),

            // Texto
            const Text(
              'Adicionar\nCartão',
              style: TextStyle(
                color: AppTheme.goldColor,
                fontSize: 13,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }
}

/// Card para lançamento/transação
class TransactionCard extends StatelessWidget {
  final String title;
  final double amount;
  final String category;
  final DateTime date;
  final bool isExpense;
  final VoidCallback? onTap;

  const TransactionCard({
    super.key,
    required this.title,
    required this.amount,
    required this.category,
    required this.date,
    this.isExpense = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          color: AppTheme.navyBlueColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppTheme.goldColor.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: isExpense ? AppTheme.errorColor : AppTheme.successColor,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                isExpense ? Icons.shopping_cart : Icons.trending_up,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    category,
                    style: const TextStyle(color: Colors.white70, fontSize: 12),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${isExpense ? '-' : '+'} R\$ ${amount.toStringAsFixed(2).replaceAll('.', ',')}',
                  style: TextStyle(
                    color: isExpense
                        ? AppTheme.errorColor
                        : AppTheme.successColor,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}',
                  style: const TextStyle(color: Colors.white70, fontSize: 12),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
