{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\Castelo Forte\\API\\Shared\\Shared.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\Castelo Forte\\API\\Shared\\Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\Castelo Forte\\API\\Shared\\Shared.csproj", "projectName": "Shared", "projectPath": "C:\\Users\\<USER>\\source\\repos\\Castelo Forte\\API\\Shared\\Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\Castelo Forte\\API\\Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[14.0.0, )"}, "FFMpegCore": {"target": "Package", "version": "[5.2.0, )"}, "Microsoft.AspNetCore.Http.Features": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.4, )"}, "MongoDB.Bson": {"target": "Package", "version": "[3.2.1, )"}, "MongoDB.Driver": {"target": "Package", "version": "[3.1.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "RestSharp": {"target": "Package", "version": "[106.11.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}