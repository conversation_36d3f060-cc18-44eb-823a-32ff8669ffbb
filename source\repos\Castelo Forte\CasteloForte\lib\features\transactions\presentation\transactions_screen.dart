import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/widgets/app_header_internal.dart';
import '../../../core/widgets/app_footer.dart';
import '../../../core/widgets/dashboard_cards.dart';
import 'transaction_details_screen.dart';

/// Tela de Lançamentos
class TransactionsScreen extends StatelessWidget {
  const TransactionsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.navyBlueColor,
      appBar: const AppHeaderInternal(title: 'Lançamentos'),
      body: Safe<PERSON>rea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Filtros e busca
              _buildFiltersSection(),

              const SizedBox(height: 20),

              // Lista de transações
              _buildTransactionsList(context),
            ],
          ),
        ),
      ),
      bottomNavigationBar: const AppFooter(currentIndex: 1),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.navyBlueColor.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.goldColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Filtros',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(child: _buildFilterChip('Todos', true)),
              const SizedBox(width: 8),
              Expanded(child: _buildFilterChip('Receitas', false)),
              const SizedBox(width: 8),
              Expanded(child: _buildFilterChip('Despesas', false)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, bool isSelected) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: isSelected ? AppTheme.goldColor : Colors.transparent,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isSelected ? AppTheme.goldColor : Colors.white54,
          width: 1,
        ),
      ),
      child: Text(
        label,
        textAlign: TextAlign.center,
        style: TextStyle(
          color: isSelected ? AppTheme.navyBlueColor : Colors.white,
          fontSize: 14,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
    );
  }

  Widget _buildTransactionsList(BuildContext context) {
    final transactions = [
      {
        'title': 'Supermercado Extra',
        'amount': 650.20,
        'category': 'Alimentação',
        'date': DateTime.now().subtract(const Duration(days: 1)),
        'isExpense': true,
      },
      {
        'title': 'Salário',
        'amount': 5000.00,
        'category': 'Salário',
        'date': DateTime.now().subtract(const Duration(days: 2)),
        'isExpense': false,
      },
      {
        'title': 'Farmácia Drogasil',
        'amount': 85.50,
        'category': 'Saúde',
        'date': DateTime.now().subtract(const Duration(days: 3)),
        'isExpense': true,
      },
      {
        'title': 'Posto Shell',
        'amount': 120.00,
        'category': 'Transporte',
        'date': DateTime.now().subtract(const Duration(days: 4)),
        'isExpense': true,
      },
      {
        'title': 'Freelance',
        'amount': 800.00,
        'category': 'Renda Extra',
        'date': DateTime.now().subtract(const Duration(days: 5)),
        'isExpense': false,
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Histórico',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...transactions.map(
          (transaction) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: TransactionCard(
              title: transaction['title'] as String,
              amount: transaction['amount'] as double,
              category: transaction['category'] as String,
              date: transaction['date'] as DateTime,
              isExpense: transaction['isExpense'] as bool,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) =>
                        TransactionDetailsScreen(transaction: transaction),
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
