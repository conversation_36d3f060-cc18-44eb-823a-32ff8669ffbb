import '../../../core/services/api_service.dart';
import '../../../core/services/logger_service.dart';
import 'models/dashboard_response_model.dart';

/// Resultado do carregamento da dashboard
class DashboardLoadResult {
  final DashboardResponseModel? data;
  final bool isFromApi;
  final String? errorMessage;

  const DashboardLoadResult({
    this.data,
    required this.isFromApi,
    this.errorMessage,
  });
}

/// Service para gerenciar dados da dashboard
class DashboardService {
  static const String _baseUrl = 'Dashboard';

  /// Busca todos os dados da dashboard com informação da origem
  static Future<DashboardLoadResult> getDashboardData() async {
    try {
      LoggerService.info('Buscando dados da dashboard...');

      // Tenta fazer chamada real para a API
      try {
        final response = await ApiService.get('$_baseUrl/dados');
        LoggerService.info('Dados da dashboard obtidos da API com sucesso');

        return DashboardLoadResult(
          data: DashboardResponseModel.fromJson(response),
          isFromApi: true,
        );
      } catch (apiError) {
        LoggerService.warning('Erro na API: $apiError');

        // Em caso de erro da API, retorna resultado sem dados
        return DashboardLoadResult(
          data: null,
          isFromApi: false,
          errorMessage:
              'Não foi possível conectar ao servidor. Verifique sua conexão com a internet.',
        );
      }
    } catch (e) {
      LoggerService.failure('Erro ao buscar dados da dashboard: $e');

      // Em caso de erro geral, retorna resultado sem dados
      return DashboardLoadResult(
        data: null,
        isFromApi: false,
        errorMessage: 'Erro ao carregar dados. Tente novamente mais tarde.',
      );
    }
  }

  /// Atualiza a flag do questionário de perfil financeiro
  static Future<bool> updateQuestionarioPerfil(bool exibir) async {
    try {
      LoggerService.info('Atualizando flag do questionário de perfil: $exibir');

      // Tenta fazer chamada real para a API
      try {
        final response = await ApiService.put('$_baseUrl/questionario-perfil', {
          'exibirQuestionarioPerfil': exibir,
        });
        LoggerService.info(
          'Flag do questionário de perfil atualizada com sucesso',
        );
        return response['success'] == true;
      } catch (apiError) {
        LoggerService.warning(
          'Erro na API ao atualizar questionário: $apiError',
        );
        // Em caso de erro da API, simula sucesso para não quebrar a aplicação
        return true;
      }
    } catch (e) {
      LoggerService.failure('Erro ao atualizar questionário de perfil: $e');
      return false;
    }
  }

  /// Recarrega os dados da dashboard
  static Future<DashboardLoadResult> reloadDashboardData() async {
    LoggerService.info('Recarregando dados da dashboard...');
    return getDashboardData();
  }
}
