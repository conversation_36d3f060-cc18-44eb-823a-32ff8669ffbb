// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:casteloforte/core/theme/app_theme.dart';
import 'package:casteloforte/core/utils/constants.dart';

void main() {
  group('Castelo Forte App Tests', () {
    testWidgets('App theme configuration test', (WidgetTester tester) async {
      // Test that themes are properly configured
      expect(AppTheme.lightTheme, isA<ThemeData>());
      expect(AppTheme.darkTheme, isA<ThemeData>());
      expect(AppTheme.primaryColor, equals(const Color(0xFF2E7D32)));
    });

    testWidgets('App constants test', (WidgetTester tester) async {
      // Test that constants are properly defined
      expect(AppConstants.appName, equals('Castelo Forte'));
      expect(AppConstants.appDescription, equals('Gestão financeira pessoal'));
      expect(AppConstants.splashRoute, equals('/'));
      expect(AppConstants.loginRoute, equals('/login'));
      expect(AppConstants.dashboardRoute, equals('/dashboard'));
    });

    testWidgets('Basic widget creation test', (WidgetTester tester) async {
      // Test basic widget creation without timers
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(body: Center(child: Text(AppConstants.appName))),
        ),
      );

      expect(find.text('Castelo Forte'), findsOneWidget);
    });
  });
}
