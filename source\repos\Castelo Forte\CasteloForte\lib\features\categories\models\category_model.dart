import 'package:flutter/material.dart';

/// Modelo de dados para categoria
class CategoryModel {
  final String id;
  final String nome;
  final String descricao;
  final String tipo; // "RECEITA" ou "DESPESA"
  final int cor; // Cor em formato ARGB32
  final int icone; // Código do ícone
  final bool ativa;
  final DateTime dataCriacao;
  final DateTime? dataAlteracao;
  final int ordem;
  final String? idCategoriaPai;
  final String? nomeCategoriaPai;
  final double? limiteGastos;
  final String? periodoLimite;
  final String? observacoes;
  final int numeroTransacoes;
  final int numeroMetas;
  final double valorGasto;

  const CategoryModel({
    required this.id,
    required this.nome,
    required this.descricao,
    required this.tipo,
    required this.cor,
    required this.icone,
    required this.ativa,
    required this.dataCriacao,
    this.dataAlteracao,
    required this.ordem,
    this.idCategoriaPai,
    this.nomeCategoriaPai,
    this.limiteGastos,
    this.periodoLimite,
    this.observacoes,
    this.numeroTransacoes = 0,
    this.numeroMetas = 0,
    this.valorGasto = 0.0,
  });

  /// Cria uma instância a partir de JSON
  factory CategoryModel.fromJson(Map<String, dynamic> json) {
    return CategoryModel(
      id: json['id']?.toString() ?? '',
      nome: json['nome']?.toString() ?? '',
      descricao: json['descricao']?.toString() ?? '',
      tipo: json['tipo']?.toString() ?? 'DESPESA',
      cor: json['cor'] as int? ?? 0xFF4CAF50,
      icone: json['icone'] as int? ?? 0xE7FD,
      ativa: json['ativa'] as bool? ?? true,
      dataCriacao: json['dataCriacao'] != null 
          ? DateTime.parse(json['dataCriacao'].toString())
          : DateTime.now(),
      dataAlteracao: json['dataAlteracao'] != null 
          ? DateTime.parse(json['dataAlteracao'].toString())
          : null,
      ordem: json['ordem'] as int? ?? 0,
      idCategoriaPai: json['idCategoriaPai']?.toString(),
      nomeCategoriaPai: json['nomeCategoriaPai']?.toString(),
      limiteGastos: json['limiteGastos'] != null 
          ? (json['limiteGastos'] as num).toDouble()
          : null,
      periodoLimite: json['periodoLimite']?.toString(),
      observacoes: json['observacoes']?.toString(),
      numeroTransacoes: json['numeroTransacoes'] as int? ?? 0,
      numeroMetas: json['numeroMetas'] as int? ?? 0,
      valorGasto: json['valorGasto'] != null 
          ? (json['valorGasto'] as num).toDouble()
          : 0.0,
    );
  }

  /// Converte para JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nome': nome,
      'descricao': descricao,
      'tipo': tipo,
      'cor': cor,
      'icone': icone,
      'ativa': ativa,
      'dataCriacao': dataCriacao.toIso8601String(),
      'dataAlteracao': dataAlteracao?.toIso8601String(),
      'ordem': ordem,
      'idCategoriaPai': idCategoriaPai,
      'nomeCategoriaPai': nomeCategoriaPai,
      'limiteGastos': limiteGastos,
      'periodoLimite': periodoLimite,
      'observacoes': observacoes,
      'numeroTransacoes': numeroTransacoes,
      'numeroMetas': numeroMetas,
      'valorGasto': valorGasto,
    };
  }

  /// Retorna a cor como objeto Color
  Color get colorValue => Color(cor);

  /// Retorna o ícone como IconData
  IconData get iconData => IconData(icone, fontFamily: 'MaterialIcons');

  /// Verifica se é uma categoria de receita
  bool get isReceita => tipo.toUpperCase() == 'RECEITA';

  /// Verifica se é uma categoria de despesa
  bool get isDespesa => tipo.toUpperCase() == 'DESPESA';

  /// Retorna o tipo formatado
  String get tipoFormatado => isReceita ? 'Receita' : 'Despesa';

  /// Retorna a data de criação formatada
  String get dataCriacaoFormatada {
    return '${dataCriacao.day.toString().padLeft(2, '0')}/'
           '${dataCriacao.month.toString().padLeft(2, '0')}/'
           '${dataCriacao.year}';
  }

  /// Retorna a data de alteração formatada
  String? get dataAlteracaoFormatada {
    if (dataAlteracao == null) return null;
    return '${dataAlteracao!.day.toString().padLeft(2, '0')}/'
           '${dataAlteracao!.month.toString().padLeft(2, '0')}/'
           '${dataAlteracao!.year}';
  }

  /// Retorna o limite de gastos formatado
  String? get limiteGastosFormatado {
    if (limiteGastos == null) return null;
    return 'R\$ ${limiteGastos!.toStringAsFixed(2).replaceAll('.', ',')}';
  }

  /// Retorna o valor gasto formatado
  String get valorGastoFormatado {
    return 'R\$ ${valorGasto.toStringAsFixed(2).replaceAll('.', ',')}';
  }

  /// Retorna o percentual do limite utilizado
  double? get percentualLimite {
    if (limiteGastos == null || limiteGastos! <= 0) return null;
    return (valorGasto / limiteGastos!) * 100;
  }

  /// Indica se o limite foi ultrapassado
  bool get limiteUltrapassado {
    return limiteGastos != null && valorGasto > limiteGastos!;
  }

  /// Retorna o status do limite
  String get statusLimite {
    if (limiteGastos == null) return 'Sem limite';
    if (limiteUltrapassado) return 'Limite ultrapassado';
    
    final percentual = percentualLimite!;
    if (percentual >= 90) return 'Próximo do limite';
    if (percentual >= 70) return 'Atenção';
    return 'Dentro do limite';
  }

  /// Retorna a cor do status do limite
  Color get corStatusLimite {
    if (limiteGastos == null) return Colors.grey;
    if (limiteUltrapassado) return Colors.red;
    
    final percentual = percentualLimite!;
    if (percentual >= 90) return Colors.orange;
    if (percentual >= 70) return Colors.yellow;
    return Colors.green;
  }

  /// Cria uma cópia com campos modificados
  CategoryModel copyWith({
    String? id,
    String? nome,
    String? descricao,
    String? tipo,
    int? cor,
    int? icone,
    bool? ativa,
    DateTime? dataCriacao,
    DateTime? dataAlteracao,
    int? ordem,
    String? idCategoriaPai,
    String? nomeCategoriaPai,
    double? limiteGastos,
    String? periodoLimite,
    String? observacoes,
    int? numeroTransacoes,
    int? numeroMetas,
    double? valorGasto,
  }) {
    return CategoryModel(
      id: id ?? this.id,
      nome: nome ?? this.nome,
      descricao: descricao ?? this.descricao,
      tipo: tipo ?? this.tipo,
      cor: cor ?? this.cor,
      icone: icone ?? this.icone,
      ativa: ativa ?? this.ativa,
      dataCriacao: dataCriacao ?? this.dataCriacao,
      dataAlteracao: dataAlteracao ?? this.dataAlteracao,
      ordem: ordem ?? this.ordem,
      idCategoriaPai: idCategoriaPai ?? this.idCategoriaPai,
      nomeCategoriaPai: nomeCategoriaPai ?? this.nomeCategoriaPai,
      limiteGastos: limiteGastos ?? this.limiteGastos,
      periodoLimite: periodoLimite ?? this.periodoLimite,
      observacoes: observacoes ?? this.observacoes,
      numeroTransacoes: numeroTransacoes ?? this.numeroTransacoes,
      numeroMetas: numeroMetas ?? this.numeroMetas,
      valorGasto: valorGasto ?? this.valorGasto,
    );
  }

  @override
  String toString() {
    return 'CategoryModel(id: $id, nome: $nome, tipo: $tipo, ativa: $ativa)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CategoryModel &&
        other.id == id &&
        other.nome == nome &&
        other.tipo == tipo &&
        other.cor == cor &&
        other.icone == icone;
  }

  @override
  int get hashCode {
    return Object.hash(id, nome, tipo, cor, icone);
  }
}
