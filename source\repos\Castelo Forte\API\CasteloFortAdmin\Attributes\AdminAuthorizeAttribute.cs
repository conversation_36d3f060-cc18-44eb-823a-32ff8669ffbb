using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using ServiceAdmin.Interfaces;
using System.Security.Claims;

namespace CasteloForteAdmin.Attributes
{
    /// <summary>
    /// Atributo de autorização que valida se o usuário logado tem privilégios de administrador
    /// </summary>
    public class AdminAuthorizeAttribute : Attribute, IAsyncAuthorizationFilter
    {
        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            // Verifica se o usuário está autenticado
            if (!context.HttpContext.User.Identity?.IsAuthenticated ?? true)
            {
                context.Result = new UnauthorizedObjectResult(new
                {
                    success = false,
                    message = "Usuário não autenticado",
                    timestamp = DateTimeOffset.UtcNow
                });
                return;
            }

            try
            {
                // Obtém o ID do usuário do token
                var usuarioId = context.HttpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                
                if (string.IsNullOrEmpty(usuarioId))
                {
                    context.Result = new UnauthorizedObjectResult(new
                    {
                        success = false,
                        message = "ID do usuário não encontrado no token",
                        timestamp = DateTimeOffset.UtcNow
                    });
                    return;
                }

                // Obtém o serviço de usuário do container de DI
                var usuarioService = context.HttpContext.RequestServices.GetService<IUsuarioService>();
                
                if (usuarioService == null)
                {
                    context.Result = new ObjectResult(new
                    {
                        success = false,
                        message = "Erro interno: Serviço de usuário não disponível",
                        timestamp = DateTimeOffset.UtcNow
                    })
                    {
                        StatusCode = 500
                    };
                    return;
                }

                // Busca o usuário no banco de dados
                var usuario = await usuarioService.BuscarPorIdAsync(usuarioId);
                
                if (usuario == null)
                {
                    context.Result = new UnauthorizedObjectResult(new
                    {
                        success = false,
                        message = "Usuário não encontrado",
                        timestamp = DateTimeOffset.UtcNow
                    });
                    return;
                }

                // Verifica se o usuário está ativo
                if (!usuario.FlgAtivo)
                {
                    context.Result = new UnauthorizedObjectResult(new
                    {
                        success = false,
                        message = "Usuário inativo",
                        timestamp = DateTimeOffset.UtcNow
                    });
                    return;
                }

                // Verifica se o usuário tem privilégios de administrador
                if (!usuario.FlgAdministrador)
                {
                    context.Result = new ObjectResult(new
                    {
                        success = false,
                        message = "Acesso negado: Privilégios de administrador necessários",
                        timestamp = DateTimeOffset.UtcNow
                    })
                    {
                        StatusCode = 403 // Forbidden
                    };
                    return;
                }

                // Se chegou até aqui, o usuário é um administrador válido
                // Adiciona informações do usuário no contexto para uso posterior
                context.HttpContext.Items["UsuarioAdmin"] = usuario;
                context.HttpContext.Items["IsAdmin"] = true;
            }
            catch (Exception ex)
            {
                // Log do erro (se necessário)
                var logger = context.HttpContext.RequestServices.GetService<ILogger<AdminAuthorizeAttribute>>();
                logger?.LogError(ex, "Erro ao validar privilégios de administrador para usuário");

                context.Result = new ObjectResult(new
                {
                    success = false,
                    message = "Erro interno ao validar privilégios de administrador",
                    timestamp = DateTimeOffset.UtcNow
                })
                {
                    StatusCode = 500
                };
            }
        }
    }

    /// <summary>
    /// Extensões para facilitar o uso do atributo AdminAuthorize
    /// </summary>
    public static class AdminAuthorizeExtensions
    {
        /// <summary>
        /// Obtém o usuário administrador do contexto HTTP
        /// </summary>
        /// <param name="httpContext">Contexto HTTP</param>
        /// <returns>UsuarioViewModel do administrador ou null se não encontrado</returns>
        public static Shared.ViewModels.Admin.UsuarioViewModel? GetUsuarioAdmin(this HttpContext httpContext)
        {
            return httpContext.Items["UsuarioAdmin"] as Shared.ViewModels.Admin.UsuarioViewModel;
        }

        /// <summary>
        /// Verifica se o usuário atual é administrador
        /// </summary>
        /// <param name="httpContext">Contexto HTTP</param>
        /// <returns>True se for administrador, false caso contrário</returns>
        public static bool IsAdmin(this HttpContext httpContext)
        {
            return httpContext.Items["IsAdmin"] as bool? ?? false;
        }
    }
}
