{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "JwtSettings": {"Secret": "fedaf7d8863b48e197b9287d492b708e", "ExpiracaoHoras": 8, "Emissor": "<PERSON><PERSON>Fort<PERSON>", "Audiencia": "https://CasteloForte.com.br"}, "MongoDBSettings": {"ConnectionString": "mongodb+srv://ti:<EMAIL>/?retryWrites=true&w=majority&maxPoolSize=50&wtimeoutMS=2500&connectTimeoutMS=300000&socketTimeoutMS=300000&serverSelectionTimeoutMS=300000&heartbeatFrequencyMS=10000&maxIdleTimeMS=120000", "DatabaseNameAdmin": "<PERSON><PERSON>_Forte"}, "AzureStorage": {"AccountName": "x9tech", "AccountKey": "****************************************************************************************"}, "SmtpSettings": {"Server": "smtp.hostinger.com", "Port": 587, "Username": "<EMAIL>", "Password": "Curutuba@2024+++", "FromEmail": "<EMAIL>"}, "Criptografia": {"Chave": "X9TechSecretKey2024!@#$%^&*()12"}, "CodeChatWhatsApp": {"EndPoint": "http://*************:8084", "ApiKey": "z1Yz2P73oc4s-t55x44h637S8-s3c4e5fe6w46-FZTCu4ehnM8v4hu", "Instance": "X9Tech-8c6fca77-7b93-4d2a-86f1-7b9d225fab7c"}}