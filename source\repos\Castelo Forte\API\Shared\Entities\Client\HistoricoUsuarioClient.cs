using MongoDB.Bson.Serialization.Attributes;
using Shared.Entities.Base;

namespace Shared.Entities.Client
{
    /// <summary>
    /// Entidade para armazenar histórico de ações do usuário no contexto Client (multi-tenant)
    /// </summary>
    public class HistoricoUsuarioClient : BaseEntidade
    {
        /// <summary>
        /// Método que executou a ação
        /// </summary>
        public string Metodo { get; set; } = "";

        /// <summary>
        /// Descrição da ação realizada
        /// </summary>
        public string Acao { get; set; } = "";

        /// <summary>
        /// Dados antes da alteração (JSON serializado)
        /// </summary>
        public string DadoAntigo { get; set; } = "";

        /// <summary>
        /// Dados após a alteração (JSON serializado)
        /// </summary>
        public string DadoNovo { get; set; } = "";

        /// <summary>
        /// ID do usuário que executou a ação
        /// </summary>
        public string? IdUsuario { get; set; }

        /// <summary>
        /// Controller onde a ação foi executada
        /// </summary>
        public string Controller { get; set; } = "";

        /// <summary>
        /// Tipo da operação (CREATE, READ, UPDATE, DELETE, LOGIN, LOGOUT, etc.)
        /// </summary>
        public string TipoOperacao { get; set; } = "";

        /// <summary>
        /// IP do cliente que executou a ação
        /// </summary>
        public string? IpCliente { get; set; }

        /// <summary>
        /// User Agent do cliente
        /// </summary>
        public string? UserAgent { get; set; }

        /// <summary>
        /// Informações adicionais sobre a ação
        /// </summary>
        public string? InformacoesAdicionais { get; set; }

        /// <summary>
        /// Duração da operação em milissegundos
        /// </summary>
        public long? DuracaoMs { get; set; }

        /// <summary>
        /// Status da operação (SUCCESS, ERROR, WARNING)
        /// </summary>
        public string Status { get; set; } = "SUCCESS";
    }
}
