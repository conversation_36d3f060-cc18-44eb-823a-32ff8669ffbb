﻿using MongoDB.Bson.Serialization.Attributes;
using Shared.Entities.Base;
using Shared.Enums;
using System;
using System.ComponentModel.DataAnnotations;

namespace Shared.Entities.Client
{
    /// <summary>
    /// Entidade que representa um modelo de transferência recorrente
    /// </summary>
    public class TransferenciaRecorrente : BaseEntidade
    {
        /// <summary>
        /// Nome/título da transferência recorrente
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Nome { get; set; } = "";

        /// <summary>
        /// Descrição da transferência recorrente
        /// </summary>
        [StringLength(500)]
        public string Descricao { get; set; } = "";

        #region Dados da Transação Modelo
        /// <summary>
        /// Valor da transferência
        /// </summary>
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "O valor deve ser maior que zero")]
        public decimal Valor { get; set; } = 0;

        /// <summary>
        /// ID da conta de origem
        /// </summary>
        [Required]
        public string IdContaOrigem { get; set; } = "";

        /// <summary>
        /// ID da conta de destino (opcional)
        /// </summary>
        public string? IdContaDestino { get; set; }

        /// <summary>
        /// ID da categoria
        /// </summary>
        [Required]
        public string IdCategoria { get; set; } = "";

        /// <summary>
        /// Tipo de transação (RECEITA/DESPESA)
        /// </summary>
        [Required]
        public string Tipo { get; set; } = "DESPESA";

        /// <summary>
        /// Forma de pagamento
        /// </summary>
        public string FormaPagamento { get; set; } = "";

        /// <summary>
        /// Observações padrão
        /// </summary>
        public string? ObservacoesModelo { get; set; }
        #endregion

        #region Configuração de Recorrência
        /// <summary>
        /// Tipo de recorrência
        /// </summary>
        public RecorrenciaType TipoRecorrencia { get; set; } = RecorrenciaType.Mensal;

        /// <summary>
        /// Dias personalizados (para recorrência personalizada)
        /// </summary>
        public int? DiasPersonalizados { get; set; }

        /// <summary>
        /// Dia do mês para execução (1-31, para recorrências mensais ou maiores)
        /// </summary>
        [Range(1, 31)]
        public int? DiaExecucao { get; set; }

        /// <summary>
        /// Data de início da recorrência
        /// </summary>
        [Required]
        [DataType(DataType.Date)]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime DataInicio { get; set; } = DateTime.Now;

        /// <summary>
        /// Data de fim da recorrência (opcional)
        /// </summary>
        [DataType(DataType.Date)]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime? DataFim { get; set; }

        /// <summary>
        /// Próxima data de execução
        /// </summary>
        [DataType(DataType.Date)]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime ProximaExecucao { get; set; }

        /// <summary>
        /// Última data de execução
        /// </summary>
        [DataType(DataType.Date)]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime? UltimaExecucao { get; set; }
        #endregion

        #region Status e Controle
        /// <summary>
        /// Status da recorrência
        /// </summary>
        public RecorrenciaStatus Status { get; set; } = RecorrenciaStatus.Ativa;

        /// <summary>
        /// Número máximo de execuções (opcional)
        /// </summary>
        public int? MaximoExecucoes { get; set; }

        /// <summary>
        /// Contador de execuções realizadas
        /// </summary>
        public int ExecucoesRealizadas { get; set; } = 0;

        /// <summary>
        /// Contador de execuções falhadas
        /// </summary>
        public int ExecucoesFalhadas { get; set; } = 0;

        /// <summary>
        /// Data da última tentativa de execução
        /// </summary>
        [DataType(DataType.DateTime)]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime? UltimaTentativa { get; set; }

        /// <summary>
        /// Motivo da última falha (se houver)
        /// </summary>
        public string? MotivoUltimaFalha { get; set; }
        #endregion

        #region Propriedades Calculadas
        /// <summary>
        /// Verifica se a recorrência está ativa
        /// </summary>
        public bool IsAtiva => Status == RecorrenciaStatus.Ativa;

        /// <summary>
        /// Verifica se deve executar hoje
        /// </summary>
        public bool DeveExecutarHoje => IsAtiva && ProximaExecucao.Date <= DateTime.Now.Date;

        /// <summary>
        /// Verifica se atingiu o limite de execuções
        /// </summary>
        public bool AtingiuLimiteExecucoes => MaximoExecucoes.HasValue && ExecucoesRealizadas >= MaximoExecucoes.Value;

        /// <summary>
        /// Verifica se está vencida (passou da data fim)
        /// </summary>
        public bool IsVencida => DataFim.HasValue && DateTime.Now > DataFim.Value;

        /// <summary>
        /// Verifica se pode executar
        /// </summary>
        public bool PodeExecutar => IsAtiva && !AtingiuLimiteExecucoes && !IsVencida;
        #endregion

        #region Métodos de Negócio
        /// <summary>
        /// Calcula a próxima data de execução
        /// </summary>
        public void CalcularProximaExecucao()
        {
            if (UltimaExecucao.HasValue)
            {
                ProximaExecucao = TipoRecorrencia.CalcularProximaExecucao(UltimaExecucao.Value, DiasPersonalizados);
            }
            else
            {
                ProximaExecucao = TipoRecorrencia.CalcularProximaExecucao(DataInicio, DiasPersonalizados);
            }

            // Ajusta para o dia específico se configurado
            if (DiaExecucao.HasValue && (TipoRecorrencia == RecorrenciaType.Mensal ||
                                        TipoRecorrencia == RecorrenciaType.Bimestral ||
                                        TipoRecorrencia == RecorrenciaType.Trimestral ||
                                        TipoRecorrencia == RecorrenciaType.Semestral ||
                                        TipoRecorrencia == RecorrenciaType.Anual))
            {
                var diasNoMes = DateTime.DaysInMonth(ProximaExecucao.Year, ProximaExecucao.Month);
                var diaAjustado = Math.Min(DiaExecucao.Value, diasNoMes);
                ProximaExecucao = new DateTime(ProximaExecucao.Year, ProximaExecucao.Month, diaAjustado);
            }
        }

        /// <summary>
        /// Registra uma execução bem-sucedida
        /// </summary>
        public void RegistrarExecucaoSucesso()
        {
            ExecucoesRealizadas++;
            UltimaExecucao = DateTime.Now;
            UltimaTentativa = DateTime.Now;
            MotivoUltimaFalha = null;

            CalcularProximaExecucao();

            // Verifica se deve finalizar
            if (AtingiuLimiteExecucoes || IsVencida)
            {
                Status = RecorrenciaStatus.Concluida;
            }
        }

        /// <summary>
        /// Registra uma execução falhada
        /// </summary>
        /// <param name="motivo">Motivo da falha</param>
        public void RegistrarExecucaoFalha(string motivo)
        {
            ExecucoesFalhadas++;
            UltimaTentativa = DateTime.Now;
            MotivoUltimaFalha = motivo;

            // Se falhou muitas vezes, pausa a recorrência
            if (ExecucoesFalhadas >= 5)
            {
                Status = RecorrenciaStatus.Pausada;
            }
        }

        /// <summary>
        /// Pausa a recorrência
        /// </summary>
        public void Pausar()
        {
            if (Status == RecorrenciaStatus.Ativa)
            {
                Status = RecorrenciaStatus.Pausada;
            }
        }

        /// <summary>
        /// Retoma a recorrência
        /// </summary>
        public void Retomar()
        {
            if (Status == RecorrenciaStatus.Pausada)
            {
                Status = RecorrenciaStatus.Ativa;
                ExecucoesFalhadas = 0; // Reset contador de falhas
                MotivoUltimaFalha = null;
            }
        }

        /// <summary>
        /// Cancela a recorrência
        /// </summary>
        public void Cancelar()
        {
            Status = RecorrenciaStatus.Cancelada;
        }
        #endregion
    }

    // Manter classe antiga para compatibilidade
    [Obsolete("Use TransferenciaRecorrente")]
    public class TransferenciasRecorrentes : BaseEntidade
    {
        public string IdTransferencia { get; set; } = "";
        public int QtdDiasEntreTransferencias { get; set; }
        public DateTime? DataFinal { get; set; }
    }
}
