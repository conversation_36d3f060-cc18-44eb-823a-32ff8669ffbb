import 'package:flutter/material.dart';
import '../data/profile_service.dart';

class FinancialProfileQuestionnaireScreen extends StatefulWidget {
  const FinancialProfileQuestionnaireScreen({super.key});

  @override
  State<FinancialProfileQuestionnaireScreen> createState() =>
      _FinancialProfileQuestionnaireScreenState();
}

class _FinancialProfileQuestionnaireScreenState
    extends State<FinancialProfileQuestionnaireScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  final Map<String, dynamic> _answers = {};

  final List<QuestionData> _questions = [
    QuestionData(
      id: 'age_range',
      title: 'Qual sua faixa etária?',
      type: QuestionType.singleChoice,
      options: [
        '18-25 anos',
        '26-35 anos',
        '36-45 anos',
        '46-55 anos',
        '56+ anos',
      ],
    ),
    QuestionData(
      id: 'income_range',
      title: 'Qual sua faixa de renda mensal?',
      type: QuestionType.singleChoice,
      options: [
        'Até R\$ 2.000',
        'R\$ 2.001 - R\$ 5.000',
        'R\$ 5.001 - R\$ 10.000',
        'R\$ 10.001 - R\$ 20.000',
        'Acima de R\$ 20.000',
      ],
    ),
    QuestionData(
      id: 'financial_goals',
      title: 'Quais são seus principais objetivos financeiros?',
      type: QuestionType.multipleChoice,
      options: [
        'Criar uma reserva de emergência',
        'Comprar casa própria',
        'Investir para aposentadoria',
        'Quitar dívidas',
        'Viajar',
        'Empreender',
      ],
    ),
    QuestionData(
      id: 'risk_tolerance',
      title: 'Como você se sente em relação a riscos financeiros?',
      type: QuestionType.singleChoice,
      options: [
        'Prefiro segurança, mesmo com menor retorno',
        'Aceito riscos moderados por melhores retornos',
        'Estou disposto a correr riscos altos por retornos maiores',
      ],
    ),
    QuestionData(
      id: 'spending_habits',
      title: 'Como você descreveria seus hábitos de gastos?',
      type: QuestionType.singleChoice,
      options: [
        'Muito controlado, sempre planejo',
        'Moderadamente controlado',
        'Gasto por impulso frequentemente',
        'Tenho dificuldade para controlar gastos',
      ],
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Perfil Financeiro',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
      ),
      body: Column(
        children: [
          // Progress indicator
          Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Pergunta ${_currentPage + 1} de ${_questions.length}',
                      style: const TextStyle(color: Colors.white70),
                    ),
                    Text(
                      '${((_currentPage + 1) / _questions.length * 100).round()}%',
                      style: const TextStyle(color: Colors.white70),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                LinearProgressIndicator(
                  value: (_currentPage + 1) / _questions.length,
                  backgroundColor: Colors.white24,
                  valueColor: const AlwaysStoppedAnimation<Color>(
                    Color(0xFF4CAF50),
                  ),
                ),
              ],
            ),
          ),
          // Questions
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentPage = index;
                });
              },
              itemCount: _questions.length,
              itemBuilder: (context, index) {
                return _buildQuestionPage(_questions[index]);
              },
            ),
          ),
          // Navigation buttons
          Container(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                if (_currentPage > 0)
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _previousQuestion,
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: Colors.white),
                        padding: const EdgeInsets.symmetric(vertical: 15),
                      ),
                      child: const Text(
                        'Anterior',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                if (_currentPage > 0) const SizedBox(width: 15),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _canProceed() ? _nextQuestion : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF4CAF50),
                      padding: const EdgeInsets.symmetric(vertical: 15),
                    ),
                    child: Text(
                      _currentPage == _questions.length - 1
                          ? 'Finalizar'
                          : 'Próxima',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionPage(QuestionData question) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            question.title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 30),
          Expanded(
            child: ListView.builder(
              itemCount: question.options.length,
              itemBuilder: (context, index) {
                final option = question.options[index];
                final isSelected = _isOptionSelected(question.id, option);

                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: InkWell(
                    onTap: () =>
                        _selectOption(question.id, option, question.type),
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? const Color(0xFF4CAF50)
                            : Colors.white10,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isSelected
                              ? const Color(0xFF4CAF50)
                              : Colors.white24,
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            question.type == QuestionType.multipleChoice
                                ? (isSelected
                                      ? Icons.check_box
                                      : Icons.check_box_outline_blank)
                                : (isSelected
                                      ? Icons.radio_button_checked
                                      : Icons.radio_button_unchecked),
                            color: isSelected ? Colors.white : Colors.white70,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              option,
                              style: TextStyle(
                                color: isSelected
                                    ? Colors.white
                                    : Colors.white70,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  bool _isOptionSelected(String questionId, String option) {
    final answer = _answers[questionId];
    if (answer is List) {
      return answer.contains(option);
    }
    return answer == option;
  }

  void _selectOption(String questionId, String option, QuestionType type) {
    setState(() {
      if (type == QuestionType.multipleChoice) {
        final currentAnswers =
            _answers[questionId] as List<String>? ?? <String>[];
        if (currentAnswers.contains(option)) {
          currentAnswers.remove(option);
        } else {
          currentAnswers.add(option);
        }
        _answers[questionId] = currentAnswers;
      } else {
        _answers[questionId] = option;
      }
    });
  }

  bool _canProceed() {
    final currentQuestion = _questions[_currentPage];
    final answer = _answers[currentQuestion.id];

    if (currentQuestion.type == QuestionType.multipleChoice) {
      return answer is List && answer.isNotEmpty;
    }
    return answer != null;
  }

  void _previousQuestion() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _nextQuestion() {
    if (_currentPage < _questions.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _finishQuestionnaire();
    }
  }

  void _finishQuestionnaire() async {
    try {
      // Salvar perfil financeiro no backend
      final success = await ProfileService.saveFinancialProfile(_answers);

      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            backgroundColor: const Color(0xFF16213E),
            title: Text(
              success ? 'Perfil Criado!' : 'Atenção',
              style: const TextStyle(color: Colors.white),
            ),
            content: Text(
              success
                  ? 'Seu perfil financeiro foi criado com sucesso. Agora você receberá recomendações personalizadas.'
                  : 'Houve um problema ao salvar seu perfil, mas suas respostas foram registradas.',
              style: const TextStyle(color: Colors.white70),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(); // Close dialog
                  Navigator.of(context).pop(); // Return to dashboard
                },
                child: const Text(
                  'Continuar',
                  style: TextStyle(color: Color(0xFF4CAF50)),
                ),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            backgroundColor: const Color(0xFF16213E),
            title: const Text('Erro', style: TextStyle(color: Colors.white)),
            content: const Text(
              'Ocorreu um erro ao salvar seu perfil. Tente novamente mais tarde.',
              style: TextStyle(color: Colors.white70),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(); // Close dialog
                },
                child: const Text(
                  'OK',
                  style: TextStyle(color: Color(0xFF4CAF50)),
                ),
              ),
            ],
          ),
        );
      }
    }
  }
}

enum QuestionType { singleChoice, multipleChoice }

class QuestionData {
  final String id;
  final String title;
  final QuestionType type;
  final List<String> options;

  QuestionData({
    required this.id,
    required this.title,
    required this.type,
    required this.options,
  });
}
